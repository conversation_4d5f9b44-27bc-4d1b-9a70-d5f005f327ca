/**
 * Valida e sanitiza URLs de redirecionamento para prevenir open redirect attacks
 */

const ALLOWED_ORIGINS = [
  'https://mvp-sx.vercel.app',
  process.env.NEXT_PUBLIC_APP_URL,
  'http://localhost:3000', // apenas para desenvolvimento
].filter(Boolean) as string[];

const ALLOWED_PATHS = [
  '/dashboard',
  '/admin',
  '/perfil',
  '/pontos-turisticos',
  '/recompensas',
  '/veiculos',
  '/conquistas',
  '/relatorios',
  '/usuarios',
  '/configuracoes',
  '/resgates',
];

/**
 * Valida se uma URL de redirecionamento é segura
 */
export function isValidRedirectUrl(url: string): boolean {
  try {
    // URLs relativas são permitidas se começarem com /
    if (url.startsWith('/')) {
      // Não permitir urls com // que podem ser interpretadas como URLs absolutas
      if (url.startsWith('//')) {
        return false;
      }
      
      // Verificar se o caminho está na lista de caminhos permitidos
      const path = url.split('?')[0]; // Remove query params
      return ALLOWED_PATHS.some(allowedPath => 
        path === allowedPath || path.startsWith(allowedPath + '/')
      );
    }

    // Para URLs absolutas, verificar origem
    const urlObj = new URL(url);
    return ALLOWED_ORIGINS.some(origin => {
      try {
        const originObj = new URL(origin);
        return urlObj.origin === originObj.origin;
      } catch {
        return false;
      }
    });
  } catch {
    return false;
  }
}

/**
 * Sanitiza uma URL de redirecionamento, retornando uma URL segura
 */
export function sanitizeRedirectUrl(url: string | null, fallback = '/dashboard'): string {
  if (!url) {
    return fallback;
  }

  if (isValidRedirectUrl(url)) {
    return url;
  }

  return fallback;
}

/**
 * Cria uma URL de login com redirecionamento seguro
 */
export function createLoginUrl(redirectTo?: string): string {
  const baseUrl = '/login';
  
  if (!redirectTo) {
    return baseUrl;
  }

  const sanitizedRedirect = sanitizeRedirectUrl(redirectTo);
  
  // Só adicionar o parâmetro se for diferente do fallback padrão
  if (sanitizedRedirect !== '/dashboard') {
    return `${baseUrl}?redirectTo=${encodeURIComponent(sanitizedRedirect)}`;
  }

  return baseUrl;
}