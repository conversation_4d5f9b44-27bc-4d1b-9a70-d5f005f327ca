import { createClient } from '@/utils/supabase/client';
import { Profile } from '@/types';

export async function makeUserAdmin(userId: string): Promise<{ error?: string }> {
  const supabase = createClient();
  
  try {
    const { error } = await supabase
      .from('profiles')
      .update({ role: 'admin' })
      .eq('id', userId);
    
    if (error) {
      console.error('Error making user admin:', error);
      return { error: 'Erro ao definir usuário como admin' };
    }
    
    return {};
  } catch (error) {
    console.error('Error making user admin:', error);
    return { error: 'Erro inesperado' };
  }
}

export async function makeUserRegular(userId: string): Promise<{ error?: string }> {
  const supabase = createClient();
  
  try {
    const { error } = await supabase
      .from('profiles')
      .update({ role: 'user' })
      .eq('id', userId);
    
    if (error) {
      console.error('Error making user regular:', error);
      return { error: 'Erro ao definir usuário como regular' };
    }
    
    return {};
  } catch (error) {
    console.error('Error making user regular:', error);
    return { error: 'Erro inesperado' };
  }
}

export function isAdmin(profile: Profile | null): boolean {
  return profile?.role === 'admin';
}

export async function getCurrentUserRole(): Promise<{ role: string | null; error?: string }> {
  const supabase = createClient();
  
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return { role: null, error: 'Usuário não autenticado' };
    }
    
    const { data, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (error) {
      console.error('Error fetching user role:', error);
      return { role: null, error: 'Erro ao buscar role do usuário' };
    }
    
    return { role: data.role };
  } catch (error) {
    console.error('Error fetching user role:', error);
    return { role: null, error: 'Erro inesperado' };
  }
}