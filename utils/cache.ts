// Utilitários de cache para otimização do Next.js

// Configurações de cache desabilitadas para todos os tipos de dados
export const cacheConfig = {
  // Dados estáticos - sem cache
  static: {
    cache: 'no-store' as const,
    tags: ['static-data']
  },
  
  // Dados públicos - sem cache
  public: {
    cache: 'no-store' as const,
    tags: ['public-data', 'vehicles', 'tourist-spots']
  },
  
  // Dados dinâmicos - sem cache
  dynamic: {
    cache: 'no-store' as const,
    tags: ['dynamic-data', 'availability']
  },
  
  // Dados administrativos - sem cache
  admin: {
    cache: 'no-store' as const,
    tags: ['admin-data']
  },
  
  // Dados do usuário - sem cache
  user: {
    cache: 'no-store' as const,
    tags: ['user-data']
  }
};

// Função para fazer fetch sem cache
export async function fetchWithCache(
  url: string, 
  type: keyof typeof cacheConfig,
  options: RequestInit = {}
): Promise<Response> {
  const config = cacheConfig[type];
  
  const fetchOptions: RequestInit = {
    ...options,
    cache: config.cache,
  };

  return fetch(url, fetchOptions);
}

// Função para invalidar cache por tags
export function getCacheTags(type: keyof typeof cacheConfig): string[] {
  return cacheConfig[type].tags || [];
}

// Headers para desabilitar cache no cliente para áreas administrativas
export const adminHeaders = {
  'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0',
  'Pragma': 'no-cache',
  'Expires': '0'
};

// Headers para desabilitar cache de dados públicos
export const publicHeaders = {
  'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0',
  'Pragma': 'no-cache',
  'Expires': '0'
};