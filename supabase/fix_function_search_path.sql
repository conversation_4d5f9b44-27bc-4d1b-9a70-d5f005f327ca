
-- This script fixes the "Function Search Path Mutable" security warnings
-- by explicitly setting the search_path for each affected function.

-- Function to update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql
SET search_path = 'public';

-- Function to update user points
CREATE OR REPLACE FUNCTION update_user_points(
    p_user_id UUID,
    p_points_to_add INTEGER,
    p_trans_type transaction_type,
    p_trans_source transaction_source,
    p_trans_description TEXT,
    p_trans_metadata JSONB DEFAULT NULL
)
RETURNS void AS $$
DECLARE
    v_current_points INTEGER;
    v_new_points INTEGER;
    v_new_level TEXT;
BEGIN
    -- Obter pontos atuais
    SELECT points INTO v_current_points
    FROM profiles
    WHERE id = p_user_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Usuário % não encontrado', p_user_id;
    END IF;

    -- Calcular novos pontos
    v_new_points := v_current_points + p_points_to_add;

    -- Garantir que pontos não fiquem negativos
    IF v_new_points < 0 THEN
        v_new_points := 0;
    END IF;

    -- Determinar novo nível baseado nos pontos
    SELECT name INTO v_new_level
    FROM levels
    WHERE min_points <= v_new_points
    ORDER BY min_points DESC
    LIMIT 1;

    -- Atualizar perfil
    UPDATE profiles
    SET
        points = v_new_points,
        level = COALESCE(v_new_level, 'Bronze'),
        total_points_earned = CASE
            WHEN p_trans_type = 'earned' THEN total_points_earned + p_points_to_add
            ELSE total_points_earned
        END,
        updated_at = NOW()
    WHERE id = p_user_id;

    -- Registrar transação
    INSERT INTO point_transactions (user_id, points, type, source, description, metadata)
    VALUES (p_user_id, p_points_to_add, p_trans_type, p_trans_source, p_trans_description, p_trans_metadata);
END;
$$ LANGUAGE plpgsql
SET search_path = 'public';

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    user_email TEXT;
    user_name TEXT;
BEGIN
    -- Extrair dados do usuário
    user_email := COALESCE(NEW.email, '');
    user_name := COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name', '');

    -- Inserir perfil com tratamento de erro
    BEGIN
        INSERT INTO public.profiles (id, email, full_name, points, level, total_points_earned)
        VALUES (
            NEW.id,
            user_email,
            user_name,
            100, -- Bônus de boas-vindas
            'Bronze',
            100
        );

        -- Registrar transação de bônus
        INSERT INTO public.point_transactions (user_id, points, type, source, description)
        VALUES (NEW.id, 100, 'bonus', 'admin', 'Bônus de boas-vindas');

    EXCEPTION
        WHEN unique_violation THEN
            -- Se perfil já existe, apenas atualizar email se necessário
            UPDATE public.profiles
            SET email = user_email, updated_at = NOW()
            WHERE id = NEW.id;
        WHEN OTHERS THEN
            -- Log erro mas não falhar o trigger
            RAISE WARNING 'Erro ao criar perfil para usuário %: %', NEW.id, SQLERRM;
    END;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = 'public';

-- Helper function to check for admin role
CREATE OR REPLACE FUNCTION is_admin()
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM auth.users
        WHERE auth.users.id = auth.uid()
        AND (
            auth.users.raw_user_meta_data->>'role' = 'admin'
            OR auth.users.email = '<EMAIL>'
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = 'public';
