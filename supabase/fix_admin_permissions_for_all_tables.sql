
-- Grant full access to admins for all managed tables
-- and appropriate read access to other users.

-- Drop existing policies on vehicles to avoid conflicts
DROP POLICY IF EXISTS "Admins can manage vehicles" ON public.vehicles;
DROP POLICY IF EXISTS "All users can view vehicles" ON public.vehicles;
DROP POLICY IF EXISTS "Admins can create vehicles" ON public.vehicles;

-- Achievements
CREATE POLICY "Admins can manage achievements" ON public.achievements FOR ALL
    USING (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'))
    WITH CHECK (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'));
CREATE POLICY "Authenticated users can view achievements" ON public.achievements FOR SELECT
    USING (auth.role() = 'authenticated');

-- App Settings (Admin only)
CREATE POLICY "Admins can manage app_settings" ON public.app_settings FOR ALL
    USING (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'))
    WITH CHECK (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'));

-- Levels
CREATE POLICY "Admins can manage levels" ON public.levels FOR ALL
    USING (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'))
    WITH CHECK (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'));
CREATE POLICY "Authenticated users can view levels" ON public.levels FOR SELECT
    USING (auth.role() = 'authenticated');

-- Tourist Spots
CREATE POLICY "Admins can manage tourist_spots" ON public.tourist_spots FOR ALL
    USING (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'))
    WITH CHECK (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'));
CREATE POLICY "Authenticated users can view tourist_spots" ON public.tourist_spots FOR SELECT
    USING (auth.role() = 'authenticated');

-- Rewards
CREATE POLICY "Admins can manage rewards" ON public.rewards FOR ALL
    USING (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'))
    WITH CHECK (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'));
CREATE POLICY "Authenticated users can view rewards" ON public.rewards FOR SELECT
    USING (auth.role() = 'authenticated');

-- Vehicles
CREATE POLICY "Admins can manage vehicles" ON public.vehicles FOR ALL
    USING (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'))
    WITH CHECK (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'));
CREATE POLICY "Authenticated users can view vehicles" ON public.vehicles FOR SELECT
    USING (auth.role() = 'authenticated');
