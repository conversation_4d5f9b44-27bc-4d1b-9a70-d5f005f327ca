-- SX Locadora - Schema Limpo e Completo
-- Baseado nas melhores práticas do Supabase
-- Sem problemas de recursão infinita em RLS

-- LIMPAR TUDO ANTES DE COMEÇAR
DROP SCHEMA IF EXISTS public CASCADE;
CREATE SCHEMA public;

-- Conceder permiss<PERSON><PERSON> necessárias
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO postgres, service_role;

-- Habilitar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" SCHEMA extensions;
CREATE EXTENSION IF NOT EXISTS "pgcrypto" SCHEMA extensions;

-- ============================================================================
-- TIPOS CUSTOMIZADOS
-- ============================================================================

CREATE TYPE user_role AS ENUM ('user', 'admin');
CREATE TYPE transaction_type AS ENUM ('earned', 'redeemed', 'bonus', 'penalty');
CREATE TYPE transaction_source AS ENUM ('check_in', 'achievement', 'admin', 'referral', 'rental', 'social');
CREATE TYPE achievement_type AS ENUM ('check_in', 'rental', 'points', 'social', 'special', 'streak', 'explorer', 'champion');
CREATE TYPE reward_type AS ENUM ('discount', 'free_rental', 'merchandise', 'experience', 'partner_benefit');
CREATE TYPE redemption_status AS ENUM ('pending', 'approved', 'used', 'expired');
CREATE TYPE vehicle_type AS ENUM ('scooter', 'bike', 'e-bike', 'skateboard');

-- ============================================================================
-- TABELA PROFILES (SEM RECURSÃO)
-- ============================================================================

-- Tabela de perfis que estende auth.users
-- SEGUINDO PADRÃO RECOMENDADO: id referencia auth.users diretamente
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    phone TEXT,
    role user_role DEFAULT 'user'::user_role,
    avatar_url TEXT,
    points INTEGER DEFAULT 0 CHECK (points >= 0),
    level TEXT DEFAULT 'Bronze',
    total_points_earned INTEGER DEFAULT 0 CHECK (total_points_earned >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- DEMAIS TABELAS DO SISTEMA
-- ============================================================================

-- Tabela de níveis para gamificação
CREATE TABLE levels (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    min_points INTEGER NOT NULL CHECK (min_points >= 0),
    color TEXT NOT NULL,
    icon TEXT,
    rewards JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de conquistas
CREATE TABLE achievements (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    icon TEXT NOT NULL,
    type achievement_type NOT NULL,
    condition JSONB NOT NULL,
    reward_points INTEGER DEFAULT 0 CHECK (reward_points >= 0),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de conquistas dos usuários
CREATE TABLE user_achievements (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    achievement_id UUID REFERENCES achievements(id) ON DELETE CASCADE,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, achievement_id)
);

-- Pontos turísticos para check-ins
CREATE TABLE tourist_spots (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    points_reward INTEGER DEFAULT 10 CHECK (points_reward >= 0),
    image_url TEXT,
    address TEXT,
    is_active BOOLEAN DEFAULT true,
    check_in_radius INTEGER DEFAULT 100 CHECK (check_in_radius > 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Check-ins dos usuários
CREATE TABLE check_ins (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    tourist_spot_id UUID REFERENCES tourist_spots(id) ON DELETE CASCADE,
    points_earned INTEGER NOT NULL CHECK (points_earned >= 0),
    check_in_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transações de pontos para auditoria
CREATE TABLE point_transactions (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    points INTEGER NOT NULL,
    type transaction_type NOT NULL,
    source transaction_source NOT NULL,
    description TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recompensas disponíveis
CREATE TABLE rewards (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    cost_points INTEGER NOT NULL CHECK (cost_points > 0),
    type reward_type NOT NULL,
    value TEXT NOT NULL,
    image_url TEXT,
    is_active BOOLEAN DEFAULT true,
    stock INTEGER,
    valid_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Resgates de recompensas
CREATE TABLE reward_redemptions (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    reward_id UUID REFERENCES rewards(id) ON DELETE CASCADE,
    points_spent INTEGER NOT NULL CHECK (points_spent > 0),
    status redemption_status DEFAULT 'pending'::redemption_status,
    redeemed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Veículos disponíveis para aluguel
CREATE TABLE vehicles (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    type vehicle_type NOT NULL,
    description TEXT,
    image_url TEXT,
    hourly_price DECIMAL(10, 2) NOT NULL CHECK (hourly_price > 0),
    daily_price DECIMAL(10, 2),
    is_available BOOLEAN DEFAULT true,
    location TEXT,
    features JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Configurações do app
CREATE TABLE app_settings (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    key TEXT UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- ÍNDICES PARA PERFORMANCE
-- ============================================================================

CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_profiles_points ON profiles(points DESC);

CREATE INDEX idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_achievement_id ON user_achievements(achievement_id);

CREATE INDEX idx_check_ins_user_id ON check_ins(user_id);
CREATE INDEX idx_check_ins_tourist_spot_id ON check_ins(tourist_spot_id);
CREATE INDEX idx_check_ins_time ON check_ins(check_in_time);

CREATE INDEX idx_point_transactions_user_id ON point_transactions(user_id);
CREATE INDEX idx_point_transactions_created_at ON point_transactions(created_at DESC);

CREATE INDEX idx_reward_redemptions_user_id ON reward_redemptions(user_id);
CREATE INDEX idx_reward_redemptions_status ON reward_redemptions(status);

CREATE INDEX idx_tourist_spots_location ON tourist_spots(latitude, longitude);
CREATE INDEX idx_vehicles_type ON vehicles(type);
CREATE INDEX idx_vehicles_available ON vehicles(is_available);

-- ============================================================================
-- TRIGGERS DE UPDATED_AT
-- ============================================================================

-- Função para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar triggers
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_levels_updated_at 
    BEFORE UPDATE ON levels 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_achievements_updated_at 
    BEFORE UPDATE ON achievements 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tourist_spots_updated_at 
    BEFORE UPDATE ON tourist_spots 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rewards_updated_at 
    BEFORE UPDATE ON rewards 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vehicles_updated_at 
    BEFORE UPDATE ON vehicles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_app_settings_updated_at 
    BEFORE UPDATE ON app_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- FUNÇÕES DE NEGÓCIO
-- ============================================================================

-- Função para atualizar pontos do usuário
CREATE OR REPLACE FUNCTION update_user_points(
    p_user_id UUID,
    p_points_to_add INTEGER,
    p_trans_type transaction_type,
    p_trans_source transaction_source,
    p_trans_description TEXT,
    p_trans_metadata JSONB DEFAULT NULL
)
RETURNS void AS $$
DECLARE
    v_current_points INTEGER;
    v_new_points INTEGER;
    v_new_level TEXT;
BEGIN
    -- Obter pontos atuais
    SELECT points INTO v_current_points 
    FROM profiles 
    WHERE id = p_user_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Usuário % não encontrado', p_user_id;
    END IF;
    
    -- Calcular novos pontos
    v_new_points := v_current_points + p_points_to_add;
    
    -- Garantir que pontos não fiquem negativos
    IF v_new_points < 0 THEN
        v_new_points := 0;
    END IF;
    
    -- Determinar novo nível baseado nos pontos
    SELECT name INTO v_new_level 
    FROM levels 
    WHERE min_points <= v_new_points 
    ORDER BY min_points DESC 
    LIMIT 1;
    
    -- Atualizar perfil
    UPDATE profiles 
    SET 
        points = v_new_points,
        level = COALESCE(v_new_level, 'Bronze'),
        total_points_earned = CASE 
            WHEN p_trans_type = 'earned' THEN total_points_earned + p_points_to_add 
            ELSE total_points_earned 
        END,
        updated_at = NOW()
    WHERE id = p_user_id;
    
    -- Registrar transação
    INSERT INTO point_transactions (user_id, points, type, source, description, metadata)
    VALUES (p_user_id, p_points_to_add, p_trans_type, p_trans_source, p_trans_description, p_trans_metadata);
END;
$$ LANGUAGE plpgsql;

-- Função para criar perfil de novo usuário
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (id, email, full_name, points, level, total_points_earned)
    VALUES (
        NEW.id, 
        NEW.email, 
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        100, -- Bônus de boas-vindas
        'Bronze',
        100
    );
    
    -- Registrar transação de bônus
    INSERT INTO point_transactions (user_id, points, type, source, description)
    VALUES (NEW.id, 100, 'bonus', 'admin', 'Bônus de boas-vindas');
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger para novos usuários
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- ============================================================================
-- ROW LEVEL SECURITY (SEM RECURSÃO)
-- ============================================================================

-- Habilitar RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE check_ins ENABLE ROW LEVEL SECURITY;
ALTER TABLE point_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE reward_redemptions ENABLE ROW LEVEL SECURITY;

-- POLÍTICAS PARA PROFILES (SEM RECURSÃO INFINITA)
-- SELECT: Todos podem ver todos os perfis
CREATE POLICY "profiles_select_policy" ON profiles
    FOR SELECT 
    USING (true);

-- INSERT: Usuários autenticados podem inserir apenas seu próprio perfil
CREATE POLICY "profiles_insert_policy" ON profiles
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid() = id);

-- UPDATE: Usuários podem atualizar apenas seu próprio perfil OU são admin
CREATE POLICY "profiles_update_policy" ON profiles
    FOR UPDATE 
    TO authenticated
    USING (
        auth.uid() = id 
        OR 
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_user_meta_data->>'role' = 'admin' 
                OR auth.users.email = '<EMAIL>'
            )
        )
    )
    WITH CHECK (
        auth.uid() = id 
        OR 
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_user_meta_data->>'role' = 'admin' 
                OR auth.users.email = '<EMAIL>'
            )
        )
    );

-- DELETE: Apenas admins podem deletar perfis
CREATE POLICY "profiles_delete_policy" ON profiles
    FOR DELETE 
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_user_meta_data->>'role' = 'admin' 
                OR auth.users.email = '<EMAIL>'
            )
        )
    );

-- POLÍTICAS PARA USER_ACHIEVEMENTS
CREATE POLICY "user_achievements_select_policy" ON user_achievements
    FOR SELECT 
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR 
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_user_meta_data->>'role' = 'admin' 
                OR auth.users.email = '<EMAIL>'
            )
        )
    );

CREATE POLICY "user_achievements_insert_policy" ON user_achievements
    FOR INSERT 
    TO authenticated
    WITH CHECK (
        auth.uid() = user_id 
        OR 
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_user_meta_data->>'role' = 'admin' 
                OR auth.users.email = '<EMAIL>'
            )
        )
    );

-- POLÍTICAS PARA CHECK_INS
CREATE POLICY "check_ins_select_policy" ON check_ins
    FOR SELECT 
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR 
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_user_meta_data->>'role' = 'admin' 
                OR auth.users.email = '<EMAIL>'
            )
        )
    );

CREATE POLICY "check_ins_insert_policy" ON check_ins
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- POLÍTICAS PARA POINT_TRANSACTIONS
CREATE POLICY "point_transactions_select_policy" ON point_transactions
    FOR SELECT 
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR 
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_user_meta_data->>'role' = 'admin' 
                OR auth.users.email = '<EMAIL>'
            )
        )
    );

CREATE POLICY "point_transactions_insert_policy" ON point_transactions
    FOR INSERT 
    TO authenticated
    WITH CHECK (
        auth.uid() = user_id 
        OR 
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_user_meta_data->>'role' = 'admin' 
                OR auth.users.email = '<EMAIL>'
            )
        )
    );

-- POLÍTICAS PARA REWARD_REDEMPTIONS
CREATE POLICY "reward_redemptions_select_policy" ON reward_redemptions
    FOR SELECT 
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR 
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_user_meta_data->>'role' = 'admin' 
                OR auth.users.email = '<EMAIL>'
            )
        )
    );

CREATE POLICY "reward_redemptions_insert_policy" ON reward_redemptions
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "reward_redemptions_update_policy" ON reward_redemptions
    FOR UPDATE 
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR 
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND (
                auth.users.raw_user_meta_data->>'role' = 'admin' 
                OR auth.users.email = '<EMAIL>'
            )
        )
    );

-- ============================================================================
-- TABELAS PÚBLICAS (SEM RLS)
-- ============================================================================

-- Conceder acesso de leitura para tabelas que devem ser públicas
GRANT SELECT ON levels TO anon, authenticated;
GRANT SELECT ON achievements TO anon, authenticated;
GRANT SELECT ON tourist_spots TO anon, authenticated;
GRANT SELECT ON rewards TO anon, authenticated;
GRANT SELECT ON vehicles TO anon, authenticated;
GRANT SELECT ON app_settings TO anon, authenticated;

-- ============================================================================
-- DADOS INICIAIS
-- ============================================================================

-- Inserir níveis padrão
INSERT INTO levels (name, description, min_points, color, icon, rewards) VALUES 
('Bronze', 'Nível inicial para novos usuários', 0, '#cd7f32', 'award', '{"welcome_bonus": 50}'),
('Silver', 'Para usuários com experiência básica', 500, '#c0c0c0', 'award', '{"discount": "5%", "bonus_points": 10}'),
('Gold', 'Para usuários experientes e ativos', 1500, '#ffd700', 'crown', '{"discount": "10%", "bonus_points": 20, "priority_support": true}'),
('Platinum', 'Para usuários premium dedicados', 5000, '#e5e4e2', 'gem', '{"discount": "15%", "bonus_points": 50, "free_rentals": 2}'),
('Diamond', 'Nível máximo para verdadeiros campeões', 15000, '#b9f2ff', 'diamond', '{"discount": "25%", "bonus_points": 100, "free_rentals": 5, "vip_access": true}');

-- Inserir configurações padrão do app
INSERT INTO app_settings (key, value, description) VALUES 
('whatsapp_number', '"5511999999999"', 'Número do WhatsApp para contato'),
('company_name', '"SX Locadora"', 'Nome da empresa'),
('points_per_checkin', '10', 'Pontos ganhos por check-in'),
('daily_points_limit', '100', 'Limite diário de pontos'),
('welcome_bonus', '100', 'Bônus de boas-vindas para novos usuários');

-- ============================================================================
-- PERMISSÕES FINAIS
-- ============================================================================

-- Conceder permissões necessárias
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO postgres, service_role;

GRANT SELECT, INSERT, UPDATE, DELETE ON profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_achievements TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON check_ins TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON point_transactions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON reward_redemptions TO authenticated;

-- Permitir uso de sequências
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated, anon;

-- ============================================================================
-- VERIFICAÇÕES FINAIS
-- ============================================================================

-- Verificar se tudo foi criado corretamente
SELECT 'Schema criado com sucesso!' as status;
SELECT COUNT(*) as total_tables FROM information_schema.tables WHERE table_schema = 'public';
SELECT COUNT(*) as total_policies FROM pg_policies WHERE schemaname = 'public';
SELECT COUNT(*) as total_triggers FROM pg_trigger WHERE tgname LIKE '%auth_user%';

COMMIT;