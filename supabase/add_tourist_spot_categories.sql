-- Add categories to tourist spots
-- Adiciona sistema de categorização para pontos turísticos

-- Create enum for tourist spot categories
DO $$ BEGIN
    CREATE TYPE tourist_spot_category AS ENUM ('tourist_spot', 'restaurant', 'rental_services');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add category column to tourist_spots table
ALTER TABLE tourist_spots 
ADD COLUMN IF NOT EXISTS category tourist_spot_category DEFAULT 'tourist_spot';

-- Update existing tourist spots to have the default category
UPDATE tourist_spots 
SET category = 'tourist_spot' 
WHERE category IS NULL;

-- Create index for better performance on category filtering
CREATE INDEX IF NOT EXISTS idx_tourist_spots_category ON tourist_spots(category);

-- Add category to the trigger update logic (if needed)
-- The existing updated_at trigger will still work

-- Comments for documentation
COMMENT ON COLUMN tourist_spots.category IS 'Categoria do ponto: tourist_spot (Ponto Turístico), restaurant (Restaurante), rental_services (Locação de Passeios e Veículos)';