-- Fix public access to vehicles
-- Allow anonymous users to view available vehicles on the landing page

-- Drop the current policy that only allows authenticated users
DROP POLICY IF EXISTS "Authenticated users can view vehicles" ON public.vehicles;
DROP POLICY IF EXISTS "vehicles_select_all" ON public.vehicles;
DROP POLICY IF EXISTS "All users can view vehicles" ON public.vehicles;

-- Create a new policy that allows everyone (including anonymous users) to view available vehicles
CREATE POLICY "Public can view available vehicles" ON public.vehicles
    FOR SELECT 
    USING (is_available = true);

-- Keep admin policies for managing vehicles
-- These should already exist but let's ensure they're correct
DO $$
BEGIN
    -- Drop existing admin policies
    DROP POLICY IF EXISTS "Admins can manage vehicles" ON public.vehicles;
    DROP POLICY IF EXISTS "vehicles_insert_admin" ON public.vehicles;
    DROP POLICY IF EXISTS "vehicles_update_admin" ON public.vehicles;
    DROP POLICY IF EXISTS "vehicles_delete_admin" ON public.vehicles;
    
    -- Create comprehensive admin policy
    CREATE POLICY "Admins can manage vehicles" ON public.vehicles
        FOR ALL 
        USING (public.is_admin()) 
        WITH CHECK (public.is_admin());
        
EXCEPTION
    WHEN OTHERS THEN
        -- If is_admin() function doesn't exist, use direct role check
        DROP POLICY IF EXISTS "Admins can manage vehicles" ON public.vehicles;
        
        CREATE POLICY "Admins can manage vehicles" ON public.vehicles
            FOR ALL 
            USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() 
                    AND role = 'admin'
                )
            ) 
            WITH CHECK (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() 
                    AND role = 'admin'
                )
            );
END $$;