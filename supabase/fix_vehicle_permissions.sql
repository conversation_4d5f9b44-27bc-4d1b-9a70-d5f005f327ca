-- Policies for vehicles table
-- 1. Allow admin users to perform all actions
CREATE POLICY "Admins can manage vehicles" ON public.vehicles
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- 2. Allow all users to view vehicles
CREATE POLICY "All users can view vehicles" ON public.vehicles
    FOR SELECT
    USING (true);
