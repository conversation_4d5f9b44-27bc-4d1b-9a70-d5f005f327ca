-- Adici<PERSON>r colunas de aprovação e notas na tabela reward_redemptions
-- Data: 01/08/2025

-- Adici<PERSON>r colunas se não existirem
DO $$ 
BEGIN
    -- Adicionar coluna approved_at
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'reward_redemptions' 
        AND column_name = 'approved_at'
    ) THEN
        ALTER TABLE reward_redemptions 
        ADD COLUMN approved_at TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Adicionar coluna approved_by (referencia admin que aprovou)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'reward_redemptions' 
        AND column_name = 'approved_by'
    ) THEN
        ALTER TABLE reward_redemptions 
        ADD COLUMN approved_by UUID REFERENCES profiles(id);
    END IF;

    -- Adicionar coluna notes (observações do admin)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'reward_redemptions' 
        AND column_name = 'notes'
    ) THEN
        ALTER TABLE reward_redemptions 
        ADD COLUMN notes TEXT;
    END IF;

    -- <PERSON><PERSON><PERSON>r coluna updated_at se não existir
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'reward_redemptions' 
        AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE reward_redemptions 
        ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
END $$;

-- Criar trigger para atualizar updated_at automaticamente
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'update_reward_redemptions_updated_at'
    ) THEN
        CREATE TRIGGER update_reward_redemptions_updated_at
            BEFORE UPDATE ON reward_redemptions
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_reward_redemptions_approved_by ON reward_redemptions(approved_by);
CREATE INDEX IF NOT EXISTS idx_reward_redemptions_approved_at ON reward_redemptions(approved_at);

-- Comentários para documentação
COMMENT ON COLUMN reward_redemptions.approved_at IS 'Data e hora da aprovação do resgate pelo administrador';
COMMENT ON COLUMN reward_redemptions.approved_by IS 'ID do administrador que aprovou o resgate';
COMMENT ON COLUMN reward_redemptions.notes IS 'Observações do administrador sobre o resgate';
COMMENT ON COLUMN reward_redemptions.updated_at IS 'Data e hora da última atualização do registro';