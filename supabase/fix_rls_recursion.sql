-- SX Locadora - Correção para Recursão Infinita em RLS Policies
-- Fix para: "infinite recursion detected in policy for relation 'profiles'"

-- Inicia transação
BEGIN;

-- 1. REMOVER TODAS AS POLÍTICAS RLS PROBLEMÁTICAS DA TABELA PROFILES
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can update any profile" ON profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;

-- 2. CRIAR FUNÇÃO AUXILIAR PARA VERIFICAR SE O USUÁRIO É ADMIN
-- Esta função evita a recursão ao usar uma consulta direta ao auth.users
CREATE OR REPLACE FUNCTION is_admin_user()
RETURNS BOOLEAN AS $$
BEGIN
    -- Verificar diretamente no raw_user_meta_data do auth.users
    -- ou usar uma abordagem que não cause recursão
    RETURN EXISTS (
        SELECT 1 
        FROM auth.users u 
        WHERE u.id = auth.uid() 
        AND (
            u.raw_user_meta_data->>'role' = 'admin' 
            OR u.email = '<EMAIL>'  -- Email admin padrão
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. CRIAR POLÍTICAS MAIS SIMPLES E SEM RECURSÃO

-- Política para SELECT: Todos podem ver todos os perfis
CREATE POLICY "profiles_select_policy" ON profiles
    FOR SELECT USING (true);

-- Política para INSERT: Usuários autenticados podem inserir apenas seu próprio perfil
CREATE POLICY "profiles_insert_policy" ON profiles
    FOR INSERT WITH CHECK (
        auth.uid() = id 
        AND auth.role() = 'authenticated'
    );

-- Política para UPDATE: Usuários podem atualizar apenas seu próprio perfil
CREATE POLICY "profiles_update_own_policy" ON profiles
    FOR UPDATE USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Política para UPDATE: Admins podem atualizar qualquer perfil (SEM RECURSÃO)
CREATE POLICY "profiles_update_admin_policy" ON profiles
    FOR UPDATE USING (is_admin_user())
    WITH CHECK (is_admin_user());

-- Política para DELETE: Apenas admins podem deletar perfis
CREATE POLICY "profiles_delete_admin_policy" ON profiles
    FOR DELETE USING (is_admin_user());

-- 4. VERIFICAR E CORRIGIR OUTRAS TABELAS COM POSSÍVEL RECURSÃO

-- Remover políticas antigas de user_achievements
DROP POLICY IF EXISTS "Users can view their own achievements" ON user_achievements;
DROP POLICY IF EXISTS "Admins can view all achievements" ON user_achievements;
DROP POLICY IF EXISTS "System can insert achievements" ON user_achievements;

-- Recriar políticas de user_achievements sem recursão
CREATE POLICY "user_achievements_select_policy" ON user_achievements
    FOR SELECT USING (
        auth.uid() = user_id 
        OR is_admin_user()
    );

CREATE POLICY "user_achievements_insert_policy" ON user_achievements
    FOR INSERT WITH CHECK (
        auth.uid() = user_id 
        OR is_admin_user()
    );

-- Remover políticas antigas de check_ins
DROP POLICY IF EXISTS "Users can view their own check-ins" ON check_ins;
DROP POLICY IF EXISTS "Users can insert their own check-ins" ON check_ins;
DROP POLICY IF EXISTS "Admins can view all check-ins" ON check_ins;

-- Recriar políticas de check_ins sem recursão
CREATE POLICY "check_ins_select_policy" ON check_ins
    FOR SELECT USING (
        auth.uid() = user_id 
        OR is_admin_user()
    );

CREATE POLICY "check_ins_insert_policy" ON check_ins
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Remover políticas antigas de point_transactions
DROP POLICY IF EXISTS "Users can view their own transactions" ON point_transactions;
DROP POLICY IF EXISTS "System can insert transactions" ON point_transactions;
DROP POLICY IF EXISTS "Admins can view all transactions" ON point_transactions;
DROP POLICY IF EXISTS "Users can view own transactions" ON point_transactions;
DROP POLICY IF EXISTS "Authenticated users can insert transactions" ON point_transactions;

-- Recriar políticas de point_transactions sem recursão
CREATE POLICY "point_transactions_select_policy" ON point_transactions
    FOR SELECT USING (
        auth.uid() = user_id 
        OR is_admin_user()
    );

CREATE POLICY "point_transactions_insert_policy" ON point_transactions
    FOR INSERT WITH CHECK (
        auth.uid() = user_id 
        OR is_admin_user()
    );

-- Remover políticas antigas de reward_redemptions
DROP POLICY IF EXISTS "Users can view their own redemptions" ON reward_redemptions;
DROP POLICY IF EXISTS "Users can insert their own redemptions" ON reward_redemptions;
DROP POLICY IF EXISTS "Admins can view and update all redemptions" ON reward_redemptions;

-- Recriar políticas de reward_redemptions sem recursão
CREATE POLICY "reward_redemptions_select_policy" ON reward_redemptions
    FOR SELECT USING (
        auth.uid() = user_id 
        OR is_admin_user()
    );

CREATE POLICY "reward_redemptions_insert_policy" ON reward_redemptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "reward_redemptions_update_policy" ON reward_redemptions
    FOR UPDATE USING (
        auth.uid() = user_id 
        OR is_admin_user()
    );

-- 5. TESTAR AS POLÍTICAS
-- Verificar se as políticas foram criadas corretamente
DO $$
BEGIN
    RAISE NOTICE 'Políticas RLS corrigidas!';
    RAISE NOTICE 'Número de políticas na tabela profiles: %', 
        (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'profiles');
    RAISE NOTICE 'Função is_admin_user criada: %',
        CASE WHEN EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'is_admin_user') 
        THEN 'SIM' ELSE 'NÃO' END;
END $$;

-- Commit da transação
COMMIT;