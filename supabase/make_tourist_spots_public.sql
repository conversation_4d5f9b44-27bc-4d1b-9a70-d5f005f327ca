-- Tornar a tabela tourist_spots completamente pública
-- <PERSON><PERSON><PERSON> acesso a usuários autenticados e não autenticados (anon)

-- <PERSON><PERSON><PERSON><PERSON> que RLS esteja habilitado
ALTER TABLE public.tourist_spots ENABLE ROW LEVEL SECURITY;

-- Remover políticas existentes que possam estar conflitantes
DROP POLICY IF EXISTS "Authenticated users can view tourist_spots" ON public.tourist_spots;
DROP POLICY IF EXISTS "tourist_spots_select_all" ON public.tourist_spots;
DROP POLICY IF EXISTS "Public can view tourist_spots" ON public.tourist_spots;

-- <PERSON><PERSON><PERSON> pol<PERSON><PERSON> para acesso público (anon e authenticated)
CREATE POLICY "Public can view tourist_spots" ON public.tourist_spots
    FOR SELECT 
    TO anon, authenticated
    USING (true);

-- <PERSON><PERSON><PERSON><PERSON> que os grants estão corretos
GRANT SELECT ON public.tourist_spots TO anon, authenticated;

-- Coment<PERSON>rio para documentar a mudança
COMMENT ON POLICY "Public can view tourist_spots" ON public.tourist_spots IS 
'Permite que usuários autenticados e não autenticados visualizem todos os pontos turísticos';