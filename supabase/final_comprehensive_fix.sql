-- This script provides a comprehensive and idempotent fix for all identified database issues.
-- It corrects permissions, fixes security warnings, and ensures it can be run multiple times without error.

-- Part 1: Fix Function Search Path Warnings
-- Securely redefine functions by setting a static search_path.

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SET search_path = 'public';

CREATE OR REPLACE FUNCTION public.update_user_points(
    p_user_id UUID,
    p_points_to_add INTEGER,
    p_trans_type transaction_type,
    p_trans_source transaction_source,
    p_trans_description TEXT,
    p_trans_metadata JSONB DEFAULT NULL
)
RETURNS void AS $$
DECLARE
    v_current_points INTEGER;
    v_new_points INTEGER;
    v_new_level TEXT;
BEGIN
    SELECT points INTO v_current_points FROM profiles WHERE id = p_user_id;
    IF NOT FOUND THEN RAISE EXCEPTION 'User % not found', p_user_id; END IF;

    v_new_points := v_current_points + p_points_to_add;
    IF v_new_points < 0 THEN v_new_points := 0; END IF;

    SELECT name INTO v_new_level FROM levels WHERE min_points <= v_new_points ORDER BY min_points DESC LIMIT 1;

    UPDATE profiles SET
        points = v_new_points,
        level = COALESCE(v_new_level, 'Bronze'),
        total_points_earned = CASE WHEN p_trans_type = 'earned' THEN total_points_earned + p_points_to_add ELSE total_points_earned END,
        updated_at = NOW()
    WHERE id = p_user_id;

    INSERT INTO point_transactions (user_id, points, type, source, description, metadata)
    VALUES (p_user_id, p_points_to_add, p_trans_type, p_trans_source, p_trans_description, p_trans_metadata);
END;
$$ LANGUAGE plpgsql SET search_path = 'public';

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name, points, level, total_points_earned)
    VALUES (NEW.id, NEW.email, COALESCE(NEW.raw_user_meta_data->>'full_name', ''), 100, 'Bronze', 100);

    INSERT INTO public.point_transactions (user_id, points, type, source, description)
    VALUES (NEW.id, 100, 'bonus', 'admin', 'Welcome Bonus');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = 'public';

-- Part 2: Correctly Identify Administrators
-- This function now correctly checks for the admin email '<EMAIL>'
-- or the 'admin' role in the profiles table.

CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS boolean AS $$
BEGIN
    RETURN (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
        )
        OR
        (SELECT auth.jwt()->>'email') = '<EMAIL>'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = 'public';

-- Part 3: Re-apply RLS Policies with the Correct Admin Check
-- This section is now fully idempotent.

-- Enable RLS on all necessary tables
ALTER TABLE public.achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.app_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.levels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tourist_spots ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rewards ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies to avoid conflicts
DROP POLICY IF EXISTS "Admins can manage vehicles" ON public.vehicles;
DROP POLICY IF EXISTS "Authenticated users can view vehicles" ON public.vehicles;
DROP POLICY IF EXISTS "Admins can manage achievements" ON public.achievements;
DROP POLICY IF EXISTS "Authenticated users can view achievements" ON public.achievements;
DROP POLICY IF EXISTS "Admins can manage app_settings" ON public.app_settings;
DROP POLICY IF EXISTS "Admins can manage levels" ON public.levels;
DROP POLICY IF EXISTS "Authenticated users can view levels" ON public.levels;
DROP POLICY IF EXISTS "Admins can manage tourist_spots" ON public.tourist_spots;
DROP POLICY IF EXISTS "Authenticated users can view tourist_spots" ON public.tourist_spots;
DROP POLICY IF EXISTS "Admins can manage rewards" ON public.rewards;
DROP POLICY IF EXISTS "Authenticated users can view rewards" ON public.rewards;

-- Apply new, correct policies with unique names
CREATE POLICY "Admins can manage vehicles" ON public.vehicles FOR ALL USING (public.is_admin()) WITH CHECK (public.is_admin());
CREATE POLICY "Authenticated users can view vehicles" ON public.vehicles FOR SELECT TO authenticated USING (true);

CREATE POLICY "Admins can manage achievements" ON public.achievements FOR ALL USING (public.is_admin()) WITH CHECK (public.is_admin());
CREATE POLICY "Authenticated users can view achievements" ON public.achievements FOR SELECT TO authenticated USING (true);

CREATE POLICY "Admins can manage app_settings" ON public.app_settings FOR ALL USING (public.is_admin()) WITH CHECK (public.is_admin());

CREATE POLICY "Admins can manage levels" ON public.levels FOR ALL USING (public.is_admin()) WITH CHECK (public.is_admin());
CREATE POLICY "Authenticated users can view levels" ON public.levels FOR SELECT TO authenticated USING (true);

CREATE POLICY "Admins can manage tourist_spots" ON public.tourist_spots FOR ALL USING (public.is_admin()) WITH CHECK (public.is_admin());
CREATE POLICY "Authenticated users can view tourist_spots" ON public.tourist_spots FOR SELECT TO authenticated USING (true);

CREATE POLICY "Admins can manage rewards" ON public.rewards FOR ALL USING (public.is_admin()) WITH CHECK (public.is_admin());
CREATE POLICY "Authenticated users can view rewards" ON public.rewards FOR SELECT TO authenticated USING (true);