
-- This script corrects permissions for tables that were missing RLS policies
-- for administrators, leading to "permission denied" errors.

-- Enable RLS for all tables that should be protected.
ALTER TABLE public.achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.app_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.levels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tourist_spots ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rewards ENABLE ROW LEVEL SECURITY;

-- Helper function to check for admin role, consistent with the existing schema.
-- This avoids repeating the logic in every policy.
CREATE OR REPLACE FUNCTION is_admin()
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM auth.users
        WHERE auth.users.id = auth.uid()
        AND (
            auth.users.raw_user_meta_data->>'role' = 'admin'
            OR auth.users.email = '<EMAIL>'
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;


-- Drop any conflicting old policies before creating new ones.
DROP POLICY IF EXISTS "Admins can manage vehicles" ON public.vehicles;
DROP POLICY IF EXISTS "Authenticated users can view vehicles" ON public.vehicles;
DROP POLICY IF EXISTS "Admins can manage achievements" ON public.achievements;
DROP POLICY IF EXISTS "Authenticated users can view achievements" ON public.achievements;
DROP POLICY IF EXISTS "Admins can manage app_settings" ON public.app_settings;
DROP POLICY IF EXISTS "Admins can manage levels" ON public.levels;
DROP POLICY IF EXISTS "Authenticated users can view levels" ON public.levels;
DROP POLICY IF EXISTS "Admins can manage tourist_spots" ON public.tourist_spots;
DROP POLICY IF EXISTS "Authenticated users can view tourist_spots" ON public.tourist_spots;
DROP POLICY IF EXISTS "Admins can manage rewards" ON public.rewards;
DROP POLICY IF EXISTS "Authenticated users can view rewards" ON public.rewards;


-- Create policies for VEHICLES
CREATE POLICY "Admins can manage vehicles" ON public.vehicles
    FOR ALL USING (is_admin()) WITH CHECK (is_admin());
CREATE POLICY "Authenticated users can view vehicles" ON public.vehicles
    FOR SELECT TO authenticated USING (true);

-- Create policies for ACHIEVEMENTS
CREATE POLICY "Admins can manage achievements" ON public.achievements
    FOR ALL USING (is_admin()) WITH CHECK (is_admin());
CREATE POLICY "Authenticated users can view achievements" ON public.achievements
    FOR SELECT TO authenticated USING (true);

-- Create policies for APP_SETTINGS (Admin only)
CREATE POLICY "Admins can manage app_settings" ON public.app_settings
    FOR ALL USING (is_admin()) WITH CHECK (is_admin());

-- Create policies for LEVELS
CREATE POLICY "Admins can manage levels" ON public.levels
    FOR ALL USING (is_admin()) WITH CHECK (is_admin());
CREATE POLICY "Authenticated users can view levels" ON public.levels
    FOR SELECT TO authenticated USING (true);

-- Create policies for TOURIST_SPOTS
CREATE POLICY "Admins can manage tourist_spots" ON public.tourist_spots
    FOR ALL USING (is_admin()) WITH CHECK (is_admin());
CREATE POLICY "Authenticated users can view tourist_spots" ON public.tourist_spots
    FOR SELECT TO authenticated USING (true);

-- Create policies for REWARDS
CREATE POLICY "Admins can manage rewards" ON public.rewards
    FOR ALL USING (is_admin()) WITH CHECK (is_admin());
CREATE POLICY "Authenticated users can view rewards" ON public.rewards
    FOR SELECT TO authenticated USING (true);
