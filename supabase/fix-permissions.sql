-- Fix para resolver permission denied for table users
-- <PERSON>ste script corrige as políticas RLS que estão causando problemas

BEGIN;

-- <PERSON><PERSON>, vamos remover as políticas problemáticas
DROP POLICY IF EXISTS "profiles_update_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_delete_policy" ON profiles;
DROP POLICY IF EXISTS "user_achievements_select_policy" ON user_achievements;
DROP POLICY IF EXISTS "user_achievements_insert_policy" ON user_achievements;
DROP POLICY IF EXISTS "check_ins_select_policy" ON check_ins;
DROP POLICY IF EXISTS "point_transactions_select_policy" ON point_transactions;
DROP POLICY IF EXISTS "point_transactions_insert_policy" ON point_transactions;
DROP POLICY IF EXISTS "reward_redemptions_select_policy" ON reward_redemptions;
DROP POLICY IF EXISTS "reward_redemptions_update_policy" ON reward_redemptions;

-- Criar função helper para verificar se usuário é admin
-- Usamos uma função segura que não acessa auth.users diretamente
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = user_id 
    AND profiles.role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- POLÍTICAS CORRIGIDAS PARA PROFILES
CREATE POLICY "profiles_update_policy" ON profiles
    FOR UPDATE 
    TO authenticated
    USING (
        auth.uid() = id 
        OR 
        is_admin(auth.uid())
    )
    WITH CHECK (
        auth.uid() = id 
        OR 
        is_admin(auth.uid())
    );

CREATE POLICY "profiles_delete_policy" ON profiles
    FOR DELETE 
    TO authenticated
    USING (is_admin(auth.uid()));

-- POLÍTICAS CORRIGIDAS PARA USER_ACHIEVEMENTS
CREATE POLICY "user_achievements_select_policy" ON user_achievements
    FOR SELECT 
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR 
        is_admin(auth.uid())
    );

CREATE POLICY "user_achievements_insert_policy" ON user_achievements
    FOR INSERT 
    TO authenticated
    WITH CHECK (
        auth.uid() = user_id 
        OR 
        is_admin(auth.uid())
    );

-- POLÍTICAS CORRIGIDAS PARA CHECK_INS
CREATE POLICY "check_ins_select_policy" ON check_ins
    FOR SELECT 
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR 
        is_admin(auth.uid())
    );

-- POLÍTICAS CORRIGIDAS PARA POINT_TRANSACTIONS
CREATE POLICY "point_transactions_select_policy" ON point_transactions
    FOR SELECT 
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR 
        is_admin(auth.uid())
    );

CREATE POLICY "point_transactions_insert_policy" ON point_transactions
    FOR INSERT 
    TO authenticated
    WITH CHECK (
        auth.uid() = user_id 
        OR 
        is_admin(auth.uid())
    );

-- POLÍTICAS CORRIGIDAS PARA REWARD_REDEMPTIONS
CREATE POLICY "reward_redemptions_select_policy" ON reward_redemptions
    FOR SELECT 
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR 
        is_admin(auth.uid())
    );

CREATE POLICY "reward_redemptions_update_policy" ON reward_redemptions
    FOR UPDATE 
    TO authenticated
    USING (
        auth.uid() = user_id 
        OR 
        is_admin(auth.uid())
    );

-- Corrigir a função handle_new_user para não depender de metadados do auth.users
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    user_email TEXT;
    user_name TEXT;
BEGIN
    -- Extrair dados do usuário de forma mais segura
    user_email := COALESCE(NEW.email, '');
    user_name := COALESCE(
        NEW.raw_user_meta_data->>'full_name', 
        NEW.raw_user_meta_data->>'name',
        split_part(NEW.email, '@', 1),
        'Usuário'
    );
    
    -- Inserir perfil com tratamento de erro
    BEGIN
        INSERT INTO public.profiles (id, email, full_name, points, level, total_points_earned, role)
        VALUES (
            NEW.id, 
            user_email, 
            user_name,
            100, -- Bônus de boas-vindas
            'Bronze',
            100,
            CASE 
                WHEN user_email = '<EMAIL>' THEN 'admin'::user_role
                ELSE 'user'::user_role
            END
        );
        
        -- Registrar transação de bônus
        INSERT INTO public.point_transactions (user_id, points, type, source, description)
        VALUES (NEW.id, 100, 'bonus', 'admin', 'Bônus de boas-vindas');
        
    EXCEPTION 
        WHEN unique_violation THEN
            -- Se perfil já existe, apenas atualizar email se necessário
            UPDATE public.profiles 
            SET 
                email = user_email, 
                updated_at = NOW(),
                role = CASE 
                    WHEN user_email = '<EMAIL>' THEN 'admin'::user_role
                    ELSE role
                END
            WHERE id = NEW.id;
        WHEN OTHERS THEN
            -- Log erro mas não falhar o trigger
            RAISE WARNING 'Erro ao criar perfil para usuário %: %', NEW.id, SQLERRM;
    END;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Garantir que temos dados básicos
INSERT INTO levels (name, description, min_points, color, icon, rewards) VALUES 
('Bronze', 'Nível inicial para novos usuários', 0, '#cd7f32', 'award', '{"welcome_bonus": 50}'),
('Silver', 'Para usuários com experiência básica', 500, '#c0c0c0', 'award', '{"discount": "5%", "bonus_points": 10}'),
('Gold', 'Para usuários experientes e ativos', 1500, '#ffd700', 'crown', '{"discount": "10%", "bonus_points": 20, "priority_support": true}'),
('Platinum', 'Para usuários premium dedicados', 5000, '#e5e4e2', 'gem', '{"discount": "15%", "bonus_points": 50, "free_rentals": 2}'),
('Diamond', 'Nível máximo para verdadeiros campeões', 15000, '#b9f2ff', 'diamond', '{"discount": "25%", "bonus_points": 100, "free_rentals": 5, "vip_access": true}')
ON CONFLICT (name) DO NOTHING;

-- Algumas conquistas básicas
INSERT INTO achievements (name, description, icon, type, condition, reward_points, is_active) VALUES
('Primeiro Check-in', 'Faça seu primeiro check-in em um ponto turístico', 'map-pin', 'check_in', '{"check_ins": 1}', 50, true),
('Explorador Iniciante', 'Visite 5 pontos turísticos diferentes', 'compass', 'explorer', '{"unique_spots": 5}', 100, true),
('Colecionador de Pontos', 'Acumule 1000 pontos totais', 'star', 'points', '{"total_points": 1000}', 200, true)
ON CONFLICT DO NOTHING;

-- Verificar se tudo foi aplicado
DO $$
BEGIN
    RAISE NOTICE 'Correções aplicadas com sucesso!';
    RAISE NOTICE 'Função is_admin criada: %', 
        CASE WHEN EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'is_admin') 
        THEN 'SIM' ELSE 'NÃO' END;
    RAISE NOTICE 'Políticas RLS atualizadas: %', 
        (SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public');
END $$;

COMMIT;