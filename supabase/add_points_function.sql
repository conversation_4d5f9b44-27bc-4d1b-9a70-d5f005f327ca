-- Função para adicionar pontos a um usuário
CREATE OR REPLACE FUNCTION add_points_to_user(
  user_id UUID,
  points INTEGER,
  description TEXT DEFAULT 'Pontos adicionados'
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Inserir transação de pontos
  INSERT INTO point_transactions (
    user_id,
    points,
    type,
    description,
    created_at
  ) VALUES (
    user_id,
    points,
    'bonus',
    description,
    NOW()
  );

  -- Atualizar total de pontos do usuário
  UPDATE profiles
  SET 
    current_points = current_points + points,
    total_points_earned = total_points_earned + points,
    updated_at = NOW()
  WHERE id = user_id;
END;
$$;