-- Adici<PERSON>r colunas faltantes às tabelas achievements e rewards
-- <PERSON>ste fix corrige os erros "Could not find the 'category' column" e "Could not find the 'current_redemptions' column"

-- Adicionar colunas na tabela achievements
ALTER TABLE achievements ADD COLUMN IF NOT EXISTS category TEXT DEFAULT 'geral';

-- <PERSON><PERSON><PERSON>r colunas na tabela rewards
ALTER TABLE rewards ADD COLUMN IF NOT EXISTS category TEXT DEFAULT 'desconto';
ALTER TABLE rewards ADD COLUMN IF NOT EXISTS current_redemptions INTEGER DEFAULT 0 CHECK (current_redemptions >= 0);
ALTER TABLE rewards ADD COLUMN IF NOT EXISTS discount_percentage INTEGER DEFAULT 0 CHECK (discount_percentage >= 0 AND discount_percentage <= 100);
ALTER TABLE rewards ADD COLUMN IF NOT EXISTS max_redemptions INTEGER;
ALTER TABLE rewards ADD COLUMN IF NOT EXISTS validity_days INTEGER DEFAULT 30 CHECK (validity_days > 0);

-- Criar índices para melhor performance nas consultas
CREATE INDEX IF NOT EXISTS idx_achievements_category ON achievements(category);
CREATE INDEX IF NOT EXISTS idx_rewards_category ON rewards(category);
CREATE INDEX IF NOT EXISTS idx_rewards_current_redemptions ON rewards(current_redemptions);
CREATE INDEX IF NOT EXISTS idx_rewards_max_redemptions ON rewards(max_redemptions);

-- Atualizar conquistas existentes com categorias baseadas no tipo
UPDATE achievements 
SET 
    category = CASE 
        WHEN type = 'check_in' THEN 'explorador'
        WHEN type = 'rental' THEN 'aluguel'
        WHEN type = 'points' THEN 'pontos'
        WHEN type = 'social' THEN 'social'
        WHEN type = 'special' THEN 'especial'
        WHEN type = 'streak' THEN 'sequencia'
        WHEN type = 'explorer' THEN 'explorador'
        WHEN type = 'champion' THEN 'campeao'
        ELSE 'geral'
    END
WHERE category = 'geral';

-- Atualizar recompensas existentes com categorias baseadas no tipo
UPDATE rewards 
SET category = CASE 
    WHEN type = 'discount' THEN 'desconto'
    WHEN type = 'free_rental' THEN 'aluguel-gratis'
    WHEN type = 'merchandise' THEN 'produtos'
    WHEN type = 'experience' THEN 'experiencia'
    WHEN type = 'partner_benefit' THEN 'parceiros'
    ELSE 'desconto'
END
WHERE category = 'desconto';