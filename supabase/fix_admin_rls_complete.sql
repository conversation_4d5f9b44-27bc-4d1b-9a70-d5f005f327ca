-- ============================================================================
-- FIX ADMIN RLS POLICIES - COMPLETE SOLUTION
-- ============================================================================
-- This script fixes all RLS policies for admin users to allow proper CRUD operations
-- on all tables, especially focusing on INSERT permissions for rewards table
-- ============================================================================

BEGIN;

-- ============================================================================
-- 1. CREATE OR REPLACE is_admin FUNCTION
-- ============================================================================
-- This function checks if the current user has admin role
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.is_admin() TO authenticated;

-- ============================================================================
-- 2. DROP ALL EXISTING POLICIES
-- ============================================================================
-- Drop all existing policies to start fresh

-- Profiles
DROP POLICY IF EXISTS "profiles_select_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete_policy" ON public.profiles;

-- Rewards
DROP POLICY IF EXISTS "Admins can manage rewards" ON public.rewards;
DROP POLICY IF EXISTS "Authenticated users can view rewards" ON public.rewards;
DROP POLICY IF EXISTS "rewards_select_policy" ON public.rewards;
DROP POLICY IF EXISTS "rewards_insert_policy" ON public.rewards;
DROP POLICY IF EXISTS "rewards_update_policy" ON public.rewards;
DROP POLICY IF EXISTS "rewards_delete_policy" ON public.rewards;

-- Vehicles
DROP POLICY IF EXISTS "Admins can manage vehicles" ON public.vehicles;
DROP POLICY IF EXISTS "Authenticated users can view vehicles" ON public.vehicles;
DROP POLICY IF EXISTS "vehicles_select_policy" ON public.vehicles;
DROP POLICY IF EXISTS "vehicles_insert_policy" ON public.vehicles;
DROP POLICY IF EXISTS "vehicles_update_policy" ON public.vehicles;
DROP POLICY IF EXISTS "vehicles_delete_policy" ON public.vehicles;

-- Achievements
DROP POLICY IF EXISTS "Admins can manage achievements" ON public.achievements;
DROP POLICY IF EXISTS "Authenticated users can view achievements" ON public.achievements;
DROP POLICY IF EXISTS "achievements_select_policy" ON public.achievements;
DROP POLICY IF EXISTS "achievements_insert_policy" ON public.achievements;
DROP POLICY IF EXISTS "achievements_update_policy" ON public.achievements;
DROP POLICY IF EXISTS "achievements_delete_policy" ON public.achievements;

-- Tourist Spots
DROP POLICY IF EXISTS "Admins can manage tourist_spots" ON public.tourist_spots;
DROP POLICY IF EXISTS "Authenticated users can view tourist_spots" ON public.tourist_spots;
DROP POLICY IF EXISTS "tourist_spots_select_policy" ON public.tourist_spots;
DROP POLICY IF EXISTS "tourist_spots_insert_policy" ON public.tourist_spots;
DROP POLICY IF EXISTS "tourist_spots_update_policy" ON public.tourist_spots;
DROP POLICY IF EXISTS "tourist_spots_delete_policy" ON public.tourist_spots;

-- Levels
DROP POLICY IF EXISTS "Admins can manage levels" ON public.levels;
DROP POLICY IF EXISTS "Authenticated users can view levels" ON public.levels;
DROP POLICY IF EXISTS "levels_select_policy" ON public.levels;
DROP POLICY IF EXISTS "levels_insert_policy" ON public.levels;
DROP POLICY IF EXISTS "levels_update_policy" ON public.levels;
DROP POLICY IF EXISTS "levels_delete_policy" ON public.levels;

-- App Settings
DROP POLICY IF EXISTS "Admins can manage app_settings" ON public.app_settings;
DROP POLICY IF EXISTS "app_settings_select_policy" ON public.app_settings;
DROP POLICY IF EXISTS "app_settings_insert_policy" ON public.app_settings;
DROP POLICY IF EXISTS "app_settings_update_policy" ON public.app_settings;
DROP POLICY IF EXISTS "app_settings_delete_policy" ON public.app_settings;

-- ============================================================================
-- 3. ENABLE RLS ON ALL TABLES
-- ============================================================================
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tourist_spots ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.levels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.app_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.check_ins ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.point_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reward_redemptions ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 4. CREATE POLICIES FOR PROFILES
-- ============================================================================
-- Everyone can view profiles
CREATE POLICY "profiles_select_all" ON public.profiles
  FOR SELECT TO authenticated
  USING (true);

-- Users can insert their own profile
CREATE POLICY "profiles_insert_own" ON public.profiles
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = id);

-- Users can update their own profile, admins can update any
CREATE POLICY "profiles_update_own_or_admin" ON public.profiles
  FOR UPDATE TO authenticated
  USING (auth.uid() = id OR public.is_admin())
  WITH CHECK (auth.uid() = id OR public.is_admin());

-- Only admins can delete profiles
CREATE POLICY "profiles_delete_admin" ON public.profiles
  FOR DELETE TO authenticated
  USING (public.is_admin());

-- ============================================================================
-- 5. CREATE POLICIES FOR REWARDS (CRITICAL FOR ADMIN CREATION)
-- ============================================================================
-- Everyone can view active rewards
CREATE POLICY "rewards_select_all" ON public.rewards
  FOR SELECT TO authenticated
  USING (true);

-- Only admins can insert rewards
CREATE POLICY "rewards_insert_admin" ON public.rewards
  FOR INSERT TO authenticated
  WITH CHECK (public.is_admin());

-- Only admins can update rewards
CREATE POLICY "rewards_update_admin" ON public.rewards
  FOR UPDATE TO authenticated
  USING (public.is_admin())
  WITH CHECK (public.is_admin());

-- Only admins can delete rewards
CREATE POLICY "rewards_delete_admin" ON public.rewards
  FOR DELETE TO authenticated
  USING (public.is_admin());

-- ============================================================================
-- 6. CREATE POLICIES FOR VEHICLES
-- ============================================================================
-- Everyone can view vehicles
CREATE POLICY "vehicles_select_all" ON public.vehicles
  FOR SELECT TO authenticated
  USING (true);

-- Only admins can insert vehicles
CREATE POLICY "vehicles_insert_admin" ON public.vehicles
  FOR INSERT TO authenticated
  WITH CHECK (public.is_admin());

-- Only admins can update vehicles
CREATE POLICY "vehicles_update_admin" ON public.vehicles
  FOR UPDATE TO authenticated
  USING (public.is_admin())
  WITH CHECK (public.is_admin());

-- Only admins can delete vehicles
CREATE POLICY "vehicles_delete_admin" ON public.vehicles
  FOR DELETE TO authenticated
  USING (public.is_admin());

-- ============================================================================
-- 7. CREATE POLICIES FOR ACHIEVEMENTS
-- ============================================================================
-- Everyone can view achievements
CREATE POLICY "achievements_select_all" ON public.achievements
  FOR SELECT TO authenticated
  USING (true);

-- Only admins can insert achievements
CREATE POLICY "achievements_insert_admin" ON public.achievements
  FOR INSERT TO authenticated
  WITH CHECK (public.is_admin());

-- Only admins can update achievements
CREATE POLICY "achievements_update_admin" ON public.achievements
  FOR UPDATE TO authenticated
  USING (public.is_admin())
  WITH CHECK (public.is_admin());

-- Only admins can delete achievements
CREATE POLICY "achievements_delete_admin" ON public.achievements
  FOR DELETE TO authenticated
  USING (public.is_admin());

-- ============================================================================
-- 8. CREATE POLICIES FOR TOURIST SPOTS
-- ============================================================================
-- Everyone can view tourist spots
CREATE POLICY "tourist_spots_select_all" ON public.tourist_spots
  FOR SELECT TO authenticated
  USING (true);

-- Only admins can insert tourist spots
CREATE POLICY "tourist_spots_insert_admin" ON public.tourist_spots
  FOR INSERT TO authenticated
  WITH CHECK (public.is_admin());

-- Only admins can update tourist spots
CREATE POLICY "tourist_spots_update_admin" ON public.tourist_spots
  FOR UPDATE TO authenticated
  USING (public.is_admin())
  WITH CHECK (public.is_admin());

-- Only admins can delete tourist spots
CREATE POLICY "tourist_spots_delete_admin" ON public.tourist_spots
  FOR DELETE TO authenticated
  USING (public.is_admin());

-- ============================================================================
-- 9. CREATE POLICIES FOR LEVELS
-- ============================================================================
-- Everyone can view levels
CREATE POLICY "levels_select_all" ON public.levels
  FOR SELECT TO authenticated
  USING (true);

-- Only admins can insert levels
CREATE POLICY "levels_insert_admin" ON public.levels
  FOR INSERT TO authenticated
  WITH CHECK (public.is_admin());

-- Only admins can update levels
CREATE POLICY "levels_update_admin" ON public.levels
  FOR UPDATE TO authenticated
  USING (public.is_admin())
  WITH CHECK (public.is_admin());

-- Only admins can delete levels
CREATE POLICY "levels_delete_admin" ON public.levels
  FOR DELETE TO authenticated
  USING (public.is_admin());

-- ============================================================================
-- 10. CREATE POLICIES FOR APP SETTINGS
-- ============================================================================
-- Everyone can view app settings
CREATE POLICY "app_settings_select_all" ON public.app_settings
  FOR SELECT TO authenticated
  USING (true);

-- Only admins can insert app settings
CREATE POLICY "app_settings_insert_admin" ON public.app_settings
  FOR INSERT TO authenticated
  WITH CHECK (public.is_admin());

-- Only admins can update app settings
CREATE POLICY "app_settings_update_admin" ON public.app_settings
  FOR UPDATE TO authenticated
  USING (public.is_admin())
  WITH CHECK (public.is_admin());

-- Only admins can delete app settings
CREATE POLICY "app_settings_delete_admin" ON public.app_settings
  FOR DELETE TO authenticated
  USING (public.is_admin());

-- ============================================================================
-- 11. CREATE POLICIES FOR USER ACHIEVEMENTS
-- ============================================================================
-- Users can view their own achievements, admins can view all
CREATE POLICY "user_achievements_select_own_or_admin" ON public.user_achievements
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id OR public.is_admin());

-- Users can insert their own achievements (when earned), admins can insert any
CREATE POLICY "user_achievements_insert_own_or_admin" ON public.user_achievements
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id OR public.is_admin());

-- Only admins can update user achievements
CREATE POLICY "user_achievements_update_admin" ON public.user_achievements
  FOR UPDATE TO authenticated
  USING (public.is_admin())
  WITH CHECK (public.is_admin());

-- Only admins can delete user achievements
CREATE POLICY "user_achievements_delete_admin" ON public.user_achievements
  FOR DELETE TO authenticated
  USING (public.is_admin());

-- ============================================================================
-- 12. CREATE POLICIES FOR CHECK-INS
-- ============================================================================
-- Users can view their own check-ins, admins can view all
CREATE POLICY "check_ins_select_own_or_admin" ON public.check_ins
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id OR public.is_admin());

-- Users can insert their own check-ins
CREATE POLICY "check_ins_insert_own" ON public.check_ins
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Only admins can update check-ins
CREATE POLICY "check_ins_update_admin" ON public.check_ins
  FOR UPDATE TO authenticated
  USING (public.is_admin())
  WITH CHECK (public.is_admin());

-- Only admins can delete check-ins
CREATE POLICY "check_ins_delete_admin" ON public.check_ins
  FOR DELETE TO authenticated
  USING (public.is_admin());

-- ============================================================================
-- 13. CREATE POLICIES FOR POINT TRANSACTIONS
-- ============================================================================
-- Users can view their own transactions, admins can view all
CREATE POLICY "point_transactions_select_own_or_admin" ON public.point_transactions
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id OR public.is_admin());

-- System and admins can insert transactions
CREATE POLICY "point_transactions_insert_system_or_admin" ON public.point_transactions
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id OR public.is_admin());

-- Only admins can update transactions
CREATE POLICY "point_transactions_update_admin" ON public.point_transactions
  FOR UPDATE TO authenticated
  USING (public.is_admin())
  WITH CHECK (public.is_admin());

-- Only admins can delete transactions
CREATE POLICY "point_transactions_delete_admin" ON public.point_transactions
  FOR DELETE TO authenticated
  USING (public.is_admin());

-- ============================================================================
-- 14. CREATE POLICIES FOR REWARD REDEMPTIONS
-- ============================================================================
-- Users can view their own redemptions, admins can view all
CREATE POLICY "reward_redemptions_select_own_or_admin" ON public.reward_redemptions
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id OR public.is_admin());

-- Users can insert their own redemptions
CREATE POLICY "reward_redemptions_insert_own" ON public.reward_redemptions
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own redemptions, admins can update any
CREATE POLICY "reward_redemptions_update_own_or_admin" ON public.reward_redemptions
  FOR UPDATE TO authenticated
  USING (auth.uid() = user_id OR public.is_admin())
  WITH CHECK (auth.uid() = user_id OR public.is_admin());

-- Only admins can delete redemptions
CREATE POLICY "reward_redemptions_delete_admin" ON public.reward_redemptions
  FOR DELETE TO authenticated
  USING (public.is_admin());

-- ============================================================================
-- 15. GRANT NECESSARY PERMISSIONS
-- ============================================================================
-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO anon, authenticated;

-- Grant permissions on all tables
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;

-- Grant permissions on all sequences
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- ============================================================================
-- 16. VERIFY ADMIN USER EXISTS
-- ============================================================================
-- Update admin user if exists
UPDATE public.profiles 
SET role = 'admin'
WHERE email = '<EMAIL>';

-- Insert admin user if not exists
INSERT INTO public.profiles (id, email, full_name, role, points, level, total_points_earned)
SELECT 
  id,
  '<EMAIL>',
  'Administrador',
  'admin'::user_role,
  1000,
  'Gold',
  1000
FROM auth.users
WHERE email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE
SET role = 'admin'::user_role;

COMMIT;

-- ============================================================================
-- VERIFICATION QUERIES (Run these after applying the script)
-- ============================================================================
-- Check if admin exists and has correct role:
-- SELECT id, email, role FROM public.profiles WHERE email = '<EMAIL>';

-- Check if is_admin function works:
-- SELECT public.is_admin();

-- List all policies on rewards table:
-- SELECT schemaname, tablename, policyname, cmd, qual, with_check 
-- FROM pg_policies 
-- WHERE tablename = 'rewards';