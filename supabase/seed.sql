-- SX Locadora Initial Data
-- Seed data for development and production

-- Insert default levels
INSERT INTO levels (name, description, min_points, color, icon, rewards) VALUES
('Bronze', 'Nível inicial para novos usuários', 0, '#cd7f32', 'award', '{"welcome_bonus": 50}'),
('Silver', 'Para usuários com experiência básica', 500, '#c0c0c0', 'award', '{"discount": "5%", "bonus_points": 10}'),
('Gold', 'Para usuários experientes e ativos', 1500, '#ffd700', 'crown', '{"discount": "10%", "bonus_points": 20, "priority_support": true}'),
('Platinum', 'Para usuários premium dedicados', 5000, '#e5e4e2', 'gem', '{"discount": "15%", "bonus_points": 50, "free_rentals": 2}'),
('Diamond', 'Nível máximo para verdadeiros campeões', 15000, '#b9f2ff', 'diamond', '{"discount": "25%", "bonus_points": 100, "free_rentals": 5, "vip_access": true}');

-- Insert default achievements
INSERT INTO achievements (name, description, icon, type, condition, reward_points, is_active) VALUES
-- Check-in achievements
('Primeiro Check-in', 'Realize seu primeiro check-in em um ponto turístico', 'map-pin', 'check_in', '{"check_ins": 1}', 100, true),
('Explorador Iniciante', 'Faça check-in em 5 pontos turísticos diferentes', 'compass', 'check_in', '{"unique_spots": 5}', 250, true),
('Turista Experiente', 'Visite 15 pontos turísticos únicos', 'map', 'explorer', '{"unique_spots": 15}', 500, true),
('Mestre Explorador', 'Conquiste todos os pontos turísticos da cidade', 'trophy', 'explorer', '{"all_spots": true}', 1000, true),

-- Points achievements
('Colecionador', 'Acumule 1.000 pontos Ponto X', 'coins', 'points', '{"total_points": 1000}', 200, true),
('Milionário de Pontos', 'Alcance 10.000 pontos acumulados', 'banknote', 'points', '{"total_points": 10000}', 1000, true),

-- Streak achievements
('Semana Ativa', 'Faça check-in por 7 dias consecutivos', 'calendar-check', 'streak', '{"consecutive_days": 7}', 300, true),
('Campeão da Consistência', 'Mantenha uma sequência de 30 dias', 'flame', 'champion', '{"consecutive_days": 30}', 1500, true);

-- Insert sample tourist spots
INSERT INTO tourist_spots (name, description, latitude, longitude, points_reward, image_url, address, is_active, check_in_radius, category) VALUES
-- Pontos Turísticos
('Cristo Redentor', 'Uma das Sete Maravilhas do Mundo Moderno, localizada no topo do Corcovado', -22.9519, -43.2105, 100, 'https://example.com/cristo.jpg', 'Parque Nacional da Tijuca - Alto da Boa Vista, Rio de Janeiro - RJ', true, 200, 'tourist_spot'),
('Pão de Açúcar', 'Complexo de morros localizado na Urca, famoso mundialmente', -22.9486, -43.1565, 80, 'https://example.com/pao-de-acucar.jpg', 'Av. Pasteur, 520 - Urca, Rio de Janeiro - RJ', true, 150, 'tourist_spot'),
('Praia de Copacabana', 'Uma das praias mais famosas do mundo', -22.9711, -43.1822, 50, 'https://example.com/copacabana.jpg', 'Copacabana, Rio de Janeiro - RJ', true, 100, 'tourist_spot'),
('Maracanã', 'O lendário estádio de futebol brasileiro', -22.9122, -43.2302, 60, 'https://example.com/maracana.jpg', 'Av. Pres. Castelo Branco, Portão 3 - Maracanã, Rio de Janeiro - RJ', true, 100, 'tourist_spot'),
('Theatro Municipal', 'Teatro histórico e centro cultural da cidade', -22.9097, -43.1759, 70, 'https://example.com/theatro.jpg', 'Praça Floriano, s/n - Centro, Rio de Janeiro - RJ', true, 80, 'tourist_spot'),
('Museu do Amanhã', 'Museu de ciências aplicadas localizado no Porto Maravilha', -22.8956, -43.1783, 60, 'https://example.com/museu-amanha.jpg', 'Praça Mauá, 1 - Centro, Rio de Janeiro - RJ', true, 80, 'tourist_spot'),

-- Restaurantes
('Confeitaria Colombo', 'Histórica confeitaria do Rio de Janeiro, fundada em 1894', -22.9097, -43.1787, 40, 'https://example.com/colombo.jpg', 'Rua Gonçalves Dias, 32 - Centro, Rio de Janeiro - RJ', true, 50, 'restaurant'),
('Aprazível', 'Restaurante com vista panorâmica e culinária brasileira contemporânea', -22.9132, -43.2398, 50, 'https://example.com/aprazivel.jpg', 'Rua Aprazível, 62 - Santa Teresa, Rio de Janeiro - RJ', true, 60, 'restaurant'),
('Zaza Bistrô Tropical', 'Bistrô com ambiente descontraído e pratos autorais', -22.9845, -43.2047, 45, 'https://example.com/zaza.jpg', 'Rua Joana Angélica, 40 - Ipanema, Rio de Janeiro - RJ', true, 50, 'restaurant'),
('Casa do Bacalhau', 'Especializado em pratos portugueses tradicionais', -22.9711, -43.1822, 35, 'https://example.com/bacalhau.jpg', 'Av. Nossa Senhora de Copacabana, 1321 - Copacabana, Rio de Janeiro - RJ', true, 50, 'restaurant'),
('Quadrucci', 'Restaurante italiano com ambiente familiar', -22.9845, -43.2047, 40, 'https://example.com/quadrucci.jpg', 'Rua Dias Ferreira, 233 - Leblon, Rio de Janeiro - RJ', true, 50, 'restaurant'),

-- Locação de Passeios e Veículos
('Rio Bike Tour', 'Aluguel de bicicletas e tours ecológicos pela cidade', -22.9711, -43.1822, 30, 'https://example.com/riobike.jpg', 'Posto 4, Copacabana - Rio de Janeiro - RJ', true, 80, 'rental_services'),
('Jeep Tour Tijuca', 'Passeios de jipe pela Floresta da Tijuca', -22.9519, -43.2105, 60, 'https://example.com/jeeptour.jpg', 'Estrada das Paineiras - Alto da Boa Vista, Rio de Janeiro - RJ', true, 100, 'rental_services'),
('Stand Up Paddle Lagoa', 'Aluguel de pranchas de SUP na Lagoa Rodrigo de Freitas', -22.9711, -43.2047, 35, 'https://example.com/sup.jpg', 'Av. Borges de Medeiros - Lagoa, Rio de Janeiro - RJ', true, 70, 'rental_services'),
('Trilhas Cariocas', 'Guias especializados em trilhas e caminhadas ecológicas', -22.9132, -43.2398, 50, 'https://example.com/trilhas.jpg', 'Largo do Guimarães - Santa Teresa, Rio de Janeiro - RJ', true, 60, 'rental_services'),
('Passeio de Barco Baía', 'Passeios de barco pela Baía de Guanabara', -22.8831, -43.1050, 80, 'https://example.com/barco.jpg', 'Marina da Glória - Glória, Rio de Janeiro - RJ', true, 100, 'rental_services'),
('Voo de Asa Delta', 'Voos de asa delta com instrutores certificados', -22.9986, -43.2847, 120, 'https://example.com/asadelta.jpg', 'Estrada da Canoa, 1476 - São Conrado, Rio de Janeiro - RJ', true, 150, 'rental_services');

-- Insert sample vehicles
INSERT INTO vehicles (name, type, description, image_url, hourly_price, daily_price, is_available, location, features) VALUES
('Patinete Elétrico Premium', 'scooter', 'Patinete elétrico de alta qualidade com autonomia de 25km', 'https://example.com/scooter1.jpg', 15.00, 80.00, true, 'Copacabana', '{"max_speed": "25km/h", "autonomy": "25km", "weight": "12kg", "lights": true}'),
('Bike Urbana Comfort', 'bike', 'Bicicleta urbana confortável ideal para passeios pela cidade', 'https://example.com/bike1.jpg', 8.00, 40.00, true, 'Ipanema', '{"gears": 7, "basket": true, "lights": true, "comfort_seat": true}'),
('E-Bike Adventure', 'e-bike', 'Bike elétrica para aventuras urbanas com assistência inteligente', 'https://example.com/ebike1.jpg', 25.00, 120.00, true, 'Centro', '{"battery_range": "50km", "power": "250W", "gears": 21, "display": true}'),
('Skate Longboard', 'skateboard', 'Longboard profissional para deslocamentos urbanos', 'https://example.com/skateboard1.jpg', 12.00, 60.00, true, 'Barra da Tijuca', '{"length": "42in", "trucks": "aluminum", "wheels": "soft", "grip_tape": true}'),
('Patinete Eco', 'scooter', 'Patinete elétrico econômico para trajetos curtos', 'https://example.com/scooter2.jpg', 10.00, 50.00, true, 'Botafogo', '{"max_speed": "20km/h", "autonomy": "15km", "weight": "10kg", "foldable": true}'),
('Bike Retrô', 'bike', 'Bicicleta vintage com design clássico e conforto moderno', 'https://example.com/bike2.jpg', 10.00, 50.00, true, 'Santa Teresa', '{"style": "vintage", "basket": true, "bell": true, "comfort_grip": true}');

-- Insert sample rewards
INSERT INTO rewards (name, description, cost_points, type, value, image_url, is_active, stock, valid_until) VALUES
('10% de Desconto', 'Desconto de 10% em qualquer aluguel', 500, 'discount', '10', 'https://example.com/discount10.jpg', true, NULL, NULL),
('Aluguel Gratuito 1h', 'Uma hora grátis de aluguel em qualquer veículo', 800, 'free_rental', '1', 'https://example.com/free1h.jpg', true, 100, NULL),
('25% de Desconto Premium', 'Desconto especial de 25% para membros premium', 1200, 'discount', '25', 'https://example.com/discount25.jpg', true, 50, NULL),
('Camiseta SX Locadora', 'Camiseta oficial da SX Locadora', 1000, 'merchandise', 'Camiseta M', 'https://example.com/tshirt.jpg', true, 25, NULL),
('Experiência VIP', 'Acesso a eventos exclusivos e tours especiais', 2500, 'experience', 'VIP Access', 'https://example.com/vip.jpg', true, 10, NULL),
('Parceria Café Premium', 'Café grátis em cafeterias parceiras', 300, 'partner_benefit', 'Free Coffee', 'https://example.com/coffee.jpg', true, NULL, '2025-12-31'),
('Aluguel Gratuito Dia Inteiro', 'Um dia completo de aluguel gratuito', 2000, 'free_rental', '24', 'https://example.com/free24h.jpg', true, 20, NULL);

-- Insert app settings
INSERT INTO app_settings (key, value, description) VALUES
('whatsapp_number', '"5511999999999"', 'Número do WhatsApp para contato de aluguel'),
('welcome_points', '100', 'Pontos dados aos novos usuários'),
('daily_check_in_limit', '5', 'Limite de check-ins por dia por usuário'),
('min_check_in_interval', '3600', 'Intervalo mínimo entre check-ins no mesmo local (segundos)'),
('app_name', '"SX Locadora"', 'Nome da aplicação'),
('app_description', '"Plataforma de mobilidade urbana com gamificação"', 'Descrição da aplicação'),
('support_email', '"<EMAIL>"', 'Email de suporte'),
('terms_url', '"https://sxlocadora.com.br/termos"', 'URL dos termos de uso'),
('privacy_url', '"https://sxlocadora.com.br/privacidade"', 'URL da política de privacidade'),
('social_instagram', '"@sxlocadora"', 'Perfil do Instagram'),
('social_facebook', '"SXLocadora"', 'Perfil do Facebook'),
('rental_base_points', '10', 'Pontos base ganhos por aluguel'),
('achievement_check_enabled', 'true', 'Habilitar verificação automática de conquistas'),
('leaderboard_enabled', 'true', 'Habilitar ranking de usuários'),
('referral_points', '200', 'Pontos ganhos por indicação'),
('max_daily_points', '500', 'Máximo de pontos que podem ser ganhos por dia');

-- Create admin user profile
-- Note: The admin user must first be created through Supabase Auth with:
-- Email: <EMAIL>
-- Password: Zxc12345!
-- Then this will set their role to admin

-- After creating the user through Supabase Auth, update their role to admin
DO $$
BEGIN
    -- Wait for the profile to be created by the trigger, then update role
    -- This assumes the admin user was created through Supabase Auth first
    IF EXISTS (SELECT 1 FROM profiles WHERE email = '<EMAIL>') THEN
        UPDATE profiles 
        SET 
            role = 'admin',
            full_name = 'Administrador SX',
            points = 10000,
            level = 'Diamond',
            total_points_earned = 10000
        WHERE email = '<EMAIL>';
    END IF;
END $$;