-- Migration: Fix user levels to match their current points
-- This updates all users' levels to match their current point totals

-- Update all profiles with the correct level based on their current points
UPDATE profiles p
SET level = (
    SELECT name 
    FROM levels l
    WHERE l.min_points <= p.points
    ORDER BY l.min_points DESC
    LIMIT 1
)
WHERE p.level != (
    SELECT name 
    FROM levels l
    WHERE l.min_points <= p.points
    ORDER BY l.min_points DESC
    LIMIT 1
);

-- Show the updated profiles for verification
SELECT id, email, points, level, total_points_earned 
FROM profiles 
WHERE points > 0
ORDER BY points DESC;