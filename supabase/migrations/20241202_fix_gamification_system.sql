-- Migration: Fix gamification system issues
-- This migration addresses several issues with the gamification system

-- 1. Create tourist spot category type if it doesn't exist
DO $$ BEGIN
    CREATE TYPE tourist_spot_category AS ENUM ('tourist_spot', 'restaurant', 'rental_services');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 2. Add category column to tourist_spots table
ALTER TABLE tourist_spots 
ADD COLUMN IF NOT EXISTS category tourist_spot_category DEFAULT 'tourist_spot' NOT NULL;

-- 3. Ensure update_user_points function exists with correct signature
CREATE OR REPLACE FUNCTION update_user_points(
    user_id UUID,
    points_to_add INTEGER,
    trans_type transaction_type,
    trans_source transaction_source,
    trans_description TEXT,
    trans_metadata JSONB DEFAULT NULL
)
RETURNS void AS $$
DECLARE
    current_points INTEGER;
    new_points INTEGER;
    new_level TEXT;
BEGIN
    -- Get current points (handle case where profile doesn't exist)
    SELECT COALESCE(points, 0) INTO current_points 
    FROM profiles 
    WHERE id = user_id;
    
    -- If no profile found, create basic profile
    IF current_points IS NULL THEN
        INSERT INTO profiles (id, email, points, total_points_earned, level)
        SELECT user_id, '', 0, 0, 'Bronze'
        WHERE NOT EXISTS (SELECT 1 FROM profiles WHERE id = user_id);
        current_points := 0;
    END IF;
    
    -- Calculate new points
    new_points := current_points + points_to_add;
    
    -- Ensure points don't go below 0
    IF new_points < 0 THEN
        new_points := 0;
    END IF;
    
    -- Determine new level based on points
    SELECT name INTO new_level 
    FROM levels 
    WHERE min_points <= new_points 
    ORDER BY min_points DESC 
    LIMIT 1;
    
    -- Update profile
    UPDATE profiles 
    SET 
        points = new_points,
        level = COALESCE(new_level, 'Bronze'),
        total_points_earned = CASE 
            WHEN trans_type = 'earned' THEN total_points_earned + points_to_add 
            ELSE total_points_earned 
        END,
        updated_at = NOW()
    WHERE id = user_id;
    
    -- Record transaction
    INSERT INTO point_transactions (user_id, points, type, source, description, metadata)
    VALUES (user_id, points_to_add, trans_type, trans_source, trans_description, trans_metadata);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Insert default levels if they don't exist
INSERT INTO levels (name, description, min_points, color, icon, rewards) 
VALUES 
    ('Bronze', 'Nível inicial para todos os usuários', 0, '#CD7F32', '🥉', '{}'),
    ('Silver', 'Desbloqueado com 500 pontos', 500, '#C0C0C0', '🥈', '{"discount": 5}'),
    ('Gold', 'Desbloqueado com 1.500 pontos', 1500, '#FFD700', '🥇', '{"discount": 10, "priority_support": true}'),
    ('Platinum', 'Desbloqueado com 5.000 pontos', 5000, '#E5E4E2', '💎', '{"discount": 15, "free_rentals": true}'),
    ('Diamond', 'Nível máximo com 15.000 pontos', 15000, '#B9F2FF', '💠', '{"discount": 25, "vip_status": true}')
ON CONFLICT (name) DO NOTHING;

-- 5. Insert sample achievements if they don't exist
INSERT INTO achievements (name, description, icon, type, condition, reward_points, is_active)
VALUES
    ('Primeiro Check-in', 'Faça seu primeiro check-in', '🏁', 'check_in', '{"check_ins": 1}', 50, true),
    ('Explorador Iniciante', 'Faça check-in em 5 locais diferentes', '🗺️', 'explorer', '{"unique_spots": 5}', 100, true),
    ('Colecionador de Pontos', 'Acumule 1.000 pontos', '💰', 'points', '{"total_points": 1000}', 150, true),
    ('Aventureiro', 'Faça check-in em 10 locais diferentes', '🧭', 'explorer', '{"unique_spots": 10}', 200, true),
    ('Mestre dos Check-ins', 'Realize 50 check-ins', '🎯', 'check_in', '{"check_ins": 50}', 300, true)
ON CONFLICT (name) DO NOTHING;

-- 6. Grant necessary permissions
GRANT EXECUTE ON FUNCTION update_user_points TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_points TO anon;