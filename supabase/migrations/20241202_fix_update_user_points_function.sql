-- Migration: Fix update_user_points function parameter names
-- This migration drops and recreates the function with correct parameter names

-- Drop the existing function with its current signature
DROP FUNCTION IF EXISTS update_user_points(uuid, integer, transaction_type, transaction_source, text, jsonb);

-- Recreate the function with correct parameter names matching the RPC calls
CREATE OR REPLACE FUNCTION update_user_points(
    user_id UUID,
    points_to_add INTEGER,
    trans_type transaction_type,
    trans_source transaction_source,
    trans_description TEXT,
    trans_metadata JSONB DEFAULT NULL
)
RETURNS void AS $$
DECLARE
    current_points INTEGER;
    new_points INTEGER;
    new_level TEXT;
BEGIN
    -- Get current points (handle case where profile doesn't exist)
    SELECT COALESCE(points, 0) INTO current_points 
    FROM profiles 
    WHERE id = user_id;
    
    -- If no profile found, return error (profile should exist)
    IF current_points IS NULL THEN
        RAISE EXCEPTION 'Profile not found for user_id: %', user_id;
    END IF;
    
    -- Calculate new points
    new_points := current_points + points_to_add;
    
    -- Ensure points don't go below 0
    IF new_points < 0 THEN
        new_points := 0;
    END IF;
    
    -- Determine new level based on points
    SELECT name INTO new_level 
    FROM levels 
    WHERE min_points <= new_points 
    ORDER BY min_points DESC 
    LIMIT 1;
    
    -- Update profile
    UPDATE profiles 
    SET 
        points = new_points,
        level = COALESCE(new_level, 'Bronze'),
        total_points_earned = CASE 
            WHEN trans_type = 'earned' THEN total_points_earned + points_to_add 
            ELSE total_points_earned 
        END,
        updated_at = NOW()
    WHERE id = user_id;
    
    -- Record transaction
    INSERT INTO point_transactions (user_id, points, type, source, description, metadata)
    VALUES (user_id, points_to_add, trans_type, trans_source, trans_description, trans_metadata);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION update_user_points TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_points TO anon;