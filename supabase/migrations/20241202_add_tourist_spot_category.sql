-- Migration: Add category column to tourist_spots table
-- This adds support for different types of tourist spots

-- Create tourist spot category type if it doesn't exist
DO $$ BEGIN
    CREATE TYPE tourist_spot_category AS ENUM ('tourist_spot', 'restaurant', 'rental_services');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add category column to tourist_spots table
ALTER TABLE tourist_spots 
ADD COLUMN IF NOT EXISTS category tourist_spot_category DEFAULT 'tourist_spot' NOT NULL;