-- SX Locadora - Fix para o erro "relation 'profiles' does not exist"
-- Este arquivo corrige problemas de transação, triggers e RLS

-- Inicia transação explícita
BEGIN;

-- 1. VERIFICAR SE EXTENSÕES ESTÃO HABILITADAS
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 2. RECRIAR TIPOS SE NECESSÁRIO
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('user', 'admin');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE transaction_type AS ENUM ('earned', 'redeemed', 'bonus', 'penalty');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE transaction_source AS ENUM ('check_in', 'achievement', 'admin', 'referral', 'rental', 'social');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE achievement_type AS ENUM ('check_in', 'rental', 'points', 'social', 'special', 'streak', 'explorer', 'champion');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE reward_type AS ENUM ('discount', 'free_rental', 'merchandise', 'experience', 'partner_benefit');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE redemption_status AS ENUM ('pending', 'approved', 'used', 'expired');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE vehicle_type AS ENUM ('scooter', 'bike', 'e-bike', 'skateboard');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 3. RECRIAR TABELA PROFILES COM VERIFICAÇÃO
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    phone TEXT,
    role user_role DEFAULT 'user',
    avatar_url TEXT,
    points INTEGER DEFAULT 0 CHECK (points >= 0),
    level TEXT DEFAULT 'Bronze',
    total_points_earned INTEGER DEFAULT 0 CHECK (total_points_earned >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. RECRIAR OUTRAS TABELAS ESSENCIAIS
CREATE TABLE IF NOT EXISTS levels (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    min_points INTEGER NOT NULL CHECK (min_points >= 0),
    color TEXT NOT NULL,
    icon TEXT,
    rewards JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS point_transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    points INTEGER NOT NULL,
    type transaction_type NOT NULL,
    source transaction_source NOT NULL,
    description TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. REMOVER TRIGGER ANTIGO SE EXISTIR
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- 6. RECRIAR FUNÇÃO DE CRIAÇÃO DE USUÁRIO COM TRATAMENTO DE ERRO ROBUSTO
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    user_email TEXT;
    user_name TEXT;
BEGIN
    -- Garantir que temos email válido
    user_email := COALESCE(NEW.email, '');
    
    -- Extrair nome do metadata
    user_name := COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name', '');
    
    -- Inserir perfil com tratamento de erro
    BEGIN
        INSERT INTO public.profiles (id, email, full_name, points, level, total_points_earned)
        VALUES (
            NEW.id, 
            user_email, 
            user_name,
            100, -- Bônus de boas-vindas
            'Bronze',
            100
        );
        
        -- Adicionar transação de bônus de boas-vindas
        INSERT INTO public.point_transactions (
            user_id, 
            points, 
            type, 
            source, 
            description
        ) VALUES (
            NEW.id,
            100,
            'bonus',
            'admin',
            'Bônus de boas-vindas'
        );
        
    EXCEPTION 
        WHEN unique_violation THEN
            -- Se perfil já existe, apenas atualizar email se necessário
            UPDATE public.profiles 
            SET email = user_email, updated_at = NOW()
            WHERE id = NEW.id;
        WHEN OTHERS THEN
            -- Log erro mas não falhar o trigger
            RAISE WARNING 'Erro ao criar perfil para usuário %: %', NEW.id, SQLERRM;
    END;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. RECRIAR TRIGGER COM CONDIÇÕES MAIS SEGURAS
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    WHEN (NEW.email IS NOT NULL) -- Só executar se temos email
    EXECUTE FUNCTION handle_new_user();

-- 8. RECRIAR FUNÇÃO DE ATUALIZAÇÃO DE PONTOS
CREATE OR REPLACE FUNCTION update_user_points(
    user_id UUID,
    points_to_add INTEGER,
    trans_type transaction_type,
    trans_source transaction_source,
    trans_description TEXT,
    trans_metadata JSONB DEFAULT NULL
)
RETURNS void AS $$
DECLARE
    current_points INTEGER;
    new_points INTEGER;
    new_level TEXT;
BEGIN
    -- Verificar se usuário existe
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = user_id) THEN
        RAISE EXCEPTION 'Usuário % não encontrado', user_id;
    END IF;
    
    -- Get current points
    SELECT points INTO current_points FROM profiles WHERE id = user_id;
    
    -- Calculate new points
    new_points := current_points + points_to_add;
    
    -- Ensure points don't go below 0
    IF new_points < 0 THEN
        new_points := 0;
    END IF;
    
    -- Determine new level based on points
    SELECT name INTO new_level 
    FROM levels 
    WHERE min_points <= new_points 
    ORDER BY min_points DESC 
    LIMIT 1;
    
    -- Update profile
    UPDATE profiles 
    SET 
        points = new_points,
        level = COALESCE(new_level, 'Bronze'),
        total_points_earned = CASE 
            WHEN trans_type = 'earned' THEN total_points_earned + points_to_add 
            ELSE total_points_earned 
        END,
        updated_at = NOW()
    WHERE id = user_id;
    
    -- Record transaction
    INSERT INTO point_transactions (user_id, points, type, source, description, metadata)
    VALUES (user_id, points_to_add, trans_type, trans_source, trans_description, trans_metadata);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. CONFIGURAR RLS MAIS PERMISSIVO
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Remover políticas antigas
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can update any profile" ON profiles;

-- Criar políticas mais permissivas
CREATE POLICY "Enable read access for all users" ON profiles
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON profiles
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

CREATE POLICY "Admins can update any profile" ON profiles
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- 10. CONFIGURAR RLS PARA POINT_TRANSACTIONS
ALTER TABLE point_transactions ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view their own transactions" ON point_transactions;
DROP POLICY IF EXISTS "System can insert transactions" ON point_transactions;
DROP POLICY IF EXISTS "Admins can view all transactions" ON point_transactions;

CREATE POLICY "Users can view own transactions" ON point_transactions
    FOR SELECT USING (auth.uid() = user_id OR EXISTS (
        SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin'
    ));

CREATE POLICY "Authenticated users can insert transactions" ON point_transactions
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- 11. CRIAR ÍNDICES ESSENCIAIS
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_points ON profiles(points DESC);
CREATE INDEX IF NOT EXISTS idx_point_transactions_user_id ON point_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_point_transactions_created_at ON point_transactions(created_at DESC);

-- 12. INSERIR NÍVEIS PADRÃO SE NÃO EXISTIREM
INSERT INTO levels (name, description, min_points, color, icon, rewards) 
VALUES 
('Bronze', 'Nível inicial para novos usuários', 0, '#cd7f32', 'award', '{"welcome_bonus": 50}'),
('Silver', 'Para usuários com experiência básica', 500, '#c0c0c0', 'award', '{"discount": "5%", "bonus_points": 10}'),
('Gold', 'Para usuários experientes e ativos', 1500, '#ffd700', 'crown', '{"discount": "10%", "bonus_points": 20, "priority_support": true}'),
('Platinum', 'Para usuários premium dedicados', 5000, '#e5e4e2', 'gem', '{"discount": "15%", "bonus_points": 50, "free_rentals": 2}'),
('Diamond', 'Nível máximo para verdadeiros campeões', 15000, '#b9f2ff', 'diamond', '{"discount": "25%", "bonus_points": 100, "free_rentals": 5, "vip_access": true}')
ON CONFLICT (name) DO NOTHING;

-- 13. COMMIT DA TRANSAÇÃO
COMMIT;

-- 14. TESTE DA FUNÇÃO DE CRIAÇÃO DE PERFIL
-- Verificar se a função funciona corretamente
DO $$
BEGIN
    RAISE NOTICE 'Schema corrigido com sucesso!';
    RAISE NOTICE 'Tabela profiles existe: %', 
        CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles') 
        THEN 'SIM' ELSE 'NÃO' END;
    RAISE NOTICE 'Trigger existe: %',
        CASE WHEN EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'on_auth_user_created') 
        THEN 'SIM' ELSE 'NÃO' END;
    RAISE NOTICE 'Políticas RLS criadas: %',
        (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'profiles');
END $$;