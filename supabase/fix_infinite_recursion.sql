-- SX Locadora - Correção Definitiva para Recursão Infinita em RLS
-- Solução baseada na documentação oficial do Supabase
-- Fix para: "infinite recursion detected in policy for relation 'profiles'"

BEGIN;

-- 1. REMOVER TODAS AS POLÍTICAS RLS PROBLEMÁTICAS
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update any profile" ON profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "profiles_select_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_update_own_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_update_admin_policy" ON profiles;
DROP POLICY IF EXISTS "profiles_delete_admin_policy" ON profiles;

-- 2. CRIAR SCHEMA PRIVADO PARA FUNÇÕES DE SEGURANÇA
CREATE SCHEMA IF NOT EXISTS private;

-- 3. CRIAR FUNÇÃO SECURITY DEFINER PARA VERIFICAR SE É ADMIN
-- Esta função executa com privilégios do criador, evitando recursão RLS
CREATE OR REPLACE FUNCTION private.is_admin()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER -- Executa com privilégios do criador
SET search_path = public
AS $$
BEGIN
    -- Verificar diretamente no auth.users para evitar recursão
    RETURN EXISTS (
        SELECT 1 
        FROM auth.users u 
        WHERE u.id = auth.uid() 
        AND (
            -- Verificar no metadata se tem role admin
            u.raw_user_meta_data->>'role' = 'admin'
            OR 
            -- Ou verificar email de admin padrão
            u.email = '<EMAIL>'
        )
    );
END;
$$;

-- 4. CRIAR FUNÇÃO PARA VERIFICAR SE PERFIL EXISTE (SEM RECURSÃO)
CREATE OR REPLACE FUNCTION private.user_has_profile(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Esta função pode acessar profiles sem RLS
    RETURN EXISTS (SELECT 1 FROM profiles WHERE id = user_id);
END;
$$;

-- 5. CRIAR POLÍTICAS RLS SEM RECURSÃO

-- Política SELECT: Todos podem ver perfis
CREATE POLICY "profiles_can_be_viewed_by_all" ON profiles
    FOR SELECT 
    USING (true);

-- Política INSERT: Usuários autenticados podem inserir seu próprio perfil
CREATE POLICY "users_can_insert_own_profile" ON profiles
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid() = id);

-- Política UPDATE: Usuários podem atualizar seu próprio perfil
CREATE POLICY "users_can_update_own_profile" ON profiles
    FOR UPDATE 
    TO authenticated
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Política UPDATE para ADMINS: Admins podem atualizar qualquer perfil (sem recursão)
CREATE POLICY "admins_can_update_any_profile" ON profiles
    FOR UPDATE 
    TO authenticated
    USING (private.is_admin())
    WITH CHECK (private.is_admin());

-- Política DELETE: Apenas admins podem deletar perfis
CREATE POLICY "admins_can_delete_profiles" ON profiles
    FOR DELETE 
    TO authenticated
    USING (private.is_admin());

-- 6. CORRIGIR OUTRAS TABELAS COM POSSÍVEL RECURSÃO

-- Limpar políticas antigas de user_achievements
DROP POLICY IF EXISTS "Users can view their own achievements" ON user_achievements;
DROP POLICY IF EXISTS "Admins can view all achievements" ON user_achievements;
DROP POLICY IF EXISTS "System can insert achievements" ON user_achievements;
DROP POLICY IF EXISTS "user_achievements_select_policy" ON user_achievements;
DROP POLICY IF EXISTS "user_achievements_insert_policy" ON user_achievements;

-- Recriar políticas de user_achievements
CREATE POLICY "view_own_achievements" ON user_achievements
    FOR SELECT 
    TO authenticated
    USING (auth.uid() = user_id OR private.is_admin());

CREATE POLICY "insert_own_achievements" ON user_achievements
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid() = user_id OR private.is_admin());

-- Limpar políticas de check_ins
DROP POLICY IF EXISTS "Users can view their own check-ins" ON check_ins;
DROP POLICY IF EXISTS "Users can insert their own check-ins" ON check_ins;
DROP POLICY IF EXISTS "Admins can view all check-ins" ON check_ins;
DROP POLICY IF EXISTS "check_ins_select_policy" ON check_ins;
DROP POLICY IF EXISTS "check_ins_insert_policy" ON check_ins;

-- Recriar políticas de check_ins
CREATE POLICY "view_own_checkins" ON check_ins
    FOR SELECT 
    TO authenticated
    USING (auth.uid() = user_id OR private.is_admin());

CREATE POLICY "insert_own_checkins" ON check_ins
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- Limpar políticas de point_transactions
DROP POLICY IF EXISTS "Users can view their own transactions" ON point_transactions;
DROP POLICY IF EXISTS "System can insert transactions" ON point_transactions;
DROP POLICY IF EXISTS "Admins can view all transactions" ON point_transactions;
DROP POLICY IF EXISTS "Users can view own transactions" ON point_transactions;
DROP POLICY IF EXISTS "Authenticated users can insert transactions" ON point_transactions;
DROP POLICY IF EXISTS "point_transactions_select_policy" ON point_transactions;
DROP POLICY IF EXISTS "point_transactions_insert_policy" ON point_transactions;

-- Recriar políticas de point_transactions
CREATE POLICY "view_own_transactions" ON point_transactions
    FOR SELECT 
    TO authenticated
    USING (auth.uid() = user_id OR private.is_admin());

CREATE POLICY "insert_transactions" ON point_transactions
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid() = user_id OR private.is_admin());

-- Limpar políticas de reward_redemptions
DROP POLICY IF EXISTS "Users can view their own redemptions" ON reward_redemptions;
DROP POLICY IF EXISTS "Users can insert their own redemptions" ON reward_redemptions;
DROP POLICY IF EXISTS "Admins can view and update all redemptions" ON reward_redemptions;
DROP POLICY IF EXISTS "reward_redemptions_select_policy" ON reward_redemptions;
DROP POLICY IF EXISTS "reward_redemptions_insert_policy" ON reward_redemptions;
DROP POLICY IF EXISTS "reward_redemptions_update_policy" ON reward_redemptions;

-- Recriar políticas de reward_redemptions
CREATE POLICY "view_own_redemptions" ON reward_redemptions
    FOR SELECT 
    TO authenticated
    USING (auth.uid() = user_id OR private.is_admin());

CREATE POLICY "insert_own_redemptions" ON reward_redemptions
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "update_redemptions" ON reward_redemptions
    FOR UPDATE 
    TO authenticated
    USING (auth.uid() = user_id OR private.is_admin())
    WITH CHECK (auth.uid() = user_id OR private.is_admin());

-- 7. ATUALIZAR FUNÇÃO DE PONTOS PARA USAR SECURITY DEFINER
CREATE OR REPLACE FUNCTION update_user_points(
    user_id UUID,
    points_to_add INTEGER,
    trans_type transaction_type,
    trans_source transaction_source,
    trans_description TEXT,
    trans_metadata JSONB DEFAULT NULL
)
RETURNS void 
LANGUAGE plpgsql 
SECURITY DEFINER -- Executa sem RLS
SET search_path = public
AS $$
DECLARE
    current_points INTEGER;
    new_points INTEGER;
    new_level TEXT;
BEGIN
    -- Verificar se usuário existe (sem RLS)
    IF NOT private.user_has_profile(user_id) THEN
        RAISE EXCEPTION 'Usuário % não encontrado', user_id;
    END IF;
    
    -- Get current points
    SELECT points INTO current_points FROM profiles WHERE id = user_id;
    
    -- Calculate new points
    new_points := current_points + points_to_add;
    
    -- Ensure points don't go below 0
    IF new_points < 0 THEN
        new_points := 0;
    END IF;
    
    -- Determine new level based on points
    SELECT name INTO new_level 
    FROM levels 
    WHERE min_points <= new_points 
    ORDER BY min_points DESC 
    LIMIT 1;
    
    -- Update profile
    UPDATE profiles 
    SET 
        points = new_points,
        level = COALESCE(new_level, 'Bronze'),
        total_points_earned = CASE 
            WHEN trans_type = 'earned' THEN total_points_earned + points_to_add 
            ELSE total_points_earned 
        END,
        updated_at = NOW()
    WHERE id = user_id;
    
    -- Record transaction
    INSERT INTO point_transactions (user_id, points, type, source, description, metadata)
    VALUES (user_id, points_to_add, trans_type, trans_source, trans_description, trans_metadata);
END;
$$;

-- 8. CRIAR FUNÇÃO PARA VERIFICAR ROLE DO USUÁRIO
CREATE OR REPLACE FUNCTION private.get_user_role(user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role FROM profiles WHERE id = user_id;
    RETURN COALESCE(user_role, 'user');
END;
$$;

-- 9. CONCEDER PERMISSÕES NECESSÁRIAS
GRANT USAGE ON SCHEMA private TO authenticated, anon;
GRANT EXECUTE ON FUNCTION private.is_admin() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION private.user_has_profile(UUID) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION private.get_user_role(UUID) TO authenticated, anon;

-- 10. VERIFICAR SE AS POLÍTICAS FORAM CRIADAS
DO $$
BEGIN
    RAISE NOTICE '=== CORREÇÃO DE RECURSÃO INFINITA CONCLUÍDA ===';
    RAISE NOTICE 'Políticas na tabela profiles: %', 
        (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'profiles');
    RAISE NOTICE 'Função is_admin criada: %',
        CASE WHEN EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'is_admin') 
        THEN 'SIM' ELSE 'NÃO' END;
    RAISE NOTICE 'Schema private criado: %',
        CASE WHEN EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = 'private') 
        THEN 'SIM' ELSE 'NÃO' END;
    RAISE NOTICE '=== TESTANDO FUNÇÃO DE ADMIN ===';
    RAISE NOTICE 'Função is_admin está funcionando: %',
        CASE WHEN private.is_admin() IS NOT NULL THEN 'SIM' ELSE 'NÃO' END;
END $$;

COMMIT;