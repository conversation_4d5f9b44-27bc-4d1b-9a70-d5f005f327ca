# SX Locadora - Mobility Rental Platform MVP

🚲 **Plataforma de mobilidade urbana com sistema de gamificação Ponto X integrado**

Um MVP de aplicação web progressiva (PWA) moderna para aluguel de patinetes, bikes e veículos recreativos com sistema completo de gamificação e painel administrativo.

## 🌟 Funcionalidades Implementadas

### 🎯 Para Usuários
- ✅ **Sistema de Autenticação Completo**: Cadastro e login com validação brasileira
- ✅ **Dashboard Pessoal**: Visualização de pontos, nível, conquistas e progresso
- ✅ **Sistema Ponto X**: Gamificação completa com 5 níveis (Bronze, Silver, Gold, Platinum, Diamond)
- ✅ **Sistema de Conquistas**: 8 tipos de achievements com validação automática
- ✅ **Check-in em Pontos Turísticos**: Sistema de geolocalização com validação de proximidade
- ✅ **Catálogo de Veículos**: Interface responsiva com filtros e integração WhatsApp
- ✅ **Sistema de Recompensas**: Troca de pontos por benefícios
- ✅ **Histórico de Atividades**: Transações de pontos e atividades recentes
- ✅ **Interface Mobile-First**: Design responsivo otimizado para dispositivos móveis

### 🛠️ Para Administradores
- ✅ **Painel Administrativo**: Dashboard completo com estatísticas do sistema
- ✅ **Gestão de Usuários**: Visualização e gerenciamento de contas
- ✅ **Controle de Pontos Turísticos**: CRUD completo com upload de imagens
- ✅ **Sistema de Conquistas**: Configuração de achievements e validações
- ✅ **Gerenciamento de Recompensas**: CRUD de prêmios e benefícios
- ✅ **Controle de Veículos**: Catálogo administrativo com disponibilidade
- ✅ **Configurações do Sistema**: Parâmetros globais e WhatsApp
- ✅ **Relatórios e Analytics**: Métricas de engajamento e uso

### 📱 Recursos Mobile
- ✅ **Responsividade Total**: Interface otimizada para mobile
- ✅ **Touch Friendly**: Botões e interações adequadas para touch
- ✅ **Performance Otimizada**: Carregamento rápido em dispositivos móveis

## 🏗️ Arquitetura Técnica

### Stack Tecnológica
- **Framework**: Next.js 15 (App Router)
- **Frontend**: React 19 + TypeScript 5.7
- **Styling**: Tailwind CSS 3.4 + Tailwind Forms
- **Icons**: Lucide React 0.460
- **Database**: Supabase (PostgreSQL)
- **Auth**: Supabase Auth + Row Level Security
- **Deployment**: Vercel Ready

### Estrutura do Banco de Dados
```sql
-- Principais tabelas
- profiles (usuários + perfis gamificação)
- levels (níveis do sistema Ponto X)
- achievements (conquistas disponíveis)
- user_achievements (conquistas dos usuários)
- tourist_spots (pontos turísticos)
- check_ins (check-ins dos usuários)
- point_transactions (histórico de pontos)
- rewards (recompensas disponíveis)
- reward_redemptions (resgates)
- vehicles (veículos para aluguel)
- app_settings (configurações do sistema)
```

### Recursos de Segurança
- **Row Level Security (RLS)**: Políticas de acesso por usuário
- **Middleware de Autenticação**: Proteção de rotas sensíveis
- **Validação de Tipos**: TypeScript strict + Zod
- **CSRF Protection**: Next.js built-in
- **SQL Injection Prevention**: Supabase prepared statements

## 🚀 Instalação e Configuração

### Pré-requisitos
- Node.js 18+
- NPM 8+
- Conta Supabase

### Passo a Passo

1. **Clone o repositório**
```bash
git clone <repository-url>
cd mvp-sx
```

2. **Instale as dependências**
```bash
npm install
```

3. **Configure as variáveis de ambiente**
```bash
cp .env.local.example .env.local
```

Edite `.env.local` com suas credenciais:
```env
NEXT_PUBLIC_SUPABASE_URL=sua_url_supabase
NEXT_PUBLIC_SUPABASE_ANON_KEY=sua_chave_anonima
NEXT_PUBLIC_WHATSAPP_NUMBER=558781613421
NEXT_PUBLIC_ADMIN_EMAIL=<EMAIL>
```

4. **Configure o banco Supabase**
```bash
# Execute o schema SQL no Supabase
# 1. Copie o conteúdo de supabase/schema.sql
# 2. Execute no SQL Editor do Supabase
# 3. Execute supabase/seed.sql para dados iniciais
```

5. **Execute o projeto**
```bash
npm run dev
```

Acesse `http://localhost:3000`

## 📱 Funcionalidades Detalhadas

### Sistema de Gamificação Ponto X

**Níveis Disponíveis:**
- 🥉 **Bronze** (0+ pontos): Nível inicial
- 🥈 **Silver** (500+ pontos): 5% desconto + 10 pts bônus
- 🥇 **Gold** (1.500+ pontos): 10% desconto + 20 pts bônus + suporte prioritário
- 💎 **Platinum** (5.000+ pontos): 15% desconto + 50 pts bônus + 2 aluguéis grátis
- 💎 **Diamond** (15.000+ pontos): 25% desconto + 100 pts bônus + 5 aluguéis grátis + acesso VIP

**Tipos de Conquistas:**
- **Check-in**: Visitas a pontos turísticos
- **Rental**: Atividades de aluguel
- **Points**: Acúmulo de pontos
- **Social**: Interações sociais
- **Special**: Eventos especiais
- **Streak**: Sequências de atividades
- **Explorer**: Exploração de locais
- **Champion**: Conquistas épicas

### Integração WhatsApp

O sistema redireciona automaticamente para WhatsApp com mensagem pré-formatada contendo:
- Dados do usuário
- Veículo escolhido
- Duração desejada
- Características do veículo
- Localização de retirada

### Sistema de Check-in

**Validações Implementadas:**
- Geolocalização do usuário
- Validação de proximidade (configurável por ponto)
- Limite de um check-in por dia por local
- Cálculo automático de distância
- Pontos automáticos por check-in

## 🔧 Administração

### Primeiro Acesso Admin

1. Crie uma conta normalmente
2. No Supabase, execute:
```sql
UPDATE profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';
```
3. Faça logout e login novamente
4. Acesse `/admin`

### Configurações Disponíveis

O painel admin permite configurar:
- Número do WhatsApp para aluguéis
- Pontos de bônus por ação
- Limites diários de pontos
- Configurações de check-in
- Informações da empresa
- URLs dos termos e políticas

## 🎨 Design System

### Cores Principais
- **Primary**: Blue (#3b82f6)
- **Secondary**: Green (#22c55e)
- **Níveis**: Bronze, Silver, Gold, Platinum, Diamond

### Componentes Reutilizáveis
- `Button`: Variações primary, secondary, outline, ghost, danger
- `Input`: Com validação e estados de erro
- `Card`: Layout base para conteúdo
- `Modal`: Overlays e confirmações

### Responsividade
- **Mobile-first approach**
- Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
- Grid sistema adaptativo
- Componentes flexíveis

## 📊 Métricas e Analytics

### KPIs Implementados
- Total de usuários registrados
- Usuários ativos (últimos 7 dias)
- Pontos totais acumulados
- Check-ins realizados
- Conquistas desbloqueadas
- Taxa de engajamento
- Crescimento diário

### Relatórios Disponíveis
- Dashboard administrativo em tempo real
- Métricas de gamificação
- Análise de comportamento do usuário
- Performance do sistema

## 🔐 Segurança e Privacidade

### Medidas Implementadas
- **Autenticação Supabase**: OAuth2 + JWT
- **Row Level Security**: Isolamento de dados por usuário
- **Validação de Input**: Sanitização e validação
- **HTTPS Only**: Conexões seguras
- **Password Hashing**: bcrypt via Supabase
- **Rate Limiting**: Proteção contra spam

### Conformidade LGPD
- Coleta mínima de dados pessoais
- Consentimento explícito
- Direito ao esquecimento (funcionalidade admin)
- Transparência no uso dos dados
- Criptografia de dados sensíveis

## 🌐 Internacionalização

### Configuração Brasileira
- **Idioma**: Português brasileiro
- **Moeda**: Real (R$)
- **Data/Hora**: Formato brasileiro
- **Telefone**: Formato brasileiro com máscara
- **CEP**: Validação de código postal
- **Fuso horário**: America/Sao_Paulo

## 🚀 Deploy e Produção

### Vercel (Recomendado)

1. **Conecte o repositório no Vercel**
2. **Configure as variáveis de ambiente**
3. **Deploy automático**

### Variáveis de Produção
```env
NEXT_PUBLIC_SUPABASE_URL=https://seu-projeto.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=sua_chave_publica
NEXT_PUBLIC_APP_URL=https://seudominio.com
NEXT_PUBLIC_WHATSAPP_NUMBER=558781613421
```

### Configurações de Performance
- **Image Optimization**: Next.js automático
- **Static Generation**: Para páginas públicas
- **CDN**: Vercel Edge Network
- **Caching**: Browser cache otimizado

## 📝 Licença e Créditos

**Desenvolvido por**: Vitor Pouza  
**Data**: Janeiro 2025  
**Versão**: 1.0.0  
**Licença**: MIT  

### Bibliotecas Utilizadas
- Next.js - Framework React
- Tailwind CSS - Styling
- Supabase - Backend as a Service
- Lucide React - Ícones
- TypeScript - Tipagem estática

### Recursos Externos
- Geolocalização: Browser Geolocation API
- WhatsApp: wa.me integration
- Mobile: Interface responsiva otimizada

---

**⭐ Este é um MVP completo e funcional, pronto para produção!**

Para suporte ou dúvidas, entre em contato com o desenvolvedor.