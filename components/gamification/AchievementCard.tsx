'use client';

import { 
  Trophy, 
  MapPin, 
  Bike, 
  Coins, 
  Users, 
  Star, 
  Flame, 
  Compass,
  Award,
  Lock,
  CheckCircle,
} from 'lucide-react';
import { Achievement, UserAchievement } from '@/types';

interface AchievementCardProps {
  achievement: Achievement;
  isEarned?: boolean;
  earnedAt?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function AchievementCard({ 
  achievement, 
  isEarned = false, 
  earnedAt,
  size = 'md' 
}: AchievementCardProps) {
  const sizeClasses = {
    sm: {
      container: 'p-3',
      icon: 'w-8 h-8',
      title: 'text-sm',
      desc: 'text-xs',
      points: 'text-xs',
    },
    md: {
      container: 'p-4',
      icon: 'w-10 h-10',
      title: 'text-base',
      desc: 'text-sm',
      points: 'text-sm',
    },
    lg: {
      container: 'p-6',
      icon: 'w-12 h-12',
      title: 'text-lg',
      desc: 'text-base',
      points: 'text-base',
    },
  };

  const classes = sizeClasses[size];

  const getAchievementIcon = (type: Achievement['type']) => {
    const iconMap = {
      'check_in': MapPin,
      'rental': Bike,
      'points': Coins,
      'social': Users,
      'special': Star,
      'streak': Flame,
      'explorer': Compass,
      'champion': Trophy,
    };
    return iconMap[type] || Award;
  };

  const getTypeColor = (type: Achievement['type']) => {
    const colorMap = {
      'check_in': 'text-blue-600 bg-blue-100',
      'rental': 'text-green-600 bg-green-100',
      'points': 'text-yellow-600 bg-yellow-100',
      'social': 'text-purple-600 bg-purple-100',
      'special': 'text-pink-600 bg-pink-100',
      'streak': 'text-red-600 bg-red-100',
      'explorer': 'text-indigo-600 bg-indigo-100',
      'champion': 'text-gold bg-yellow-50',
    };
    return colorMap[type] || 'text-gray-600 bg-gray-100';
  };

  const getTypeLabel = (type: Achievement['type']) => {
    const labelMap = {
      'check_in': 'Check-in',
      'rental': 'Aluguel',
      'points': 'Pontos',
      'social': 'Social',
      'special': 'Especial',
      'streak': 'Sequência',
      'explorer': 'Explorador',
      'champion': 'Campeão',
    };
    return labelMap[type] || 'Conquista';
  };

  const IconComponent = getAchievementIcon(achievement.type);
  const typeColor = getTypeColor(achievement.type);
  const typeLabel = getTypeLabel(achievement.type);

  return (
    <div className={`
      bg-white rounded-lg border transition-all duration-200 
      ${isEarned 
        ? 'border-primary-200 shadow-md hover:shadow-lg' 
        : 'border-gray-200 opacity-60 hover:opacity-80'
      }
      ${classes.container}
    `}>
      <div className="flex items-start space-x-3">
        <div className={`
          rounded-full p-2 flex-shrink-0
          ${isEarned ? typeColor : 'text-gray-400 bg-gray-100'}
        `}>
          <IconComponent className={classes.icon} />
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className={`font-semibold text-gray-900 ${classes.title} flex items-center gap-2`}>
                {achievement.name}
                {isEarned && <CheckCircle className="w-4 h-4 text-green-500" />}
                {!isEarned && <Lock className="w-4 h-4 text-gray-400" />}
              </h3>
              
              <p className={`text-gray-600 mt-1 ${classes.desc}`}>
                {achievement.description}
              </p>

              <div className="flex items-center justify-between mt-2">
                <span className={`
                  inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                  ${isEarned ? typeColor : 'text-gray-500 bg-gray-100'}
                `}>
                  {typeLabel}
                </span>

                <span className={`
                  font-semibold ${classes.points}
                  ${isEarned ? 'text-primary-600' : 'text-gray-500'}
                `}>
                  +{achievement.reward_points} pts
                </span>
              </div>
            </div>
          </div>

          {isEarned && earnedAt && (
            <div className="mt-2 pt-2 border-t border-gray-100">
              <p className="text-xs text-gray-500">
                Conquistado em {new Date(earnedAt).toLocaleDateString('pt-BR', {
                  day: 'numeric',
                  month: 'short',
                  year: 'numeric',
                })}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}