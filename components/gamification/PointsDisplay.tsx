'use client';

import { Coins, TrendingUp, Gift } from 'lucide-react';
import { Profile } from '@/types';

interface PointsDisplayProps {
  profile: Profile;
  showTotal?: boolean;
  showRedeemButton?: boolean;
  size?: 'sm' | 'md' | 'lg';
  onRedeem?: () => void;
}

export function PointsDisplay({ 
  profile, 
  showTotal = false, 
  showRedeemButton = false,
  size = 'md',
  onRedeem 
}: PointsDisplayProps) {
  const sizeClasses = {
    sm: {
      container: 'p-3',
      icon: 'w-5 h-5',
      points: 'text-lg',
      label: 'text-xs',
      total: 'text-sm',
      button: 'px-3 py-1 text-xs',
    },
    md: {
      container: 'p-4',
      icon: 'w-6 h-6',
      points: 'text-2xl',
      label: 'text-sm',
      total: 'text-base',
      button: 'px-4 py-2 text-sm',
    },
    lg: {
      container: 'p-6',
      icon: 'w-8 h-8',
      points: 'text-3xl',
      label: 'text-base',
      total: 'text-lg',
      button: 'px-6 py-3 text-base',
    },
  };

  const classes = sizeClasses[size];

  const formatPoints = (points: number) => {
    return points.toLocaleString('pt-BR');
  };

  return (
    <div className={`bg-white rounded-lg border ${classes.container}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="bg-primary-100 rounded-full p-2">
            <Coins className={`text-primary-600 ${classes.icon}`} />
          </div>
          
          <div>
            <p className={`text-gray-600 ${classes.label}`}>
              Pontos Disponíveis
            </p>
            <p className={`font-bold text-primary-600 ${classes.points}`}>
              {formatPoints(profile.points)}
            </p>
          </div>
        </div>

        {showRedeemButton && (
          <button
            onClick={onRedeem}
            className={`
              bg-secondary-600 text-white rounded-lg hover:bg-secondary-700 
              transition-colors font-medium flex items-center space-x-2
              ${classes.button}
            `}
          >
            <Gift className="w-4 h-4" />
            <span>Resgatar</span>
          </button>
        )}
      </div>

      {showTotal && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4 text-gray-500" />
              <span className={`text-gray-600 ${classes.label}`}>
                Total Acumulado
              </span>
            </div>
            <span className={`font-semibold text-gray-900 ${classes.total}`}>
              {formatPoints(profile.total_points_earned)}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}