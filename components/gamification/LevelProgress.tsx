'use client';

import { Trophy, Star } from 'lucide-react';
import { LevelProgress as LevelProgressType } from '@/types';

interface LevelProgressProps {
  levelProgress: LevelProgressType;
  showDetails?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function LevelProgress({ 
  levelProgress, 
  showDetails = true, 
  size = 'md' 
}: LevelProgressProps) {
  const { currentLevel, nextLevel, progress, pointsToNext } = levelProgress;

  const sizeClasses = {
    sm: {
      container: 'p-3',
      title: 'text-sm',
      level: 'text-lg',
      progress: 'h-2',
      details: 'text-xs',
    },
    md: {
      container: 'p-4',
      title: 'text-base',
      level: 'text-xl',
      progress: 'h-3',
      details: 'text-sm',
    },
    lg: {
      container: 'p-6',
      title: 'text-lg',
      level: 'text-2xl',
      progress: 'h-4',
      details: 'text-base',
    },
  };

  const classes = sizeClasses[size];
  
  const getLevelColor = (levelName: string) => {
    const colors: Record<string, string> = {
      'Bronze': '#cd7f32',
      'Silver': '#c0c0c0',
      'Gold': '#ffd700',
      'Platinum': '#e5e4e2',
      'Diamond': '#b9f2ff',
    };
    return colors[levelName] || '#6b7280';
  };

  return (
    <div className={`bg-white rounded-lg border ${classes.container}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <div 
            className="w-8 h-8 rounded-full flex items-center justify-center"
            style={{ backgroundColor: getLevelColor(currentLevel.name) }}
          >
            <Trophy className="w-4 h-4 text-white" />
          </div>
          <div>
            <h3 className={`font-semibold text-gray-900 ${classes.title}`}>
              Nível Atual
            </h3>
            <p 
              className={`font-bold ${classes.level}`}
              style={{ color: getLevelColor(currentLevel.name) }}
            >
              {currentLevel.name}
            </p>
          </div>
        </div>

        {nextLevel && (
          <div className="text-right">
            <p className={`text-gray-500 ${classes.details}`}>Próximo nível</p>
            <p 
              className={`font-semibold ${classes.details}`}
              style={{ color: getLevelColor(nextLevel.name) }}
            >
              {nextLevel.name}
            </p>
          </div>
        )}
      </div>

      {nextLevel ? (
        <>
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>Progresso</span>
              <span>{progress}%</span>
            </div>
            
            <div className={`bg-gray-200 rounded-full ${classes.progress}`}>
              <div
                className={`bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full ${classes.progress} transition-all duration-500 ease-out`}
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>

          {showDetails && (
            <div className="mt-3 text-center">
              <p className={`text-gray-600 ${classes.details}`}>
                <strong>{pointsToNext.toLocaleString('pt-BR')}</strong> pontos para o próximo nível
              </p>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-4">
          <Star className="w-8 h-8 text-gold mx-auto mb-2" />
          <p className={`font-semibold text-gold ${classes.details}`}>
            Nível Máximo Alcançado!
          </p>
          <p className={`text-gray-600 ${classes.details}`}>
            Parabéns, você é um verdadeiro campeão!
          </p>
        </div>
      )}

      {showDetails && currentLevel.description && (
        <div className="mt-3 pt-3 border-t border-gray-100">
          <p className={`text-gray-600 ${classes.details}`}>
            {currentLevel.description}
          </p>
        </div>
      )}
    </div>
  );
}