'use client';

import { useState } from 'react';
import { MapPin, Star, Navigation, Loader2 } from 'lucide-react';
import { TouristSpot } from '@/types';
import { Button } from '@/components/ui/Button';
import { useTouristSpots } from '@/hooks/useTouristSpots';


interface TouristSpotCardProps {
  spot: TouristSpot;
  userLocation?: { lat: number; lng: number };
  onCheckIn?: (spotId: string, userLat: number, userLng: number) => Promise<any>;
  hasCheckedInToday?: boolean;
}

export function TouristSpotCard({ 
  spot, 
  userLocation, 
  onCheckIn,
  hasCheckedInToday = false 
}: TouristSpotCardProps) {
  const [checkingIn, setCheckingIn] = useState(false);

  const { performCheckIn, checkInLoading } = useTouristSpots();

  const handleCheckInClick = async () => {
    if (!userLocation) return;

    try {
      await performCheckIn(spot.id);
      // Aqui você pode atualizar estado local se precisar
    } catch (error: any) {
      alert(error.message);
    }
  };


  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number) => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c * 1000; // Convert to meters
  };

  const distance = userLocation ? calculateDistance(
    userLocation.lat, 
    userLocation.lng, 
    parseFloat(spot.latitude.toString()), 
    parseFloat(spot.longitude.toString())
  ) : null;

  const isInRange = distance ? distance <= spot.check_in_radius : false;

  const handleCheckIn = async () => {
    if (!onCheckIn || !userLocation) return;

    setCheckingIn(true);
    try {
      await onCheckIn(spot.id, userLocation.lat, userLocation.lng);
    } catch (error) {
      console.error('Check-in error:', error);
    } finally {
      setCheckingIn(false);
    }
  };

  const openMaps = () => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${spot.latitude},${spot.longitude}`;
    window.open(url, '_blank');
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
      {spot.image_url && (
        <div className="h-48 bg-gray-200 relative">
          <img
            src={spot.image_url}
            alt={spot.name}
            className="w-full h-full object-cover"
            onError={(e) => {
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
          <div className="absolute top-3 right-3 bg-primary-600 text-white px-2 py-1 rounded-full text-sm font-medium">
            +{spot.points_reward} pts
          </div>
        </div>
      )}

      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900 pr-2">
            {spot.name}
          </h3>
          <div className="flex items-center text-yellow-500">
            <Star className="w-4 h-4 fill-current" />
          </div>
        </div>

        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {spot.description}
        </p>

        {spot.address && (
          <div className="flex items-center text-gray-500 text-sm mb-3">
            <MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
            <span className="truncate">{spot.address}</span>
          </div>
        )}

        {distance !== null && (
          <div className="flex items-center justify-between text-sm mb-4">
            <span className={`
              font-medium
              ${isInRange ? 'text-green-600' : 'text-gray-600'}
            `}>
              {distance < 1000 
                ? `${Math.round(distance)}m de distância`
                : `${(distance / 1000).toFixed(1)}km de distância`
              }
            </span>
            {isInRange && (
              <span className="text-green-600 font-medium">
                Em alcance!
              </span>
            )}
          </div>
        )}

        <div className="flex items-center space-x-2">
          {userLocation && onCheckIn && (
            <Button
              onClick={handleCheckInClick}
              disabled={!isInRange || hasCheckedInToday || checkInLoading}
              size="sm"
              className="flex-1"
              variant={hasCheckedInToday ? 'outline' : 'primary'}
            >
              {checkingIn && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              {hasCheckedInToday 
                ? 'Check-in feito hoje'
                : isInRange 
                  ? 'Fazer Check-in'
                  : 'Fora de alcance'
              }
            </Button>
          )}

          <Button
            onClick={openMaps}
            variant="outline"
            size="sm"
            className="flex-shrink-0"
          >
            <Navigation className="w-4 h-4" />
          </Button>
        </div>

        {!isInRange && distance !== null && (
          <p className="text-xs text-gray-500 mt-2">
            Aproxime-se a menos de {spot.check_in_radius}m para fazer check-in
          </p>
        )}
      </div>
    </div>
  );
}