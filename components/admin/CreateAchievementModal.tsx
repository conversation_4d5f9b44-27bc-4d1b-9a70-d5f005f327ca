'use client';

import { useState } from 'react';
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { X, Trophy } from 'lucide-react';

interface CreateAchievementModalProps {
  onClose: () => void;
  onSubmit: (achievement: any) => Promise<void>;
}

export function CreateAchievementModal({ onClose, onSubmit }: CreateAchievementModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: 'trophy',
    type: 'rental' as 'check_in' | 'rental' | 'points' | 'social' | 'special' | 'streak' | 'explorer' | 'champion',
    reward_points: 100,
    requirement_type: 'rental_count',
    requirement_value: 1,
    category: 'rental',
    is_active: true,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isSubmitting) return; // Prevent double submission
    
    setIsSubmitting(true);

    try {
      // Criar objeto condition baseado nos requirement fields
      let condition: any = {};
      
      switch (formData.requirement_type) {
        case 'rental_count':
          condition = { rental_count: formData.requirement_value };
          break;
        case 'checkin_count':
          condition = { check_ins: formData.requirement_value };
          break;
        case 'points_total':
          condition = { total_points: formData.requirement_value };
          break;
        case 'level':
          condition = { level: formData.requirement_value };
          break;
        case 'consecutive_days':
          condition = { consecutive_days: formData.requirement_value };
          break;
        default:
          condition = { 
            type: formData.requirement_type,
            value: formData.requirement_value 
          };
      }

      const achievementData = {
        ...formData,
        condition: condition
      };

      await onSubmit(achievementData);
      onClose();
    } catch (error) {
      console.error('Erro ao criar conquista:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div onClick={(e) => e.stopPropagation()}>
        <Card className="w-full max-w-2xl max-h-[95vh] overflow-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <h2 className="text-xl font-semibold flex items-center">
            <Trophy className="w-5 h-5 mr-2" />
            Nova Conquista
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome da Conquista
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Primeiro Aluguel"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descrição
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Descreva a conquista..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                rows={3}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo de Conquista
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                >
                  <option value="rental">Aluguel</option>
                  <option value="check_in">Check-in</option>
                  <option value="points">Pontos</option>
                  <option value="social">Social</option>
                  <option value="special">Especial</option>
                  <option value="streak">Sequência</option>
                  <option value="explorer">Explorador</option>
                  <option value="champion">Campeão</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Ícone
                </label>
                <Input
                  type="text"
                  value={formData.icon}
                  onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                  placeholder="trophy, medal, star, etc."
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pontos de Recompensa
                </label>
                <Input
                  type="number"
                  min="1"
                  value={String(formData.reward_points || '')}
                  onChange={(e) => setFormData({ ...formData, reward_points: parseInt(e.target.value) || 0 })}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Categoria
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="rental">Aluguel</option>
                  <option value="checkin">Check-in</option>
                  <option value="social">Social</option>
                  <option value="level">Nível</option>
                  <option value="special">Especial</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo de Requisito
                </label>
                <select
                  value={formData.requirement_type}
                  onChange={(e) => setFormData({ ...formData, requirement_type: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="rental_count">Número de Aluguéis</option>
                  <option value="checkin_count">Número de Check-ins</option>
                  <option value="points_total">Total de Pontos</option>
                  <option value="level">Nível Alcançado</option>
                  <option value="consecutive_days">Dias Consecutivos</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Valor do Requisito
                </label>
                <Input
                  type="number"
                  min="1"
                  value={String(formData.requirement_value || '')}
                  onChange={(e) => setFormData({ ...formData, requirement_value: parseInt(e.target.value) || 0 })}
                  required
                />
              </div>
            </div>

            <div className="flex items-center">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">Ativar conquista imediatamente</span>
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Criando...' : 'Criar Conquista'}
              </Button>
            </div>
          </form>
        </CardContent>
        </Card>
      </div>
    </div>
  );
}