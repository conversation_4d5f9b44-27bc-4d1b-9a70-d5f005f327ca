'use client';

import { useState } from 'react';
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { X, Gift } from 'lucide-react';

interface CreateRewardModalProps {
  onClose: () => void;
  onSubmit: (reward: any) => Promise<void>;
}

export function CreateRewardModal({ onClose, onSubmit }: CreateRewardModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    cost_points: 100,
    type: 'discount' as 'discount' | 'free_rental' | 'merchandise' | 'experience' | 'partner_benefit',
    value: '10%',
    category: 'desconto',
    discount_percentage: 0,
    validity_days: 30,
    max_redemptions: 0,
    is_active: true,
    destaque: false,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isSubmitting) return; // Prevent double submission
    
    setIsSubmitting(true);

    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error('Erro ao criar recompensa:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 "
      onClick={handleBackdropClick}
    >
      
      <div onClick={(e) => e.stopPropagation()} >
        <Card className="w-full max-w-2xl max-h-[95vh] overflow-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <h2 className="text-xl font-semibold flex items-center">
            <Gift className="w-5 h-5 mr-2" />
            Nova Recompensa
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome da Recompensa
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Desconto de 10%"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descrição
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Descreva a recompensa e como usá-la..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                rows={3}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pontos Necessários
                </label>
                <Input
                  type="number"
                  min="1"
                  value={String(formData.cost_points || '')}
                  onChange={(e) => setFormData({ ...formData, cost_points: parseInt(e.target.value) || 0 })}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo de Recompensa
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                >
                  <option value="discount">Desconto</option>
                  <option value="free_rental">Aluguel Grátis</option>
                  <option value="merchandise">Produto</option>
                  <option value="experience">Experiência</option>
                  <option value="partner_benefit">Benefício Parceiro</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Categoria
              </label>
              <select
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="desconto">Desconto</option>
                <option value="produto">Produto</option>
                <option value="experiencia">Experiência</option>
                <option value="digital">Digital</option>
                <option value="especial">Especial</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valor da Recompensa
              </label>
              <Input
                type="text"
                value={formData.value}
                onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                placeholder="Ex: 10%, 2 horas grátis, R$ 50"
                required
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              {formData.category === 'desconto' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Percentual de Desconto
                  </label>
                  <Input
                    type="number"
                    min="1"
                    max="100"
                    value={String(formData.discount_percentage || '')}
                    onChange={(e) => setFormData({ ...formData, discount_percentage: parseInt(e.target.value) || 0 })}
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Validade (dias)
                </label>
                <Input
                  type="number"
                  min="1"
                  value={String(formData.validity_days || '')}
                  onChange={(e) => setFormData({ ...formData, validity_days: parseInt(e.target.value) || 0 })}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Máximo de Resgates
                </label>
                <Input
                  type="number"
                  min="0"
                  value={String(formData.max_redemptions || '')}
                  onChange={(e) => setFormData({ ...formData, max_redemptions: parseInt(e.target.value) || 0 })}
                  placeholder="0 = ilimitado"
                />
              </div>
            </div>
           
            <div className="flex items-center">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.destaque}
                  onChange={(e) => setFormData({ ...formData, destaque: e.target.checked })}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">Marcar como Destaque</span>
              </label>
            </div>

            <div className="flex items-center">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">Ativar recompensa imediatamente</span>
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Criando...' : 'Criar Recompensa'}
              </Button>
            </div>
          </form>
        </CardContent>
        </Card>
      </div>
    </div>
  );
}