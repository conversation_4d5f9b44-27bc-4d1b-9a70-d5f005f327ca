'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { X, Gift } from 'lucide-react';

interface Reward {
  id: string;
  name: string;
  description: string;
  cost_points: number;
  type: 'discount' | 'free_rental' | 'merchandise' | 'experience' | 'partner_benefit';
  value: string;
  image_url?: string;
  is_active: boolean;
  stock?: number;
  valid_until?: string;
  destaque: boolean;
}

interface EditRewardModalProps {
  reward: Reward;
  onClose: () => void;
  onSubmit: (rewardId: string, updatedData: Partial<Reward>) => Promise<void>;
}

export function EditRewardModal({ reward, onClose, onSubmit }: EditRewardModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: reward.name,
    description: reward.description,
    cost_points: reward.cost_points,
    type: reward.type,
    value: reward.value,
    image_url: reward.image_url || '',
    is_active: reward.is_active,
    stock: reward.stock || 0,
    valid_until: reward.valid_until ? reward.valid_until.split('T')[0] : '', // Format for date input
    destaque: reward.destaque,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isSubmitting) return; // Prevent double submission
    
    setIsSubmitting(true);

    try {
      // Prepare data for submission
      const submitData: Partial<Reward> = {
        ...formData,
        stock: formData.stock === 0 ? undefined : formData.stock, // Convert 0 to undefined for unlimited stock
        valid_until: formData.valid_until ? new Date(formData.valid_until).toISOString() : undefined,
      };
      
      await onSubmit(reward.id, submitData);
      onClose();
    } catch (error) {
      console.error('Erro ao atualizar recompensa:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div onClick={(e) => e.stopPropagation()}>
        <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <h2 className="text-xl font-semibold flex items-center">
            <Gift className="w-5 h-5 mr-2" />
            Editar Recompensa
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome da Recompensa
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Desconto de 10%"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descrição
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Descreva a recompensa e como usá-la..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
                rows={3}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pontos Necessários
                </label>
                <Input
                  type="number"
                  min="1"
                  value={String(formData.cost_points || '')}
                  onChange={(e) => setFormData({ ...formData, cost_points: parseInt(e.target.value) || 0 })}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo de Recompensa
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                >
                  <option value="discount">Desconto</option>
                  <option value="free_rental">Aluguel Grátis</option>
                  <option value="merchandise">Produto</option>
                  <option value="experience">Experiência</option>
                  <option value="partner_benefit">Benefício Parceiro</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valor da Recompensa
              </label>
              <Input
                type="text"
                value={formData.value}
                onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                placeholder="Ex: 10%, 2 horas grátis, R$ 50"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Para descontos: use &quot;10%&quot;. Para valores fixos: use &quot;R$ 50&quot;. Para tempo: use &quot;2 horas&quot;
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                URL da Imagem
              </label>
              <Input
                type="url"
                value={formData.image_url}
                onChange={(e) => setFormData({ ...formData, image_url: e.target.value })}
                placeholder="https://exemplo.com/imagem.jpg"
              />
              <p className="text-xs text-gray-500 mt-1">
                Opcional: Adicione uma imagem representativa da recompensa
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Estoque Disponível
                </label>
                <Input
                  type="number"
                  min="0"
                  value={String(formData.stock || '')}
                  onChange={(e) => setFormData({ ...formData, stock: parseInt(e.target.value) || 0 })}
                  placeholder="0 = ilimitado"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Deixe 0 para estoque ilimitado
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Válido Até
                </label>
                <Input
                  type="date"
                  value={formData.valid_until}
                  onChange={(e) => setFormData({ ...formData, valid_until: e.target.value })}
                  min={new Date().toISOString().split('T')[0]} // Prevent past dates
                />
                <p className="text-xs text-gray-500 mt-1">
                  Opcional: Data limite para resgatar esta recompensa
                </p>
              </div>
            </div>

            {/* Marcar como destaque */}
            <div className="flex items-center">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.destaque}
                  onChange={(e) => setFormData({ ...formData, destaque: e.target.checked })}
                  className="mr-2 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">Marcar como Destaque</span>
              </label>
            </div>
            

            <div className="flex items-center">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  className="mr-2 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">Recompensa ativa</span>
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Salvando...' : 'Salvar Alterações'}
              </Button>
            </div>
          </form>
        </CardContent>
        </Card>
      </div>
    </div>
  );
}