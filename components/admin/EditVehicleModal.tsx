'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON>Header, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { X, Car } from 'lucide-react';

interface Vehicle {
  id: string;
  name: string;
  description: string;
  type: string;
  hourly_price: number;
  daily_price?: number;
  location?: string;
  latitude?: number;
  longitude?: number;
  image_url?: string;
  is_available: boolean;
  features?: any;
  vehicle_whatsapp?: string;
  destaque: boolean;
}

interface EditVehicleModalProps {
  vehicle: Vehicle;
  onClose: () => void;
  onSubmit: (vehicleId: string, updatedData: Partial<Vehicle>) => Promise<void>;
}

export function EditVehicleModal({ vehicle, onClose, onSubmit }: EditVehicleModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: vehicle.name,
    description: vehicle.description,
    type: vehicle.type,
    hourly_price: vehicle.hourly_price,
    daily_price: vehicle.daily_price || 0,
    location: vehicle.location || '',
    latitude: vehicle.latitude || 0,
    longitude: vehicle.longitude || 0,
    image_url: vehicle.image_url || '',
    is_available: vehicle.is_available,
    vehicle_whatsapp: vehicle.vehicle_whatsapp || '',
    destaque: vehicle.destaque,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isSubmitting) return; // Prevent double submission
    
    setIsSubmitting(true);

    try {
      await onSubmit(vehicle.id, formData);
      onClose();
    } catch (error) {
      console.error('Erro ao atualizar veículo:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div onClick={(e) => e.stopPropagation()}>
        <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <h2 className="text-xl font-semibold flex items-center">
            <Car className="w-5 h-5 mr-2" />
            Editar Veículo
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome do Veículo
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Patinete Elétrico Pro"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descrição
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Descreva o veículo..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
                rows={3}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="scooter">Patinete</option>
                  <option value="bike">Bicicleta</option>
                  <option value="e-bike">E-Bike</option>
                  <option value="skateboard">Skate</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preço por Hora (R$)
                </label>
                <Input
                  type="number"
                  min="1"
                  step="0.01"
                  value={String(formData.hourly_price || '')}
                  onChange={(e) => setFormData({ ...formData, hourly_price: parseFloat(e.target.value) || 0 })}
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preço Diário (R$) - Opcional
              </label>
              <Input
                type="number"
                min="0"
                step="0.01"
                value={String(formData.daily_price || '')}
                onChange={(e) => setFormData({ ...formData, daily_price: parseFloat(e.target.value) || 0 })}
                placeholder="0.00"
              />
              <p className="text-xs text-gray-500 mt-1">
                Deixe 0 se não oferece aluguel diário
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Localização
              </label>
              <Input
                type="text"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                placeholder="Ex: Centro, Zona Sul"
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Latitude
                </label>
                <Input
                  type="number"
                  step="0.000001"
                  value={String(formData.latitude || '')}
                  onChange={(e) => setFormData({ ...formData, latitude: parseFloat(e.target.value) || 0 })}
                  placeholder="-23.550520"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Longitude
                </label>
                <Input
                  type="number"
                  step="0.000001"
                  value={String(formData.longitude || '')}
                  onChange={(e) => setFormData({ ...formData, longitude: parseFloat(e.target.value) || 0 })}
                  placeholder="-46.633308"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Whatsapp
              </label>
              <Input
                type="tel"
                inputMode="numeric"
                pattern="[0-9]*"
                value={formData.vehicle_whatsapp}
                onChange={(e) =>setFormData({...formData,vehicle_whatsapp: e.target.value.replace(/\D/g, ""),})}
                placeholder="Ex: 5587999999999"
            />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                URL da Imagem
              </label>
              <Input
                type="url"
                value={formData.image_url}
                onChange={(e) => setFormData({ ...formData, image_url: e.target.value })}
                placeholder="https://exemplo.com/imagem.jpg"
              />
              <p className="text-xs text-gray-500 mt-1">
                Opcional: Adicione uma imagem do veículo
              </p>
            </div>

            <div className="flex items-center">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.destaque}
                    onChange={(e) => setFormData({ ...formData, destaque: e.target.checked })}
                    className="mr-2 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Marcar como Destaque</span>
                </label>
              </div>

            <div className="flex items-center">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.is_available}
                  onChange={(e) => setFormData({ ...formData, is_available: e.target.checked })}
                  className="mr-2 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">Disponível para aluguel</span>
              </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Salvando...' : 'Salvar Alterações'}
              </Button>
            </div>
          </form>
        </CardContent>
        </Card>
      </div>
    </div>
  );
}