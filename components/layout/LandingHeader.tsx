'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/Button';
import { User, Settings, LogOut, ChevronDown } from 'lucide-react';
import { useState } from 'react';
import { usePathname } from 'next/navigation';

export function LandingHeader() {
  const { user, profile, signOut } = useAuth();
  const [dropdownOpen, setDropdownOpen] = useState(false);
   const pathname = usePathname(); // hook para pegar a rota atual

  const handleSignOut = async () => {
    try {
      await signOut();
      window.location.href = '/';
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
      window.location.href = '/';
    }
  };

  return (
    <header className="bg-white/95 backdrop-blur-sm border-b border-gray-100 sticky top-0 z-50">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <Image
              src="/assets/logo-2.png"
              alt="Trivvy"
              width={80}
              height={65}
              className="rounded-md"
            />
          </Link>

          {/* Standard Navigation */}
          {!user && pathname === '/' &&  (
            <div className="hidden md:flex items-center space-x-6">
              <Link 
                href="#veiculos" 
                className="text-gray-700 hover:text-primary-600 transition-colors"
              >
                Veículos
              </Link>
              <Link 
                href="#pontos-turisticos" 
                className="text-gray-700 hover:text-primary-600 transition-colors"
              >
                Pontos Turísticos
              </Link>
              <Link 
                href="#funcionamento" 
                className="text-gray-700 hover:text-primary-600 transition-colors"
              >
                Funcionamento
              </Link>
            </div>
          )}
          
          {/* Navigation */}
          <div className="flex items-center space-x-6">
            {user && profile ? (
              // Usuário logado
              <div className="flex items-center space-x-4">
                {/* Links rápidos */}
                <div className="hidden md:flex items-center space-x-4 text-sm">
                  <Link 
                    href="/dashboard" 
                    className="text-gray-700 hover:text-primary-600 transition-colors"
                  >
                    Dashboard
                  </Link>
                  <Link 
                    href="/veiculos" 
                    className="text-gray-700 hover:text-primary-600 transition-colors"
                  >
                    Veículos
                  </Link>
                  <Link 
                    href="/dashboard/recompensas" 
                    className="text-gray-700 hover:text-primary-600 transition-colors"
                  >
                    Recompensas
                  </Link>
                </div>

                {/* User dropdown */}
                <div className="relative">
                  <button
                    onClick={() => setDropdownOpen(!dropdownOpen)}
                    className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {(profile.full_name || profile.email || 'U')[0].toUpperCase()}
                      </span>
                    </div>
                    <div className="hidden sm:block text-left">
                      <p className="text-sm font-medium text-gray-900">
                        {profile.full_name || 'Usuário'}
                      </p>
                      <p className="text-xs text-gray-500">
                        {profile.points} pts
                      </p>
                    </div>
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  </button>

                  {/* Dropdown menu */}
                  {dropdownOpen && (
                    <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2">
                      <div className="px-4 py-2 border-b border-gray-100">
                        <p className="text-sm font-medium text-gray-900">
                          {profile.full_name || 'Usuário'}
                        </p>
                        <p className="text-xs text-gray-500">{profile.email}</p>
                        <p className="text-xs text-primary-600 font-medium">
                          {profile.points} pontos • Nível {profile.level}
                        </p>
                      </div>

                      <Link
                        href="/dashboard"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                        onClick={() => setDropdownOpen(false)}
                      >
                        <User className="w-4 h-4 mr-3" />
                        Meu Dashboard
                      </Link>

                      <Link
                        href="/dashboard/perfil"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                        onClick={() => setDropdownOpen(false)}
                      >
                        <Settings className="w-4 h-4 mr-3" />
                        Configurações
                      </Link>

                      {profile.role === 'admin' && (
                        <Link
                          href="/admin"
                          className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                          onClick={() => setDropdownOpen(false)}
                        >
                          <Settings className="w-4 h-4 mr-3" />
                          Painel Admin
                        </Link>
                      )}

                      <hr className="my-2" />

                      <button
                        onClick={handleSignOut}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        <LogOut className="w-4 h-4 mr-3" />
                        Sair
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              // Usuário não logado
              <div className="flex items-center space-x-3">
                <Link href="/login">
                  <Button variant="ghost" size="sm" className="text-gray-700 hover:text-gray-900">
                    Entrar
                  </Button>
                </Link>
                <Link href="/cadastro">
                  <Button size="sm" className="bg-primary-600 hover:bg-primary-700">
                    Cadastrar
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {dropdownOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setDropdownOpen(false)}
        />
      )}
    </header>
  );
}