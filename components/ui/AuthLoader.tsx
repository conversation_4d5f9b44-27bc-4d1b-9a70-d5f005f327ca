'use client';

import { Loader2 } from 'lucide-react';

interface AuthLoaderProps {
  message?: string;
  fullScreen?: boolean;
}

export function AuthLoader({ 
  message = 'Verificando autenticação...', 
  fullScreen = false 
}: AuthLoaderProps) {
  const containerClasses = fullScreen 
    ? 'fixed inset-0 z-50 bg-white flex items-center justify-center'
    : 'flex items-center justify-center min-h-[400px] p-8';

  return (
    <div className={containerClasses}>
      <div className="text-center">
        <div className="relative mb-4">
          <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center shadow-lg mx-auto">
            <span className="text-white font-bold text-xl">Trivvy</span>
          </div>
          <Loader2 className="w-6 h-6 text-primary-600 animate-spin absolute -bottom-1 -right-1 bg-white rounded-full p-1" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Trivvy</h3>
        <p className="text-gray-600 text-sm max-w-sm mx-auto">
          {message}
        </p>
        <div className="mt-4 flex justify-center">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-primary-400 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-primary-400 rounded-full animate-pulse delay-75"></div>
            <div className="w-2 h-2 bg-primary-400 rounded-full animate-pulse delay-150"></div>
          </div>
        </div>
      </div>
    </div>
  );
}