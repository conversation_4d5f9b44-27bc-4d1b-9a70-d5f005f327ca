import { LucideIcon, Package, MapPin, Award, Users, Car } from 'lucide-react';
import { Button } from './Button';

interface EmptyStateProps {
  icon?: LucideIcon;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    loading?: boolean;
  };
  variant?: 'default' | 'illustration';
  children?: React.ReactNode;
}

export function EmptyState({ 
  icon: Icon, 
  title, 
  description, 
  action, 
  variant = 'default',
  children 
}: EmptyStateProps) {
  const defaultIcon = Package;
  const IconComponent = Icon || defaultIcon;

  return (
    <div className="text-center py-16 px-6">
      <div className="max-w-md mx-auto">
        {variant === 'illustration' ? (
          <div className="relative mb-8">
            <div className="w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
              <IconComponent className="w-12 h-12 text-gray-400" />
            </div>
            {/* Decorative dots */}
            <div className="absolute -top-2 -right-2 w-4 h-4 bg-primary-100 rounded-full"></div>
            <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-secondary-100 rounded-full"></div>
          </div>
        ) : (
          <div className="mb-6">
            <IconComponent className="mx-auto h-16 w-16 text-gray-300" />
          </div>
        )}
        
        <h3 className="text-xl font-semibold text-gray-900 mb-3">{title}</h3>
        <p className="text-gray-500 text-base leading-relaxed mb-8">{description}</p>
        
        {action && (
          <Button 
            onClick={action.onClick}
            loading={action.loading}
            size="lg"
            className="shadow-lg hover:shadow-xl transition-shadow"
          >
            {action.label}
          </Button>
        )}
        
        {children}
      </div>
    </div>
  );
}

// Componentes especializados para casos comuns
export function EmptyTouristSpots({ onCreateSpot }: { onCreateSpot?: () => void }) {
  return (
    <EmptyState
      icon={MapPin}
      title="Nenhum ponto turístico encontrado"
      description="Ainda não há pontos turísticos cadastrados. Que tal adicionar o primeiro?"
      variant="illustration"
      action={onCreateSpot ? {
        label: "Adicionar Ponto Turístico",
        onClick: onCreateSpot
      } : undefined}
    />
  );
}

export function EmptyAchievements() {
  return (
    <EmptyState
      icon={Award}
      title="Sem conquistas ainda"
      description="Continue explorando pontos turísticos e alugando veículos para desbloquear suas primeiras conquistas!"
      variant="illustration"
    />
  );
}

export function EmptyVehicles({ onAddVehicle }: { onAddVehicle?: () => void }) {
  return (
    <EmptyState
      icon={Car}
      title="Nenhum veículo disponível"
      description="Não há veículos cadastrados no momento. Adicione veículos para começar os aluguéis."
      variant="illustration"
      action={onAddVehicle ? {
        label: "Adicionar Veículo",
        onClick: onAddVehicle
      } : undefined}
    />
  );
}

export function EmptyUsers() {
  return (
    <EmptyState
      icon={Users}
      title="Nenhum usuário encontrado"
      description="Não há usuários cadastrados ou nenhum corresponde aos filtros aplicados."
      variant="illustration"
    />
  );
}