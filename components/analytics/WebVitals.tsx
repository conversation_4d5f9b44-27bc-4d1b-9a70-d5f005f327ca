'use client'

import { useReportWebVitals } from 'next/web-vitals'

interface WebVitalsMetric {
  id: string
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
}

export function WebVitals() {
  useReportWebVitals((metric: WebVitalsMetric) => {
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      //console.log('Web Vitals:', metric)
    }

    // Send to analytics service in production
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
      // Google Analytics 4
      if (window.gtag) {
        window.gtag('event', metric.name, {
          custom_map: { metric_id: 'custom_metric' },
          value: Math.round(metric.value),
          metric_id: metric.id,
          metric_rating: metric.rating,
        })
      }

      // Custom analytics endpoint
      fetch('/api/analytics/web-vitals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(metric),
      }).catch(() => {
        // Fail silently to not affect user experience
      })
    }

    // Performance monitoring thresholds
    const thresholds = {
      CLS: { good: 0.1, poor: 0.25 },
      FID: { good: 100, poor: 300 },
      FCP: { good: 1800, poor: 3000 },
      LCP: { good: 2500, poor: 4000 },
      TTFB: { good: 800, poor: 1800 },
      INP: { good: 200, poor: 500 },
    }

    // Alert on poor performance
    const threshold = thresholds[metric.name as keyof typeof thresholds]
    if (threshold && metric.value > threshold.poor) {
      console.warn(`Poor ${metric.name} performance detected:`, {
        value: metric.value,
        threshold: threshold.poor,
        rating: metric.rating,
      })
    }
  })

  return null
}

// TypeScript declaration for gtag
declare global {
  interface Window {
    gtag?: (
      command: string,
      eventName: string,
      parameters: Record<string, any>
    ) => void
  }
}