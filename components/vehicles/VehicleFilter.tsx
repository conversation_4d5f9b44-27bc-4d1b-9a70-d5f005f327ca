'use client';

import { useState } from 'react';
import { Filter, X, Search } from 'lucide-react';
import { VehicleType } from '@/types';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

interface VehicleFilters {
  type?: VehicleType;
  maxPrice?: number;
  location?: string;
  searchQuery?: string;
}

interface VehicleFilterProps {
  onFiltersChange: (filters: VehicleFilters) => void;
  availableLocations: string[];
  priceRange: { min: number; max: number };
  vehicleTypes: { type: VehicleType; count: number; label: string }[];
}

export function VehicleFilter({ 
  onFiltersChange, 
  availableLocations, 
  priceRange,
  vehicleTypes 
}: VehicleFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState<VehicleFilters>({});

  const updateFilters = (newFilters: Partial<VehicleFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange(updatedFilters);
  };

  const clearFilters = () => {
    setFilters({});
    onFiltersChange({});
  };

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== '' && value !== null
  );

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Search className="w-5 h-5 text-gray-500" />
          <Input
            placeholder="Buscar veículos..."
            value={filters.searchQuery || ''}
            onChange={(e) => updateFilters({ searchQuery: e.target.value })}
            className="max-w-sm"
          />
        </div>

        <div className="flex items-center space-x-2">
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
            >
              <X className="w-4 h-4 mr-1" />
              Limpar
            </Button>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
          >
            <Filter className="w-4 h-4 mr-1" />
            Filtros
            {hasActiveFilters && (
              <span className="ml-1 bg-primary-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {Object.values(filters).filter(Boolean).length}
              </span>
            )}
          </Button>
        </div>
      </div>

      {isOpen && (
        <div className="border-t border-gray-200 pt-4 space-y-4">
          {/* Vehicle Type Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tipo de Veículo
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => updateFilters({ type: undefined })}
                className={`px-3 py-1 rounded-full text-sm transition-colors ${
                  !filters.type
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Todos
              </button>
              {vehicleTypes.map(({ type, count, label }) => (
                <button
                  key={type}
                  onClick={() => updateFilters({ type })}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    filters.type === type
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {label} ({count})
                </button>
              ))}
            </div>
          </div>

          {/* Price Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Preço máximo por hora
            </label>
            <div className="flex items-center space-x-4">
              <input
                type="range"
                min={priceRange.min}
                max={priceRange.max}
                step="5"
                value={filters.maxPrice || priceRange.max}
                onChange={(e) => updateFilters({ maxPrice: Number(e.target.value) })}
                className="flex-1"
              />
              <span className="text-sm font-medium text-gray-700 min-w-16">
                R$ {(filters.maxPrice || priceRange.max).toFixed(2)}
              </span>
            </div>
          </div>

          {/* Location Filter */}
          {availableLocations.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Localização
              </label>
              <select
                value={filters.location || ''}
                onChange={(e) => updateFilters({ location: e.target.value || undefined })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Todas as localizações</option>
                {availableLocations.map((location) => (
                  <option key={location} value={location}>
                    {location}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      )}
    </div>
  );
}