'use client';

import React from 'react';
import { usePublicVehicles } from '@/hooks/usePublicVehicles';
import { useAppSettings } from '@/hooks/useAppSettings';
import { whatsappService } from '@/lib/whatsapp';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { 
  Car, 
  Bike, 
  Zap, 
  MapPin, 
  Clock, 
  Star, 
  ArrowRight,
  Gift,
  Trophy,
  MessageCircle,
  Users,
  Shield,
  Sparkles
} from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const vehicleIcons = {
  scooter: Zap,
  bike: Bike,
  'e-bike': Zap,
  skateboard: Car,
};

const vehicleEmojis = {
  scooter: '🛴',
  bike: '🚲', 
  'e-bike': '⚡',
  skateboard: '🛹',
};

interface VehicleMarketplaceProps {
  showHeader?: boolean;
  maxVehicles?: number;
}

export function VehicleMarketplace({ showHeader = true, maxVehicles = 12 }: VehicleMarketplaceProps) {
  const { vehicles, loading } = usePublicVehicles();
  const { getWhatsAppNumber } = useAppSettings();

  const featuredVehicles = vehicles.slice(0, maxVehicles);

  const handleDirectWhatsApp = (vehicle: any) => {
    const whatsappNumber = vehicle.vehicle_whatsapp ?? getWhatsAppNumber();
    const message = `Enviado via plataforma Trivvy: Olá! Gostaria de alugar o ${vehicle.name}.`;
        // const message = `Olá! Gostaria de alugar o ${vehicle.name} por R$ ${vehicle.hourly_price}/hora. Pode me ajudar?`;

    whatsappService.openWhatsApp(whatsappNumber, message);
  };

  const getVehicleIcon = (type: string) => {
    const Icon = vehicleIcons[type as keyof typeof vehicleIcons] || Car;
    return Icon;
  };

  if (loading) {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="flex justify-center">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </section>
    );
  }

  const handleLocation = (vehicle: any) => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${vehicle.latitude},${vehicle.longitude}`;
    window.open(url, '_blank');
  };

  return (
    <section className="py-12 md:py-20 bg-gradient-to-br from-gray-50 via-white to-primary-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMwMDAwMDAiIGZpbGwtb3BhY2l0eT0iMC4xIj48Y2lyY2xlIGN4PSIzMCIgY3k9IjMwIiByPSIyIi8+PC9nPjwvZz48L3N2Zz4=')] repeat"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative">
        {showHeader && (
          <div className="text-center mb-6 md:mb-16">
            <div className="inline-flex items-center gap-2 bg-primary-50 text-primary-700 px-3 py-1.5 rounded-full text-xs font-medium mb-3 md:mb-6">
              <Sparkles className="w-3 h-3" />
              <span className="hidden sm:inline">Marketplace de Veículos</span>
              <span className="sm:hidden">Veículos</span>
            </div>
                     
            <h2 className="text-xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 md:mb-4 leading-tight px-2">
              <span className="block sm:inline">Passeios e Locação de</span>{' '}
              <span className="text-primary-600 relative">
                veículos
                <div className="absolute -bottom-1 md:-bottom-2 left-0 right-0 h-1.5 md:h-3 bg-primary-200 -z-10 rounded-full"></div>
              </span>
            </h2>
        </div>
        )}

        {/* Vehicle Grid - Mobile-first */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
          {featuredVehicles.map((vehicle) => {
            const Icon = getVehicleIcon(vehicle.type);
            const emoji = vehicleEmojis[vehicle.type as keyof typeof vehicleEmojis] || '🚗';
            
            return (
              <Card 
                key={vehicle.id} 
                className="group hover:shadow-2xl transition-all duration-300 md:duration-500 border-0 bg-white/80 backdrop-blur-sm hover:bg-white md:hover:scale-105 overflow-hidden"
              >
                <CardContent className="p-0">
                  {/* Vehicle Image - Mobile-optimized */}
                  <div className="relative h-40 md:h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center overflow-hidden">
                    {vehicle.image_url ? (
                      <Image
                        src={vehicle.image_url}
                        alt={vehicle.name}
                        fill
                        className="object-contain group-hover:scale-105 transition-transform duration-300 md:duration-500"
                      />

                    ) : (
                      <div className="text-5xl md:text-6xl opacity-80">{emoji}</div>
                    )}
                    
                    {/* Type Badge - Mobile-first */}
                    {/* <div className="absolute top-3 left-3">
                      <Badge variant="secondary" className="bg-white/90 text-gray-700 backdrop-blur-sm text-xs">
                        <Icon className="w-3 h-3 mr-1" />
                        <span className="hidden sm:inline">{vehicle.type}</span>
                      </Badge>
                    </div> */}

                    {/* Availability - Mobile-first */}
                    <div className="absolute top-3 right-3">
                      <Badge variant="success" className="bg-green-500 text-white text-xs">
                        <div className="w-2 h-2 bg-white rounded-full mr-1"></div>
                        <span className="hidden sm:inline">Disponível</span>
                      </Badge>
                    </div>
                  </div>

                  <div className="p-4 md:p-6">
                    {/* Header - Mobile-optimized layout */}
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-3 md:mb-4 gap-2">
                      <div className="min-w-0 flex-1">
                        <h3 className="text-lg md:text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors truncate">
                          {vehicle.name}
                        </h3>
                        {vehicle.location && (
                          <div className="flex items-center gap-1 text-gray-500 text-sm mt-1">
                            <MapPin className="w-3 h-3 flex-shrink-0" />
                            <span className="truncate">{vehicle.location}</span>
                          </div>
                        )}
                      </div>
                      <div className="text-left sm:text-right flex-shrink-0">
                        <div className="text-xl md:text-2xl font-bold text-primary-600">
                          R$ {vehicle.hourly_price}
                        </div>
                        
                      </div>
                    </div>

                    {vehicle.description && (
                      <p className="text-gray-600 text-sm mb-4 md:mb-6 line-clamp-2">
                        {vehicle.description}
                      </p>
                    )}

                    {/* Action Buttons - Mobile-first */}
                    <div className="space-y-2 md:space-y-3">

                      <Button
                        onClick={() => handleLocation(vehicle)}
                        variant="ghost"
                        className="w-full bg-white text-blue-600 font-medium py-3 mb-5 shadow-md hover:bg-gray-100 transition-all duration-300"
                      >
                        <MapPin className="w-4 h-4 mr-2" />
                        Acessar localização no mapa
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>

                      {/* WhatsApp Direct - Touch-friendly */}
                      <Button
                        onClick={() => handleDirectWhatsApp(vehicle)}
                        className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 min-h-[44px] transition-all duration-300"
                      >
                        <MessageCircle className="w-4 h-4 mr-2" />
                        <span className="text-sm md:text-base">Alugar via WhatsApp</span>
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>

                      {/* Gamification CTA - Touch-friendly */}
                      {/* <Link href="/cadastro" className="block">
                        <Button
                          variant="outline"
                          className="w-full border-primary-200 text-primary-600 hover:bg-primary-50 hover:border-primary-300 font-medium py-3 min-h-[44px] transition-all duration-300 group"
                        >
                          <Gift className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                          <span className="text-sm md:text-base">Cadastrar e Ganhar Até 10%</span>
                          <Trophy className="w-4 h-4 ml-2 group-hover:scale-110 transition-transform" />
                        </Button>
                      </Link> */}
                    </div>

                    {/* Benefits Preview - Mobile-optimized */}
                    {/* <div className="mt-3 md:mt-4 p-3 bg-primary-50 rounded-lg border border-primary-100">
                      <div className="text-xs text-primary-700 font-medium mb-1">
                        Com cadastro você ganha:
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 md:gap-2 text-xs text-primary-600">
                        <div>+50 pontos por aluguel</div>
                        <div>Descontos progressivos</div>
                      </div>
                    </div> */}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

      </div>
    </section>
  );
}