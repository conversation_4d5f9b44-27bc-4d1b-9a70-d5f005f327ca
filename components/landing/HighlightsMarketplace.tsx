'use client';

import React from 'react';
import { usePublicVehicles } from '@/hooks/usePublicVehicles';
import { usePublicTouristSpots } from '@/hooks/usePublicTouristSpots';
import { usePublicRewards } from '@/hooks/usePublicRewards';
import { useAppSettings } from '@/hooks/useAppSettings';
import { whatsappService } from '@/lib/whatsapp';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import {
  Car,
  MapPin,
  Star,
  ArrowRight,
  Gift,
  Trophy,
  MessageCircle,
  Navigation,
  Award,
  Camera,
  Crown,
  Coins
} from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';





const vehicleEmojis = {
  scooter: '🛴',
  bike: '🚲',
  'e-bike': '⚡',
  skateboard: '🛹',
};

interface HighlightsMarketplaceProps {
  showHeader?: boolean;
}

export function HighlightsMarketplace({ showHeader = true }: HighlightsMarketplaceProps) {
  const { vehicles, loading: vehiclesLoading } = usePublicVehicles();
  const { touristSpots, loading: spotsLoading } = usePublicTouristSpots();
  const { rewards, loading: rewardsLoading } = usePublicRewards();
  const { getWhatsAppNumber } = useAppSettings();

  const loading = vehiclesLoading || spotsLoading || rewardsLoading;

  // Lógica para destacar os melhores itens (simulando campo "destaque")
  const highlightedVehicles = vehicles
    .filter(v => v.is_available)
    // Selecionar somente os veículos com campo "destaque" marcado como true
    .filter(v => v.destaque)
    .sort((a, b) => a.hourly_price - b.hourly_price) // Ordenar por menor preço
    .slice(0, 2);

  const highlightedSpots = touristSpots
    .filter(s => s.is_active)
    // Selecionar somente os pontos turísticos com campo "destaque" marcado como true
    .filter(s => s.destaque)
    .sort((a, b) => b.points_reward - a.points_reward) // Ordenar por maior recompensa
    .slice(0, 2);

  const highlightedRewards = rewards
    .filter((r: any) => r.is_active)
    // Selecionar somente as recompensas com campo "destaque" marcado como true
    .filter((r: any) => r.destaque)
    .sort((a: any, b: any) => a.cost_points - b.cost_points) // Ordenar por menor custo
    .slice(0, 2);

  const handleDirectWhatsApp = (item: any, type: 'vehicle' | 'spot') => {
    const whatsappNumber = item.vehicle_whatsapp || item.spots_whatsapp || getWhatsAppNumber();
    let message = '';

    if (type === 'vehicle') {
      message = `Enviado via plataforma Trivvy: Olá! Gostaria de alugar o ${item.name}.`;
    } else {
      message = `Olá! Gostaria de saber mais sobre o ponto turístico ${item.name}. Pode me ajudar com informações sobre como chegar?`;
    }

    whatsappService.openWhatsApp(whatsappNumber, message);
  };

  const handleLocation = (item: any) => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${item.latitude},${item.longitude}`;
    window.open(url, '_blank');
  };



  if (loading) {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="flex justify-center">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </section>
    );
  }

  // Conta quantidade de veículos, pontos turísticos e recompensas marcados como destaque. Se for igual a zero, retorna null
  const totalHighlighted = highlightedVehicles.length + highlightedSpots.length + highlightedRewards.length;
  if (totalHighlighted === 0) {
    return null;
  }

  return (
    <section className="py-12 md:py-20 bg-gradient-to-br from-purple-50 via-white to-pink-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMwMDAwMDAiIGZpbGwtb3BhY2l0eT0iMC4xIj48Y2lyY2xlIGN4PSIzMCIgY3k9IjMwIiByPSIyIi8+PC9nPjwvZz48L3N2Zz4=')] repeat"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative">
        {showHeader && (
          <div className="text-center mb-6 md:mb-16">
            <div className="inline-flex items-center gap-2 bg-purple-50 text-purple-700 px-3 py-1.5 rounded-full text-xs font-medium mb-3 md:mb-6">
              <Crown className="w-3 h-3" />
              <span className="hidden sm:inline">Destaques da Plataforma</span>
              <span className="sm:hidden">Destaques</span>
            </div>

            <h2 className="text-xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 md:mb-4 leading-tight px-2">
              <span className="block sm:inline">Desbloqueie recompensas</span>{' '}
              <span className="text-purple-600 relative">
                Gratis
                <div className="absolute -bottom-1 md:-bottom-2 left-0 right-0 h-1.5 md:h-3 bg-purple-200 -z-10 rounded-full"></div>
              </span>
            </h2>
            {/* <p className="text-gray-600 text-sm md:text-base max-w-2xl mx-auto">
              Descubra os veículos mais econômicos, pontos turísticos com maiores recompensas e as melhores ofertas disponíveis
            </p> */}
          </div>
        )}

        {/* Highlights Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
          {/* Highlighted Vehicles */}
          {highlightedVehicles.map((vehicle) => {
            const emoji = vehicleEmojis[vehicle.type as keyof typeof vehicleEmojis] || '🚗';

            return (
              <Card
                key={`vehicle-${vehicle.id}`}
                className="group hover:shadow-2xl transition-all duration-300 md:duration-500 border-0 bg-white/80 backdrop-blur-sm hover:bg-white md:hover:scale-105 overflow-hidden"
              >
                <CardContent className="p-0">
                  {/* Vehicle Image */}
                  <div className="relative h-40 md:h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center overflow-hidden">
                    {vehicle.image_url ? (
                      <Image
                        src={vehicle.image_url}
                        alt={vehicle.name}
                        fill
                        className="object-contain group-hover:scale-105 transition-transform duration-300 md:duration-500"
                      />
                    ) : (
                      <div className="text-5xl md:text-6xl opacity-80">{emoji}</div>
                    )}

                    {/* Highlight Badge */}
                    <div className="absolute top-3 left-3">
                      <Badge variant="secondary" className="bg-purple-500 text-white backdrop-blur-sm text-xs">
                        <Star className="w-3 h-3 mr-1" />
                        <span className="hidden sm:inline">Melhor Preço</span>
                        <span className="sm:hidden">Top</span>
                      </Badge>
                    </div>

                    {/* Availability */}
                    <div className="absolute top-3 right-3">
                      <Badge variant="success" className="bg-green-500 text-white text-xs">
                        <div className="w-2 h-2 bg-white rounded-full mr-1"></div>
                        <span className="hidden sm:inline">Disponível</span>
                      </Badge>
                    </div>
                  </div>

                  <div className="p-4 md:p-6">
                    {/* Header */}
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-3 md:mb-4 gap-2">
                      <div className="min-w-0 flex-1">
                        <h3 className="text-lg md:text-xl font-bold text-gray-900 group-hover:text-purple-600 transition-colors truncate">
                          {vehicle.name}
                        </h3>
                        {vehicle.location && (
                          <div className="flex items-center gap-1 text-gray-500 text-sm mt-1">
                            <MapPin className="w-3 h-3 flex-shrink-0" />
                            <span className="truncate">{vehicle.location}</span>
                          </div>
                        )}
                      </div>
                      <div className="text-left sm:text-right flex-shrink-0">
                        <div className="text-xl md:text-2xl font-bold text-purple-600">
                          R$ {vehicle.hourly_price}
                        </div>
                        <div className="text-sm text-gray-500">por hora</div>
                      </div>
                    </div>

                    {vehicle.description && (
                      <p className="text-gray-600 text-sm mb-4 md:mb-6 line-clamp-2">
                        {vehicle.description}
                      </p>
                    )}

                    {/* Action Buttons */}
                    <div className="space-y-2 md:space-y-3">
                      <Button
                        onClick={() => handleLocation(vehicle)}
                        variant="ghost"
                        className="w-full bg-white text-blue-600 font-medium py-3 mb-5 shadow-md hover:bg-gray-100 transition-all duration-300"
                      >
                        <MapPin className="w-4 h-4 mr-2" />
                        Acessar localização no mapa
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>

                      <Button
                        onClick={() => handleDirectWhatsApp(vehicle, 'vehicle')}
                        className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 min-h-[44px] transition-all duration-300"
                      >
                        <MessageCircle className="w-4 h-4 mr-2" />
                        <span className="text-sm md:text-base">Alugar via WhatsApp</span>
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}

          {/* Highlighted Tourist Spots */}
          {highlightedSpots.map((spot) => {
            return (
              <Card
                key={`spot-${spot.id}`}
                className="group hover:shadow-2xl transition-all duration-300 md:duration-500 border-0 bg-white/80 backdrop-blur-sm hover:bg-white md:hover:scale-105 overflow-hidden"
              >
                <CardContent className="p-0">
                  {/* Spot Image */}
                  <div className="relative h-40 md:h-48 bg-gradient-to-br from-cyan-100 to-blue-200 flex items-center justify-center overflow-hidden">
                    {spot.image_url ? (
                      <Image
                        src={spot.image_url}
                        alt={spot.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                    ) : (
                      <div className="text-5xl md:text-6xl opacity-80">📍</div>
                    )}

                    {/* Highlight Badge */}
                    <div className="absolute top-3 left-3">
                      <Badge variant="secondary" className="bg-cyan-500 text-white backdrop-blur-sm text-xs">
                        <Trophy className="w-3 h-3 mr-1" />
                        <span className="hidden sm:inline">Maior Recompensa</span>
                        <span className="sm:hidden">Top</span>
                      </Badge>
                    </div>

                    {/* Check-in Available */}
                    <div className="absolute top-3 right-3">
                      <Badge variant="success" className="bg-cyan-500 text-white text-xs">
                        <Camera className="w-3 h-3 mr-1" />
                        <span className="hidden sm:inline">Check-in</span>
                      </Badge>
                    </div>
                  </div>

                  <div className="p-4 md:p-6">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-3 md:mb-4 gap-2">
                      <div className="min-w-0 flex-1">
                        <h3 className="text-lg md:text-xl font-bold text-gray-900 group-hover:text-cyan-600 transition-colors truncate">
                          {spot.name}
                        </h3>
                        {spot.address && (
                          <div className="flex items-center gap-1 text-gray-500 text-sm mt-1">
                            <MapPin className="w-3 h-3 flex-shrink-0" />
                            <span className="truncate">{spot.address}</span>
                          </div>
                        )}
                      </div>
                      <div className="text-left sm:text-right flex-shrink-0">
                        <div className="text-xl md:text-2xl font-bold text-cyan-600">
                          +{spot.points_reward}
                        </div>
                        <div className="text-sm text-gray-500">pontos</div>
                      </div>
                    </div>

                    {spot.description && (
                      <p className="text-gray-600 text-sm mb-4 md:mb-6 line-clamp-2">
                        {spot.description}
                      </p>
                    )}

                    {/* Action Buttons */}
                    <div className="space-y-2 md:space-y-3">
                      <Button
                        onClick={() => handleDirectWhatsApp(spot, 'spot')}
                        className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 min-h-[44px] transition-all duration-300"
                      >
                        <MessageCircle className="w-4 h-4 mr-2" />
                        <span className="text-sm md:text-base">Pedir Informações</span>
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>

                      <Button
                        onClick={() => handleLocation(spot)}
                        variant="ghost"
                        className="w-full bg-white text-blue-600 font-medium py-3 shadow-md hover:bg-gray-100 transition-all duration-300"
                      >
                        <MapPin className="w-4 h-4 mr-2" />
                        Acessar localização no mapa
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>

                      <Link href="/cadastro" className="block">
                        <Button
                          variant="outline"
                          className="w-full border-cyan-200 text-cyan-600 hover:bg-cyan-50 hover:border-cyan-300 font-medium py-3 min-h-[44px] transition-all duration-300 group"
                        >
                          <Gift className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                          <span className="text-sm md:text-base">Cadastrar e Fazer Check-in</span>
                          <Award className="w-4 h-4 ml-2 group-hover:scale-110 transition-transform" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}

          {/* Highlighted Rewards */}
          {highlightedRewards.map((reward: any) => {
            return (
              <Card
                key={`reward-${reward.id}`}
                className="group hover:shadow-2xl transition-all duration-300 md:duration-500 border-0 bg-white/80 backdrop-blur-sm hover:bg-white md:hover:scale-105 overflow-hidden"
              >
                <CardContent className="p-0">
                  {/* Reward Image */}
                  <div className="relative h-40 md:h-48 bg-gradient-to-br from-yellow-100 to-orange-200 flex items-center justify-center overflow-hidden">
                    {reward.image_url ? (
                      <Image
                        src={reward.image_url}
                        alt={reward.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                    ) : (
                      <div className="text-5xl md:text-6xl opacity-80">🎁</div>
                    )}

                    {/* Highlight Badge */}
                    <div className="absolute top-3 left-3">
                      <Badge variant="secondary" className="bg-yellow-500 text-white backdrop-blur-sm text-xs">
                        <Coins className="w-3 h-3 mr-1" />
                        <span className="hidden sm:inline">Melhor Oferta</span>
                        <span className="sm:hidden">Top</span>
                      </Badge>
                    </div>

                    {/* Available Badge */}
                    <div className="absolute top-3 right-3">
                      <Badge variant="success" className="bg-green-500 text-white text-xs">
                        <Gift className="w-3 h-3 mr-1" />
                        <span className="hidden sm:inline">Disponível</span>
                      </Badge>
                    </div>
                  </div>

                  <div className="p-4 md:p-6">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-3 md:mb-4 gap-2">
                      <div className="min-w-0 flex-1">
                        <h3 className="text-lg md:text-xl font-bold text-gray-900 group-hover:text-yellow-600 transition-colors truncate">
                          {reward.name}
                        </h3>
                        <div className="flex items-center gap-1 text-gray-500 text-sm mt-1">
                          <Trophy className="w-3 h-3 flex-shrink-0" />
                          <span className="truncate capitalize">{reward.type.replace('_', ' ')}</span>
                        </div>
                      </div>
                      <div className="text-left sm:text-right flex-shrink-0">
                        <div className="text-xl md:text-2xl font-bold text-yellow-600">
                          {reward.cost_points}
                        </div>
                        <div className="text-sm text-gray-500">pontos</div>
                      </div>
                    </div>

                    {reward.description && (
                      <p className="text-gray-600 text-sm mb-4 md:mb-6 line-clamp-2">
                        {reward.description}
                      </p>
                    )}

                    {/* Action Buttons */}
                    <div className="space-y-2 md:space-y-3">
                      <Link href="/cadastro" className="block">
                        <Button
                          className="w-full bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-3 min-h-[44px] transition-all duration-300"
                        >
                          <Gift className="w-4 h-4 mr-2" />
                          <span className="text-sm md:text-base">Cadastrar e Resgatar</span>
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                      </Link>

                      <Link href="/recompensas" className="block">
                        <Button
                          variant="outline"
                          className="w-full border-yellow-200 text-yellow-600 hover:bg-yellow-50 hover:border-yellow-300 font-medium py-3 min-h-[44px] transition-all duration-300"
                        >
                          <Star className="w-4 h-4 mr-2" />
                          <span className="text-sm md:text-base">Ver Todas as Recompensas</span>
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Call to Action */}
        {/* <div className="mt-12 md:mt-16 text-center">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 md:p-8 shadow-lg border border-purple-100">
            <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-4">
              Quer ver mais opções?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Explore nossa plataforma completa com centenas de veículos, pontos turísticos e recompensas exclusivas
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/veiculos">
                <Button className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 min-h-[44px]">
                  <Car className="w-4 h-4 mr-2" />
                  Ver Todos os Veículos
                </Button>
              </Link>
              <Link href="/pontos-turisticos">
                <Button variant="outline" className="border-cyan-200 text-cyan-600 hover:bg-cyan-50 px-6 py-3 min-h-[44px]">
                  <Navigation className="w-4 h-4 mr-2" />
                  Explorar Pontos Turísticos
                </Button>
              </Link>
              <Link href="/recompensas">
                <Button variant="outline" className="border-yellow-200 text-yellow-600 hover:bg-yellow-50 px-6 py-3 min-h-[44px]">
                  <Gift className="w-4 h-4 mr-2" />
                  Ver Recompensas
                </Button>
              </Link>
            </div>
          </div>
        </div> */}
      </div>
    </section>
  );
}