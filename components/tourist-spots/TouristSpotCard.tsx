'use client';

import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { MapPin, Star, Clock } from 'lucide-react';
import { ArrowRight } from 'lucide-react';
import { Trophy, Camera } from 'lucide-react';
import Image from 'next/image';

interface TouristSpot {
  id: string;
  name: string;
  description: string;
  address: string;
  points_reward: number;
  check_in_radius: number;
  image_url?: string;
}

interface CheckIn {
  id: string;
  check_in_time: string;
}

interface TouristSpotCardProps {
  spot: TouristSpot;
  hasVisited: boolean;
  lastCheckIn?: CheckIn;
  onCheckIn: () => void;
  checkInLoading?: boolean;
}

export function TouristSpotCard({ spot, hasVisited, lastCheckIn, onCheckIn, checkInLoading = false }: TouristSpotCardProps) {
  const canCheckIn = () => {
    if (!hasVisited) return true;
    if (!lastCheckIn) return true;
    
    const lastCheckInTime = new Date(lastCheckIn.check_in_time);
    const now = new Date();
    const hoursSinceLastCheckIn = (now.getTime() - lastCheckInTime.getTime()) / (1000 * 60 * 60);
    
    return hoursSinceLastCheckIn >= 24;
  };

  const handleLocation = (spot: any) => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${spot.latitude},${spot.longitude}`;
    window.open(url, '_blank');
  };

  return (
    <Card className={`hover:shadow-lg transition-shadow ${hasVisited ? 'border-primary-200' : ''}`}>
      <CardContent className="p-6">

        {/* Spot image */}
        <div className="relative h-48 bg-gradient-to-br from-cyan-100 to-blue-200 flex items-center justify-center overflow-hidden">
          {spot.image_url ? (
            <Image
              src={spot.image_url}
              alt={spot.name}
              fill
              className="object-cover group-hover:scale-110 transition-transform duration-500"
            />
          ) : (
            <div className="text-6xl opacity-80">📍</div>
          )}
          
          {/* Points Badge */}
          <div className="absolute top-4 left-4">
            <Badge variant="secondary" className="bg-white/90 text-gray-700 backdrop-blur-sm">
              <Trophy className="w-3 h-3 mr-1" />
              +{spot.points_reward} pontos
            </Badge>
          </div>

          {/* Check-in Available */}
          <div className="absolute top-4 right-4">
            <Badge variant="success" className="bg-cyan-500 text-white">
              <Camera className="w-3 h-3 mr-1" />
              Check-in
            </Badge>
          </div>
        </div>

        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center mb-2">
              <MapPin className="w-5 h-5 text-primary-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">{spot.name}</h3>
            </div>
            
            <p className="text-sm text-gray-600 mb-3">{spot.description}</p>
            
            {spot.address && (
              <p className="text-xs text-gray-500 mb-3">{spot.address}</p>
            )}
            
            <div className="flex items-center space-x-3">
              <div className="flex items-center">
                <Star className="w-4 h-4 text-gold mr-1" />
                <span className="text-sm font-medium text-gray-900">
                  {spot.points_reward} pontos
                </span>
              </div>
              
              <Badge 
                variant="outline" 
                size="sm"
                className="bg-blue-100 text-blue-800"
              >
                {spot.check_in_radius}m raio
              </Badge>
            </div>
          </div>
        </div>

        {hasVisited && lastCheckIn && (
          <div className="mb-4 p-3 bg-primary-50 rounded-lg">
            <div className="flex items-center text-sm text-primary-700">
              <Clock className="w-4 h-4 mr-2" />
              Último check-in: {new Date(lastCheckIn.check_in_time).toLocaleDateString('pt-BR')}
            </div>
          </div>
        )}

        <Button
          onClick={() => handleLocation(spot)}
          variant="ghost"
          className="w-full bg-white text-blue-600 font-medium py-3 mb-5 shadow-md hover:bg-gray-100 transition-all duration-300"
        >
          <MapPin className="w-4 h-4 mr-2" />
          Acessar localização no mapa
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>

        <Button
          onClick={onCheckIn}
          disabled={!canCheckIn() || checkInLoading}
          loading={checkInLoading}
          className="w-full"
          variant={hasVisited ? "outline" : "primary"}
        >
          {checkInLoading ? 'Fazendo Check-in...' : (
            hasVisited ? 
              (canCheckIn() ? 'Fazer Check-in Novamente' : 'Aguarde 24h para novo check-in') : 
              'Fazer Check-in'
          )}
        </Button>
      </CardContent>
    </Card>
  );
}