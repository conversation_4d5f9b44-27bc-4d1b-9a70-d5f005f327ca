'use client';

import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Gift, Star, Clock, CheckCircle } from 'lucide-react';

interface Reward {
  id: string;
  name: string;
  description: string;
  cost_points: number;
  type: 'discount' | 'free_rental' | 'merchandise' | 'experience' | 'partner_benefit';
  value: string;
  image_url?: string;
  is_active: boolean;
  stock?: number;
  valid_until?: string;
  created_at: string;
}

interface RewardCardProps {
  reward: Reward;
  canRedeem: boolean;
  alreadyRedeemed: boolean;
  onRedeem: () => void;
}

export function RewardCard({ reward, canRedeem, alreadyRedeemed, onRedeem }: RewardCardProps) {
  const typeColors = {
    discount: 'bg-green-100 text-green-800',
    free_rental: 'bg-blue-100 text-blue-800',
    merchandise: 'bg-purple-100 text-purple-800',
    experience: 'bg-indigo-100 text-indigo-800',
    partner_benefit: 'bg-gold/20 text-gold',
  };

  const getStatusColor = () => {
    if (alreadyRedeemed) return 'border-green-200 bg-green-50';
    if (canRedeem) return 'border-primary-200 hover:border-primary-300';
    return 'border-gray-200 opacity-60';
  };

  const getButtonVariant = () => {
    if (alreadyRedeemed) return 'outline';
    if (canRedeem) return 'primary';
    return 'outline';
  };

  return (
    <Card className={`transition-all duration-200 ${getStatusColor()}`}>
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center mb-2">
              <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                <Gift className="w-5 h-5 text-primary-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{reward.name}</h3>
                {reward.type === 'discount' && (
                  <p className="text-sm text-primary-600 font-medium">
                    {reward.value}% de desconto
                  </p>
                )}
                {reward.type === 'free_rental' && (
                  <p className="text-sm text-primary-600 font-medium">
                    {reward.value} hora{reward.value !== '1' ? 's' : ''} grátis
                  </p>
                )}
              </div>
              {alreadyRedeemed && (
                <CheckCircle className="w-5 h-5 text-green-600 ml-2" />
              )}
            </div>
            
            <p className="text-sm text-gray-600 mb-4">{reward.description}</p>
            
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Star className="w-4 h-4 text-gold mr-1" />
                <span className="text-sm font-medium text-gray-900">
                  {reward.cost_points} pontos
                </span>
              </div>
              
              <Badge 
                variant="outline" 
                size="sm"
                className={typeColors[reward.type]}
              >
                {reward.type}
              </Badge>
            </div>

            {reward.valid_until && (
              <div className="flex items-center text-xs text-gray-500 mb-4">
                <Clock className="w-3 h-3 mr-1" />
                Válido até {new Date(reward.valid_until).toLocaleDateString('pt-BR')}
              </div>
            )}
          </div>
        </div>

        <Button
          onClick={onRedeem}
          disabled={!canRedeem || alreadyRedeemed}
          className="w-full"
          variant={getButtonVariant()}
        >
          {alreadyRedeemed ? 
            'Já Resgatado' : 
            (canRedeem ? 'Resgatar' : 'Pontos Insuficientes')
          }
        </Button>
      </CardContent>
    </Card>
  );
}