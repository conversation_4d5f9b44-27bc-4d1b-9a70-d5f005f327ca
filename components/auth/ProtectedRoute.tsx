'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { AuthLoader } from '@/components/ui/AuthLoader';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
  redirectTo?: string;
  loadingMessage?: string;
}

export function ProtectedRoute({ 
  children, 
  requireAdmin = false, 
  redirectTo = '/login',
  loadingMessage = 'Verificando permissões...'
}: ProtectedRouteProps) {
  const { user, profile, loading, initialized } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Wait for auth to be initialized
    if (!initialized) return;

    // If not authenticated, redirect to login
    if (!user) {
      const currentPath = window.location.pathname;
      const redirectUrl = `${redirectTo}?redirectTo=${encodeURIComponent(currentPath)}`;
      router.replace(redirectUrl);
      return;
    }

    // If admin required but user is not admin
    if (requireAdmin && profile?.role !== 'admin') {
      router.replace('/dashboard');
      return;
    }
  }, [user, profile, loading, initialized, requireAdmin, redirectTo, router]);

  // Show loading while auth is initializing or we're redirecting
  if (!initialized || loading) {
    return <AuthLoader message={loadingMessage} fullScreen />;
  }

  // Show loading if user exists but we're still checking admin status
  if (user && requireAdmin && !profile) {
    return <AuthLoader message="Verificando permissões de administrador..." fullScreen />;
  }

  // If user is not authenticated, show loading while redirecting
  if (!user) {
    return <AuthLoader message="Redirecionando para login..." fullScreen />;
  }

  // If admin required but user is not admin, show loading while redirecting
  if (requireAdmin && profile?.role !== 'admin') {
    return <AuthLoader message="Acesso não autorizado. Redirecionando..." fullScreen />;
  }

  // All checks passed, render children
  return <>{children}</>;
}