'use client';

import { Coins, Trophy, MapPin, TrendingUp, Star, Gift } from 'lucide-react';
import { Profile, UserAchievement, CheckIn } from '@/types';
import { Card, CardContent } from '@/components/ui/Card';

interface StatsCardsProps {
  profile: Profile;
  achievements: UserAchievement[];
  checkIns: CheckIn[];
  totalTouristSpots: number;
}

export function StatsCards({ profile, achievements, checkIns, totalTouristSpots }: StatsCardsProps) {
  const uniqueSpotsVisited = new Set(checkIns.map(ci => ci.tourist_spot_id)).size;
  const explorationPercentage = totalTouristSpots > 0 
    ? Math.round((uniqueSpotsVisited / totalTouristSpots) * 100)
    : 0;

  const stats = [
    {
      name: 'Pontos Disponíveis',
      value: profile.points.toLocaleString('pt-BR'),
      icon: Coins,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: `+${profile.total_points_earned.toLocaleString('pt-BR')} total`,
    },
    {
      name: 'Nível Atual',
      value: profile.level,
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      change: 'Sistema Ponto X',
    },
    {
      name: 'Conquistas',
      value: achievements.length.toString(),
      icon: Trophy,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: 'Desbloqueadas',
    },
    {
      name: 'Exploração',
      value: `${explorationPercentage}%`,
      icon: MapPin,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: `${uniqueSpotsVisited}/${totalTouristSpots} locais`,
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat) => {
        const Icon = stat.icon;
        return (
          <Card key={stat.name} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className={`rounded-full p-2 ${stat.bgColor}`}>
                  <Icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-500">
                    {stat.name}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stat.value}
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    {stat.change}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}