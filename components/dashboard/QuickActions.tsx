'use client';

import Link from 'next/link';
import { MapPin, Bike, Trophy, Gift, Star, TrendingUp } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export function QuickActions() {
  const actions = [
    {
      title: 'Explorar Pontos',
      description: 'Visite pontos turísticos e ganhe pontos',
      icon: MapPin,
      href: '/dashboard/pontos-turisticos',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Alugar Veículo',
      description: 'Escolha um veículo para se locomover',
      icon: Bike,
      href: '/veiculos',
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Ver Conquistas',
      description: 'Acompanhe seu progresso',
      icon: Trophy,
      href: '/dashboard/conquistas',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Resgatar Prêmios',
      description: 'Troque pontos por recompensas',
      icon: Gift,
      href: '/dashboard/recompensas',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ];

  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Star className="w-5 h-5 mr-2" />
          Ações Rápidas
        </h3>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {actions.map((action) => {
            const Icon = action.icon;
            return (
              <Link key={action.title} href={action.href}>
                <div className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer group">
                  <div className="flex items-start space-x-3">
                    <div className={`rounded-full p-2 ${action.bgColor} group-hover:scale-110 transition-transform`}>
                      <Icon className={`h-5 w-5 ${action.color}`} />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900 group-hover:text-primary-600 transition-colors">
                        {action.title}
                      </h4>
                      <p className="text-xs text-gray-500 mt-1">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>

        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-600">
              <TrendingUp className="w-4 h-4 mr-1" />
              <span>Continue ganhando pontos!</span>
            </div>
            <Link href="/dashboard/conquistas">
              <Button variant="ghost" size="sm">
                Ver Progresso
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}