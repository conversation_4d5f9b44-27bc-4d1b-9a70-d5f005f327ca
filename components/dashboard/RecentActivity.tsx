'use client';

import { PointTransaction, UserAchievement, CheckIn } from '@/types';
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/Card';
import { 
  Coins, 
  Trophy, 
  MapPin, 
  Plus, 
  Minus,
  Clock,
  Calendar,
} from 'lucide-react';

interface RecentActivityProps {
  pointTransactions: PointTransaction[];
  userAchievements: UserAchievement[];
  checkIns: CheckIn[];
}

type ActivityItem = {
  id: string;
  type: 'points' | 'achievement' | 'checkin';
  title: string;
  description: string;
  timestamp: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  value?: number;
};

export function RecentActivity({ pointTransactions, userAchievements, checkIns }: RecentActivityProps) {
  // Combine all activities and sort by timestamp
  const activities: ActivityItem[] = [
    // Point transactions
    ...pointTransactions.slice(0, 10).map(transaction => ({
      id: `points-${transaction.id}`,
      type: 'points' as const,
      title: transaction.type === 'earned' ? 'Pontos Ganhos' : 'Pontos Resgatados',
      description: transaction.description,
      timestamp: transaction.created_at,
      icon: transaction.type === 'earned' ? Plus : Minus,
      color: transaction.type === 'earned' ? 'text-green-600' : 'text-red-600',
      bgColor: transaction.type === 'earned' ? 'bg-green-100' : 'bg-red-100',
      value: Math.abs(transaction.points),
    })),
    
    // Achievements
    ...userAchievements.slice(0, 5).map(achievement => ({
      id: `achievement-${achievement.id}`,
      type: 'achievement' as const,
      title: 'Conquista Desbloqueada',
      description: achievement.achievement_id ? 'Nova Conquista!' : 'Conquista',
      timestamp: achievement.earned_at,
      icon: Trophy,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      value: achievement.achievement_id ? 100 : 0,
    })),
    
    // Check-ins
    ...checkIns.slice(0, 5).map(checkin => ({
      id: `checkin-${checkin.id}`,
      type: 'checkin' as const,
      title: 'Check-in Realizado',
      description: checkin.tourist_spot_id ? 'Check-in realizado!' : 'Ponto turístico',
      timestamp: checkin.check_in_time,
      icon: MapPin,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      value: checkin.points_earned,
    })),
  ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, 15);

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.round(diffInHours * 60);
      return `${diffInMinutes} min atrás`;
    } else if (diffInHours < 24) {
      return `${Math.round(diffInHours)} h atrás`;
    } else if (diffInHours < 48) {
      return 'Ontem';
    } else {
      return date.toLocaleDateString('pt-BR', {
        day: 'numeric',
        month: 'short',
      });
    }
  };

  if (activities.length === 0) {
    return (
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Atividade Recente
          </h3>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Nenhuma atividade ainda</p>
            <p className="text-sm text-gray-400 mt-1">
              Comece explorando pontos turísticos ou alugando veículos!
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Clock className="w-5 h-5 mr-2" />
          Atividade Recente
        </h3>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => {
            const Icon = activity.icon;
            return (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className={`rounded-full p-2 ${activity.bgColor} flex-shrink-0`}>
                  <Icon className={`h-4 w-4 ${activity.color}`} />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.title}
                    </p>
                    <div className="flex items-center space-x-2">
                      {activity.value && (
                        <span className={`text-sm font-medium ${activity.color}`}>
                          {activity.type === 'points' && activity.title === 'Pontos Resgatados' ? '-' : '+'}
                          {activity.value}
                          {activity.type !== 'achievement' && ' pts'}
                        </span>
                      )}
                      <span className="text-xs text-gray-500">
                        {formatTimestamp(activity.timestamp)}
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 truncate">
                    {activity.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}