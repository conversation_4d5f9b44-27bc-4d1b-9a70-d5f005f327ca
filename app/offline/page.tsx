'use client';

import { WifiOff, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/Button';

export default function OfflinePage() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="text-center max-w-md">
        <div className="mb-8">
          <WifiOff className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Você está offline
          </h1>
          <p className="text-gray-600 mb-6">
            Parece que você perdeu a conexão com a internet. 
            Verifique sua conexão e tente novamente.
          </p>
        </div>

        <div className="space-y-4">
          <Button
            onClick={() => window.location.reload()}
            className="w-full"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Tentar Novamente
          </Button>

          <div className="text-sm text-gray-500">
            <p>Algumas funcionalidades podem estar disponíveis offline:</p>
            <ul className="mt-2 space-y-1">
              <li>• Visualizar dados salvos</li>
              <li>• Acessar páginas visitadas recentemente</li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-6 h-6 bg-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">Trivvy</span>
            </div>
            <span className="text-sm text-gray-600">Trivvy</span>
          </div>
        </div>
      </div>
    </div>
  );
}