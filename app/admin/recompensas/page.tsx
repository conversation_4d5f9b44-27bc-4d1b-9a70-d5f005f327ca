'use client';

import { useState } from 'react';
import { useRewards } from '@/hooks/useRewards';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { CreateRewardModal } from '@/components/admin/CreateRewardModal';
import { EditRewardModal } from '@/components/admin/EditRewardModal';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { EmptyState } from '@/components/ui/EmptyState';
import { ErrorState } from '@/components/ui/ErrorState';
import { useToast, ToastContainer } from '@/components/ui/Toast';
import { 
  Gift, 
  Plus,
  Search,
  Filter,
  Eye,
  EyeOff,
  Edit,
  Trash2,
  Users,
  Coins,
  TrendingUp,
} from 'lucide-react';

export default function AdminRewardsPage() {
  const { rewards, userRewards, loading, error, createReward, updateReward, deleteReward } = useRewards();
  const { toasts, toast, removeToast } = useToast();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingReward, setEditingReward] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showInactive, setShowInactive] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const categories = ['discount', 'free_rental', 'merchandise', 'experience', 'partner_benefit'];

  const filteredRewards = rewards.filter(reward => {
    const matchesSearch = !searchQuery || 
      reward.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      reward.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = !selectedCategory || reward.type === selectedCategory;
    const matchesStatus = showInactive || reward.is_active;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const rewardStats = rewards.reduce((stats, reward) => {
    const redeemCount = userRewards.filter(ur => ur.reward_id === reward.id).length;
    const pendingCount = userRewards.filter(ur => ur.reward_id === reward.id && ur.status === 'pending').length;
    stats[reward.id] = { redeemed: redeemCount, pending: pendingCount };
    return stats;
  }, {} as Record<string, { redeemed: number; pending: number }>);

  const totalPointsSpent = userRewards.reduce((sum, ur) => sum + (ur.points_spent || 0), 0);

  const handleToggleActive = async (rewardId: string, isActive: boolean) => {
    setActionLoading(rewardId);
    try {
      await updateReward(rewardId, { is_active: !isActive });
      toast.success(
        `Recompensa ${!isActive ? 'ativada' : 'desativada'} com sucesso!`
      );
    } catch (error) {
      console.error('Erro ao atualizar recompensa:', error);
      toast.error('Erro ao atualizar recompensa. Tente novamente.');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async (rewardId: string) => {
    if (window.confirm('Tem certeza que deseja excluir esta recompensa?')) {
      setActionLoading(rewardId);
      try {
        await deleteReward(rewardId);
        toast.success('Recompensa excluída com sucesso!');
      } catch (error) {
        console.error('Erro ao excluir recompensa:', error);
        toast.error('Erro ao excluir recompensa. Tente novamente.');
      } finally {
        setActionLoading(null);
      }
    }
  };

  const handleCreateSuccess = () => {
    setShowCreateModal(false);
    toast.success('Recompensa criada com sucesso!');
  };

  const handleEdit = (reward: any) => {
    setEditingReward(reward);
  };

  const handleEditSuccess = async (rewardId: string, updatedData: any) => {
    try {
      await updateReward(rewardId, updatedData);
      setEditingReward(null);
      toast.success('Recompensa atualizada com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar recompensa:', error);
      toast.error('Erro ao atualizar recompensa. Tente novamente.');
      throw error;
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Card>
          <CardContent className="p-8">
            <ErrorState 
              message={error}
              onRetry={() => window.location.reload()}
            />
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gerenciar Recompensas</h1>
            <p className="text-gray-600">
              Crie e gerencie recompensas para motivar usuários
            </p>
          </div>
          
          <Button onClick={() => setShowCreateModal(true)} className="flex items-center">
            <Plus className="w-4 h-4 mr-2" />
            Nova Recompensa
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <Gift className="w-8 h-8 text-primary-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{rewards.length}</div>
              <div className="text-sm text-gray-600">Total de Recompensas</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Eye className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">
                {rewards.filter(r => r.is_active).length}
              </div>
              <div className="text-sm text-gray-600">Ativas</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Users className="w-8 h-8 text-secondary-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{userRewards.length}</div>
              <div className="text-sm text-gray-600">Resgates Realizados</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Coins className="w-8 h-8 text-gold mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{totalPointsSpent}</div>
              <div className="text-sm text-gray-600">Pontos Gastos Total</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    type="text"
                    placeholder="Buscar recompensas..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Todas as Categorias</option>
                  <option value="discount">Desconto</option>
                  <option value="free_rental">Aluguel Grátis</option>
                  <option value="merchandise">Produto</option>
                  <option value="experience">Experiência</option>
                  <option value="partner_benefit">Benefício Parceiro</option>
                </select>

                <Button
                  variant={showInactive ? "primary" : "outline"}
                  onClick={() => setShowInactive(!showInactive)}
                  className="whitespace-nowrap"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  {showInactive ? 'Todas' : 'Ativas'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Redemptions */}
        {userRewards.length > 0 && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Resgates Recentes
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {userRewards.slice(0, 5).map((userReward) => {
                  const reward = rewards.find(r => r.id === userReward.reward_id);
                  if (!reward) return null;

                  return (
                    <div key={userReward.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                          <Gift className="w-5 h-5 text-primary-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{reward.name}</h4>
                          <p className="text-sm text-gray-600">
                            Resgatado em {new Date(userReward.redeemed_at).toLocaleDateString('pt-BR')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={userReward.status === 'used' ? 'success' : userReward.status === 'approved' ? 'default' : 'warning'}>
                          {userReward.status === 'used' ? 'Utilizada' : userReward.status === 'approved' ? 'Aprovada' : userReward.status === 'expired' ? 'Expirada' : 'Pendente'}
                        </Badge>
                        <span className="text-sm font-medium text-gray-600">
                          {userReward.points_spent} pts
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Rewards Grid */}
        {filteredRewards.length === 0 ? (
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={Gift}
                title="Nenhuma recompensa encontrada"
                description="Tente ajustar os filtros ou criar uma nova recompensa"
                action={{
                  label: "Criar Primeira Recompensa",
                  onClick: () => setShowCreateModal(true)
                }}
              />
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredRewards.map((reward) => {
              const stats = rewardStats[reward.id] || { redeemed: 0, pending: 0 };
              
              return (
                <Card key={reward.id} className={`relative ${!reward.is_active ? 'opacity-60' : ''}`}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                            <Gift className="w-5 h-5 text-primary-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">{reward.name}</h3>
                            <Badge variant={reward.is_active ? 'success' : 'secondary'} size="sm">
                              {reward.is_active ? 'Ativa' : 'Inativa'}
                            </Badge>
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-3">{reward.description}</p>
                        
                        <div className="flex items-center justify-between text-sm mb-2">
                          <span className="text-primary-600 font-medium">
                            {reward.cost_points} pontos
                          </span>
                          <Badge variant="outline" size="sm">
                            {reward.type}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>{stats.redeemed} resgates</span>
                          {stats.pending > 0 && (
                            <span className="text-yellow-600">{stats.pending} pendentes</span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 pt-3 border-t border-gray-100">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleActive(reward.id, reward.is_active)}
                        disabled={actionLoading === reward.id}
                        className="flex-1"
                      >
                        {actionLoading === reward.id ? (
                          <>
                            <LoadingSpinner size="sm" className="mr-1" />
                            {reward.is_active ? 'Desativando...' : 'Ativando...'}
                          </>
                        ) : reward.is_active ? (
                          <>
                            <EyeOff className="w-4 h-4 mr-1" />
                            Desativar
                          </>
                        ) : (
                          <>
                            <Eye className="w-4 h-4 mr-1" />
                            Ativar
                          </>
                        )}
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(reward)}
                        className="text-gray-600 hover:text-primary-600"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(reward.id)}
                        disabled={actionLoading === reward.id}
                        className="text-gray-600 hover:text-red-600"
                      >
                        {actionLoading === reward.id ? (
                          <LoadingSpinner size="sm" />
                        ) : (
                          <Trash2 className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* Create Reward Modal */}
        {showCreateModal && (
          <CreateRewardModal
            onClose={() => setShowCreateModal(false)}
            onSubmit={createReward}
          />
        )}

        {/* Edit Reward Modal */}
        {editingReward && (
          <EditRewardModal
            reward={editingReward}
            onClose={() => setEditingReward(null)}
            onSubmit={handleEditSuccess}
          />
        )}
      </div>

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </AdminLayout>
  );
}