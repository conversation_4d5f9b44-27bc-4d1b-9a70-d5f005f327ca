'use client';

import { useState } from 'react';
import { useAchievements } from '@/hooks/useAchievements';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { AchievementCard } from '@/components/gamification/AchievementCard';
import { CreateAchievementModal } from '@/components/admin/CreateAchievementModal';
import { EditAchievementModal } from '@/components/admin/EditAchievementModal';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { EmptyState } from '@/components/ui/EmptyState';
import { ErrorState } from '@/components/ui/ErrorState';
import { useToast, ToastContainer } from '@/components/ui/Toast';
import { 
  Trophy, 
  Plus,
  Search,
  Filter,
  Eye,
  EyeOff,
  Edit,
  Trash2,
  Users,
} from 'lucide-react';

export default function AdminAchievementsPage() {
  const { achievements, userAchievements, loading, error, createAchievement, updateAchievement, deleteAchievement } = useAchievements();
  const { toasts, toast, removeToast } = useToast();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedAchievement, setSelectedAchievement] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showInactive, setShowInactive] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const categories = ['rental', 'checkin', 'social', 'level', 'special'];

  const filteredAchievements = achievements.filter(achievement => {
    const matchesSearch = !searchQuery || 
      achievement.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      achievement.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = !selectedCategory || achievement.category === selectedCategory;
    const matchesStatus = showInactive || achievement.is_active;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const achievementStats = achievements.reduce((stats, achievement) => {
    const userCount = userAchievements.filter(ua => ua.achievement_id === achievement.id).length;
    stats[achievement.id] = userCount;
    return stats;
  }, {} as Record<string, number>);

  const handleToggleActive = async (achievementId: string, isActive: boolean) => {
    setActionLoading(achievementId);
    try {
      await updateAchievement(achievementId, { is_active: !isActive });
      toast.success(
        `Conquista ${!isActive ? 'ativada' : 'desativada'} com sucesso!`
      );
    } catch (error) {
      console.error('Erro ao atualizar conquista:', error);
      toast.error('Erro ao atualizar conquista. Tente novamente.');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async (achievementId: string) => {
    if (window.confirm('Tem certeza que deseja excluir esta conquista?')) {
      setActionLoading(achievementId);
      try {
        await deleteAchievement(achievementId);
        toast.success('Conquista excluída com sucesso!');
      } catch (error) {
        console.error('Erro ao excluir conquista:', error);
        toast.error('Erro ao excluir conquista. Tente novamente.');
      } finally {
        setActionLoading(null);
      }
    }
  };

  const handleCreateSuccess = () => {
    setShowCreateModal(false);
    toast.success('Conquista criada com sucesso!');
  };

  const handleEdit = (achievement: any) => {
    setSelectedAchievement(achievement);
    setShowEditModal(true);
  };

  const handleEditSuccess = async (achievementData: any) => {
    try {
      await updateAchievement(achievementData.id, achievementData);
      setShowEditModal(false);
      setSelectedAchievement(null);
      toast.success('Conquista atualizada com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar conquista:', error);
      toast.error('Erro ao atualizar conquista. Tente novamente.');
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Card>
          <CardContent className="p-8">
            <ErrorState 
              message={error}
              onRetry={() => window.location.reload()}
            />
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gerenciar Conquistas</h1>
            <p className="text-gray-600">
              Crie e gerencie conquistas para engajar usuários
            </p>
          </div>
          
          <Button onClick={() => setShowCreateModal(true)} className="flex items-center">
            <Plus className="w-4 h-4 mr-2" />
            Nova Conquista
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <Trophy className="w-8 h-8 text-gold mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{achievements.length}</div>
              <div className="text-sm text-gray-600">Total de Conquistas</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Eye className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">
                {achievements.filter(a => a.is_active).length}
              </div>
              <div className="text-sm text-gray-600">Ativas</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <EyeOff className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">
                {achievements.filter(a => !a.is_active).length}
              </div>
              <div className="text-sm text-gray-600">Inativas</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Users className="w-8 h-8 text-primary-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{userAchievements.length}</div>
              <div className="text-sm text-gray-600">Conquistas Desbloqueadas</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    type="text"
                    placeholder="Buscar conquistas..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Todas as Categorias</option>
                  <option value="rental">Aluguel</option>
                  <option value="checkin">Check-in</option>
                  <option value="social">Social</option>
                  <option value="level">Nível</option>
                  <option value="special">Especial</option>
                </select>

                <Button
                  variant={showInactive ? "primary" : "outline"}
                  onClick={() => setShowInactive(!showInactive)}
                  className="whitespace-nowrap"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  {showInactive ? 'Todas' : 'Ativas'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Achievements Grid */}
        {filteredAchievements.length === 0 ? (
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={Trophy}
                title="Nenhuma conquista encontrada"
                description="Tente ajustar os filtros ou criar uma nova conquista"
                action={{
                  label: "Criar Primeira Conquista",
                  onClick: () => setShowCreateModal(true)
                }}
              />
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredAchievements.map((achievement) => (
              <Card key={achievement.id} className={`relative ${!achievement.is_active ? 'opacity-60' : ''}`}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                          <Trophy className="w-5 h-5 text-primary-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{achievement.name}</h3>
                          <Badge variant={achievement.is_active ? 'success' : 'secondary'} size="sm">
                            {achievement.is_active ? 'Ativa' : 'Inativa'}
                          </Badge>
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3">{achievement.description}</p>
                      
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-primary-600 font-medium">
                          {achievement.reward_points} pontos
                        </span>
                        <span className="text-gray-500">
                          {achievementStats[achievement.id] || 0} usuários
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 pt-3 border-t border-gray-100">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleActive(achievement.id, achievement.is_active)}
                      disabled={actionLoading === achievement.id}
                      className="flex-1"
                    >
                      {actionLoading === achievement.id ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-1" />
                          {achievement.is_active ? 'Desativando...' : 'Ativando...'}
                        </>
                      ) : achievement.is_active ? (
                        <>
                          <EyeOff className="w-4 h-4 mr-1" />
                          Desativar
                        </>
                      ) : (
                        <>
                          <Eye className="w-4 h-4 mr-1" />
                          Ativar
                        </>
                      )}
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(achievement)}
                      className="text-gray-600 hover:text-primary-600"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(achievement.id)}
                      disabled={actionLoading === achievement.id}
                      className="text-gray-600 hover:text-red-600"
                    >
                      {actionLoading === achievement.id ? (
                        <LoadingSpinner size="sm" />
                      ) : (
                        <Trash2 className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Create Achievement Modal */}
        {showCreateModal && (
          <CreateAchievementModal
            onClose={() => setShowCreateModal(false)}
            onSubmit={createAchievement}
          />
        )}

        {showEditModal && selectedAchievement && (
          <EditAchievementModal
            achievement={selectedAchievement}
            onClose={() => {
              setShowEditModal(false);
              setSelectedAchievement(null);
            }}
            onSubmit={handleEditSuccess}
          />
        )}
      </div>

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </AdminLayout>
  );
}