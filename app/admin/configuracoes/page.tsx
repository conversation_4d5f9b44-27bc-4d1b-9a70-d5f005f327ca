'use client';

import { useState } from 'react';
import { useAppSettings } from '@/hooks/useAppSettings';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Switch } from '@/components/ui/Switch';
import { 
  Settings, 
  Save,
  Phone,
  MessageCircle,
  Globe,
  Shield,
  Coins,
  Trophy,
  Bell,
  Database,
} from 'lucide-react';

export default function AdminSettingsPage() {
  const { settings, updateSettings, loading } = useAppSettings();
  const [formData, setFormData] = useState({
    whats_app_number: settings?.whats_app_number || '',
    points_per_rental: settings?.points_per_rental || 100,
    points_per_checkin: settings?.points_per_checkin || 50,
    level_multiplier: settings?.level_multiplier || 500,
    max_daily_checkins: settings?.max_daily_checkins || 5,
    rental_cancellation_hours: settings?.rental_cancellation_hours || 24,
    maintenance_mode: settings?.maintenance_mode || false,
    registration_enabled: settings?.registration_enabled || true,
    notifications_enabled: settings?.notifications_enabled || true,
    email_notifications: settings?.email_notifications || true,
    sms_notifications: settings?.sms_notifications || false,
  });

  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await updateSettings(formData);
      // Show success message
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
      // Show error message
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Configurações do Sistema</h1>
            <p className="text-gray-600">
              Gerencie as configurações gerais da plataforma
            </p>
          </div>
          
          <Button 
            onClick={handleSave} 
            disabled={isSaving}
            className="flex items-center"
          >
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? 'Salvando...' : 'Salvar Alterações'}
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* WhatsApp Integration */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <MessageCircle className="w-5 h-5 mr-2" />
                Integração WhatsApp
              </h3>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Phone className="w-4 h-4 inline mr-2" />
                  Número do WhatsApp
                </label>
                <Input
                  type="tel"
                  value={formData.whats_app_number}
                  onChange={(e) => setFormData({ ...formData, whats_app_number: e.target.value })}
                  placeholder="(11) 99999-9999"
                  className="w-full"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Número usado para redirecionamento dos aluguéis
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Gamification Settings */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Trophy className="w-5 h-5 mr-2" />
                Sistema de Pontos
              </h3>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pontos por Aluguel
                </label>
                <Input
                  type="number"
                  min="1"
                  value={formData.points_per_rental}
                  onChange={(e) => setFormData({ ...formData, points_per_rental: parseInt(e.target.value) })}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pontos por Check-in
                </label>
                <Input
                  type="number"
                  min="1"
                  value={formData.points_per_checkin}
                  onChange={(e) => setFormData({ ...formData, points_per_checkin: parseInt(e.target.value) })}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Multiplicador de Nível
                </label>
                <Input
                  type="number"
                  min="100"
                  step="100"
                  value={formData.level_multiplier}
                  onChange={(e) => setFormData({ ...formData, level_multiplier: parseInt(e.target.value) })}
                  className="w-full"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Pontos necessários para subir de nível (nível × multiplicador)
                </p>
              </div>
            </CardContent>
          </Card>

          {/* System Limits */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Shield className="w-5 h-5 mr-2" />
                Limites do Sistema
              </h3>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Máximo Check-ins Diários
                </label>
                <Input
                  type="number"
                  min="1"
                  max="20"
                  value={formData.max_daily_checkins}
                  onChange={(e) => setFormData({ ...formData, max_daily_checkins: parseInt(e.target.value) })}
                  className="w-full"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Limite de check-ins por usuário por dia
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Horas para Cancelamento
                </label>
                <Input
                  type="number"
                  min="1"
                  max="72"
                  value={formData.rental_cancellation_hours}
                  onChange={(e) => setFormData({ ...formData, rental_cancellation_hours: parseInt(e.target.value) })}
                  className="w-full"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Horas antes do aluguel para cancelamento gratuito
                </p>
              </div>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Globe className="w-5 h-5 mr-2" />
                Status do Sistema
              </h3>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Modo Manutenção
                  </label>
                  <p className="text-xs text-gray-500">
                    Desabilita o acesso público ao sistema
                  </p>
                </div>
                <Switch
                  checked={formData.maintenance_mode}
                  onCheckedChange={(checked) => setFormData({ ...formData, maintenance_mode: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Registro de Usuários
                  </label>
                  <p className="text-xs text-gray-500">
                    Permite novos cadastros na plataforma
                  </p>
                </div>
                <Switch
                  checked={formData.registration_enabled}
                  onCheckedChange={(checked) => setFormData({ ...formData, registration_enabled: checked })}
                />
              </div>
            </CardContent>
          </Card>

          {/* Notifications */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Bell className="w-5 h-5 mr-2" />
                Configurações de Notificação
              </h3>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">
                      Notificações Gerais
                    </label>
                    <p className="text-xs text-gray-500">
                      Ativa sistema de notificações
                    </p>
                  </div>
                  <Switch
                    checked={formData.notifications_enabled}
                    onCheckedChange={(checked) => setFormData({ ...formData, notifications_enabled: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">
                      Email
                    </label>
                    <p className="text-xs text-gray-500">
                      Notificações por email
                    </p>
                  </div>
                  <Switch
                    checked={formData.email_notifications}
                    onCheckedChange={(checked) => setFormData({ ...formData, email_notifications: checked })}
                    disabled={!formData.notifications_enabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">
                      SMS
                    </label>
                    <p className="text-xs text-gray-500">
                      Notificações por SMS
                    </p>
                  </div>
                  <Switch
                    checked={formData.sms_notifications}
                    onCheckedChange={(checked) => setFormData({ ...formData, sms_notifications: checked })}
                    disabled={!formData.notifications_enabled}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Info */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Database className="w-5 h-5 mr-2" />
              Informações do Sistema
            </h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
              <div>
                <label className="font-medium text-gray-700">Versão</label>
                <p className="text-gray-600">1.0.0</p>
              </div>
              <div>
                <label className="font-medium text-gray-700">Ambiente</label>
                <p className="text-gray-600">Produção</p>
              </div>
              <div>
                <label className="font-medium text-gray-700">Última Atualização</label>
                <p className="text-gray-600">29/07/2025</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}