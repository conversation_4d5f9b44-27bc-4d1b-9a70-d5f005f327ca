'use client';

import { useEffect, useState } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/Card';
import { LoadingState } from '@/components/ui/LoadingState';
import { ErrorState } from '@/components/ui/ErrorState';
import {
  Users,
  MapPin,
  Trophy,
  Gift,
  Package,
  Bike,
  TrendingUp,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  DollarSign
} from 'lucide-react';
import { createClient } from '@/utils/supabase/client';

interface ReportStats {
  totalUsers: number;
  activeUsers: number;
  totalTouristSpots: number;
  totalAchievements: number;
  totalRewards: number;
  totalRedemptions: number;
  pendingRedemptions: number;
  approvedRedemptions: number;
  totalVehicles: number;
  totalCheckIns: number;
  totalPointsAwarded: number;
  totalPointsRedeemed: number;
}

interface MonthlyStats {
  month: string;
  newUsers: number;
  checkIns: number;
  pointsAwarded: number;
  redemptions: number;
}

export default function AdminReportsPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<ReportStats | null>(null);
  const [monthlyData, setMonthlyData] = useState<MonthlyStats[]>([]);

  const supabase = createClient();

  const loadReports = async () => {
    try {
      setLoading(true);
      setError(null);

      // Estatísticas gerais
      const [
        usersResult,
        touristSpotsResult,
        achievementsResult,
        rewardsResult,
        redemptionsResult,
        vehiclesResult,
        checkInsResult,
        pointTransactionsResult
      ] = await Promise.all([
        // Total de usuários e usuários ativos (últimos 30 dias)
        supabase.from('profiles').select('id, created_at').order('created_at', { ascending: false }),
        
        // Pontos turísticos
        supabase.from('tourist_spots').select('id').eq('is_active', true),
        
        // Conquistas
        supabase.from('achievements').select('id').eq('is_active', true),
        
        // Recompensas
        supabase.from('rewards').select('id').eq('is_active', true),
        
        // Resgates
        supabase.from('reward_redemptions').select('status, points_spent, created_at'),
        
        // Veículos
        supabase.from('vehicles').select('id').eq('is_available', true),
        
        // Check-ins
        supabase.from('check_ins').select('id, created_at'),
        
        // Transações de pontos
        supabase.from('point_transactions').select('points, type, created_at')
      ]);

      if (usersResult.error) throw new Error('Erro ao carregar usuários');
      if (touristSpotsResult.error) throw new Error('Erro ao carregar pontos turísticos');
      if (achievementsResult.error) throw new Error('Erro ao carregar conquistas');
      if (rewardsResult.error) throw new Error('Erro ao carregar recompensas');
      if (redemptionsResult.error) throw new Error('Erro ao carregar resgates');
      if (vehiclesResult.error) throw new Error('Erro ao carregar veículos');
      if (checkInsResult.error) throw new Error('Erro ao carregar check-ins');
      if (pointTransactionsResult.error) throw new Error('Erro ao carregar transações');

      const users = usersResult.data || [];
      const redemptions = redemptionsResult.data || [];
      const pointTransactions = pointTransactionsResult.data || [];

      // Calcular usuários ativos (últimos 30 dias)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const activeUsers = users.filter(user => 
        new Date(user.created_at) >= thirtyDaysAgo
      ).length;

      // Calcular pontos
      const totalPointsAwarded = pointTransactions
        .filter(t => t.type === 'earned' || t.type === 'bonus')
        .reduce((sum, t) => sum + (t.points || 0), 0);

      const totalPointsRedeemed = pointTransactions
        .filter(t => t.type === 'redeemed')
        .reduce((sum, t) => sum + Math.abs(t.points || 0), 0);

      const reportStats: ReportStats = {
        totalUsers: users.length,
        activeUsers,
        totalTouristSpots: touristSpotsResult.data?.length || 0,
        totalAchievements: achievementsResult.data?.length || 0,
        totalRewards: rewardsResult.data?.length || 0,
        totalRedemptions: redemptions.length,
        pendingRedemptions: redemptions.filter(r => r.status === 'pending').length,
        approvedRedemptions: redemptions.filter(r => r.status === 'approved' || r.status === 'used').length,
        totalVehicles: vehiclesResult.data?.length || 0,
        totalCheckIns: checkInsResult.data?.length || 0,
        totalPointsAwarded,
        totalPointsRedeemed
      };

      // Dados mensais (últimos 6 meses)
      const monthlyStats: MonthlyStats[] = [];
      for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
        const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

        const monthUsers = users.filter(user => {
          const userDate = new Date(user.created_at);
          return userDate >= monthStart && userDate <= monthEnd;
        }).length;

        const monthCheckIns = (checkInsResult.data || []).filter(checkIn => {
          const checkInDate = new Date(checkIn.created_at);
          return checkInDate >= monthStart && checkInDate <= monthEnd;
        }).length;

        const monthPoints = pointTransactions
          .filter(t => {
            const transDate = new Date(t.created_at);
            return transDate >= monthStart && transDate <= monthEnd && 
                   (t.type === 'earned' || t.type === 'bonus');
          })
          .reduce((sum, t) => sum + (t.points || 0), 0);

        const monthRedemptions = redemptions.filter(r => {
          const redemptionDate = new Date(r.created_at);
          return redemptionDate >= monthStart && redemptionDate <= monthEnd;
        }).length;

        monthlyStats.push({
          month: date.toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' }),
          newUsers: monthUsers,
          checkIns: monthCheckIns,
          pointsAwarded: monthPoints,
          redemptions: monthRedemptions
        });
      }

      setStats(reportStats);
      setMonthlyData(monthlyStats);
    } catch (err) {
      console.error('Erro ao carregar relatórios:', err);
      setError(err instanceof Error ? err.message : 'Erro ao carregar relatórios');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadReports();
  }, []);

  if (loading) return <LoadingState />;
  if (error) return <ErrorState message={error} onRetry={loadReports} />;
  if (!stats) return <ErrorState message="Nenhum dado encontrado" onRetry={loadReports} />;

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Relatórios</h1>
            <p className="text-gray-600">Visão geral das métricas e estatísticas da plataforma</p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Calendar className="w-4 h-4" />
            <span>Última atualização: {new Date().toLocaleDateString('pt-BR')}</span>
          </div>
        </div>

        {/* Estatísticas Principais */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total de Usuários</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
                  <p className="text-xs text-green-600">+{stats.activeUsers} ativos (30d)</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <MapPin className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Check-ins</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalCheckIns}</p>
                  <p className="text-xs text-gray-500">{stats.totalTouristSpots} pontos ativos</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Activity className="w-6 h-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pontos Distribuídos</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalPointsAwarded.toLocaleString()}</p>
                  <p className="text-xs text-red-600">-{stats.totalPointsRedeemed.toLocaleString()} resgatados</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Package className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Resgates</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalRedemptions}</p>
                  <p className="text-xs text-orange-600">{stats.pendingRedemptions} pendentes</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Métricas Detalhadas */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <div className="flex items-center">
                <BarChart3 className="w-5 h-5 text-gray-400 mr-3" />
                <h3 className="text-lg font-semibold text-gray-900">Recursos da Plataforma</h3>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <div className="flex items-center">
                    <Trophy className="w-4 h-4 text-yellow-500 mr-2" />
                    <span className="text-sm text-gray-600">Conquistas Ativas</span>
                  </div>
                  <span className="font-semibold text-gray-900">{stats.totalAchievements}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <div className="flex items-center">
                    <Gift className="w-4 h-4 text-green-500 mr-2" />
                    <span className="text-sm text-gray-600">Recompensas Ativas</span>
                  </div>
                  <span className="font-semibold text-gray-900">{stats.totalRewards}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <div className="flex items-center">
                    <Bike className="w-4 h-4 text-blue-500 mr-2" />
                    <span className="text-sm text-gray-600">Veículos Disponíveis</span>
                  </div>
                  <span className="font-semibold text-gray-900">{stats.totalVehicles}</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 text-red-500 mr-2" />
                    <span className="text-sm text-gray-600">Pontos Turísticos</span>
                  </div>
                  <span className="font-semibold text-gray-900">{stats.totalTouristSpots}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center">
                <PieChart className="w-5 h-5 text-gray-400 mr-3" />
                <h3 className="text-lg font-semibold text-gray-900">Status dos Resgates</h3>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Pendentes</span>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-orange-400 rounded-full mr-2"></div>
                    <span className="font-semibold text-gray-900">{stats.pendingRedemptions}</span>
                  </div>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Aprovados/Utilizados</span>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
                    <span className="font-semibold text-gray-900">{stats.approvedRedemptions}</span>
                  </div>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-sm text-gray-600">Total de Resgates</span>
                  <span className="font-semibold text-gray-900">{stats.totalRedemptions}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Evolução Mensal */}
        <Card>
          <CardHeader>
            <div className="flex items-center">
              <TrendingUp className="w-5 h-5 text-gray-400 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Evolução Mensal (Últimos 6 Meses)</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Mês
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Novos Usuários
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Check-ins
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pontos Distribuídos
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Resgates
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {monthlyData.map((month, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {month.month}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {month.newUsers}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {month.checkIns}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {month.pointsAwarded.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {month.redemptions}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}