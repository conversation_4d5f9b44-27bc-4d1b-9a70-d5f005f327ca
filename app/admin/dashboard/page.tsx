'use client';

import { useEffect, useState } from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/Card';
import { 
  Users, 
  MapPin, 
  Trophy, 
  TrendingUp, 
  Star,
  Activity,
  Calendar,
  BarChart3,
  Settings,
} from 'lucide-react';
import { createClient } from '@/utils/supabase/client';

interface DashboardStats {
  totalUsers: number;
  totalPoints: number;
  totalCheckIns: number;
  totalAchievements: number;
  activeUsers: number;
  newUsersToday: number;
}

export default function AdminDashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalPoints: 0,
    totalCheckIns: 0,
    totalAchievements: 0,
    activeUsers: 0,
    newUsersToday: 0,
  });
  const [loading, setLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Get total users
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      // Get new users today
      const { count: newUsersToday } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', `${today}T00:00:00.000Z`);

      // Get total points earned
      const { data: pointsData } = await supabase
        .from('profiles')
        .select('total_points_earned');
      
      const totalPoints = pointsData?.reduce((sum, profile) => 
        sum + (profile.total_points_earned || 0), 0
      ) || 0;

      // Get total check-ins
      const { count: totalCheckIns } = await supabase
        .from('check_ins')
        .select('*', { count: 'exact', head: true });

      // Get total achievements earned
      const { count: totalAchievements } = await supabase
        .from('user_achievements')
        .select('*', { count: 'exact', head: true });

      // Get active users (users with activity in last 7 days)
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      
      const { count: activeUsers } = await supabase
        .from('point_transactions')
        .select('user_id', { count: 'exact', head: true })
        .gte('created_at', weekAgo.toISOString());

      setStats({
        totalUsers: totalUsers || 0,
        totalPoints,
        totalCheckIns: totalCheckIns || 0,
        totalAchievements: totalAchievements || 0,
        activeUsers: activeUsers || 0,
        newUsersToday: newUsersToday || 0,
      });
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      name: 'Total de Usuários',
      value: stats.totalUsers.toLocaleString('pt-BR'),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: `+${stats.newUsersToday} hoje`,
    },
    {
      name: 'Pontos Totais',
      value: stats.totalPoints.toLocaleString('pt-BR'),
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      change: 'Acumulados',
    },
    {
      name: 'Check-ins',
      value: stats.totalCheckIns.toLocaleString('pt-BR'),
      icon: MapPin,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: 'Realizados',
    },
    {
      name: 'Conquistas',
      value: stats.totalAchievements.toLocaleString('pt-BR'),
      icon: Trophy,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: 'Desbloqueadas',
    },
    {
      name: 'Usuários Ativos',
      value: stats.activeUsers.toLocaleString('pt-BR'),
      icon: Activity,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      change: 'Últimos 7 dias',
    },
    {
      name: 'Engajamento',
      value: stats.totalUsers > 0 
        ? `${Math.round((stats.activeUsers / stats.totalUsers) * 100)}%`
        : '0%',
      icon: TrendingUp,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      change: 'Taxa de atividade',
    },
  ];

  if (loading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Carregando estatísticas...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-600 to-pink-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2 flex items-center">
                <BarChart3 className="w-8 h-8 mr-3" />
                Dashboard Administrativo
              </h1>
              <p className="text-red-100">
                Visão geral do sistema Trivvy
              </p>
            </div>
            <div className="text-right">
              <p className="text-red-200 text-sm">Última atualização</p>
              <p className="text-white font-medium">
                {new Date().toLocaleString('pt-BR')}
              </p>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {statCards.map((stat) => {
            const Icon = stat.icon;
            return (
              <Card key={stat.name} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className={`rounded-full p-3 ${stat.bgColor}`}>
                      <Icon className={`h-6 w-6 ${stat.color}`} />
                    </div>
                    <div className="ml-4 flex-1">
                      <p className="text-sm font-medium text-gray-500">
                        {stat.name}
                      </p>
                      <p className="text-2xl font-bold text-gray-900">
                        {stat.value}
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        {stat.change}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900">
                Ações Rápidas
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <a
                  href="/admin/usuarios"
                  className="block p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Users className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium text-gray-900">Gerenciar Usuários</p>
                      <p className="text-sm text-gray-500">Visualizar e gerenciar contas</p>
                    </div>
                  </div>
                </a>

                <a
                  href="/admin/pontos-turisticos"
                  className="block p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-green-600" />
                    <div>
                      <p className="font-medium text-gray-900">Pontos Turísticos</p>
                      <p className="text-sm text-gray-500">Adicionar e editar locais</p>
                    </div>
                  </div>
                </a>

                <a
                  href="/admin/configuracoes"
                  className="block p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Settings className="w-5 h-5 text-gray-600" />
                    <div>
                      <p className="font-medium text-gray-900">Configurações</p>
                      <p className="text-sm text-gray-500">Ajustar parâmetros do sistema</p>
                    </div>
                  </div>
                </a>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900">
                Resumo do Sistema
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Taxa de Crescimento</span>
                  <span className="font-medium text-green-600">
                    {stats.totalUsers > 0 && stats.newUsersToday > 0
                      ? `+${((stats.newUsersToday / stats.totalUsers) * 100).toFixed(1)}%`
                      : '0%'
                    }
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Média de Pontos</span>
                  <span className="font-medium text-blue-600">
                    {stats.totalUsers > 0 
                      ? Math.round(stats.totalPoints / stats.totalUsers).toLocaleString('pt-BR')
                      : '0'
                    }
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Check-ins por Usuário</span>
                  <span className="font-medium text-purple-600">
                    {stats.totalUsers > 0 
                      ? (stats.totalCheckIns / stats.totalUsers).toFixed(1)
                      : '0'
                    }
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Conquistas por Usuário</span>
                  <span className="font-medium text-yellow-600">
                    {stats.totalUsers > 0 
                      ? (stats.totalAchievements / stats.totalUsers).toFixed(1)
                      : '0'
                    }
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Status */}
        <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <div>
                <h3 className="font-semibold text-gray-900">Sistema Operacional</h3>
                <p className="text-sm text-gray-600">
                  Todos os serviços estão funcionando normalmente. 
                  Última verificação: {new Date().toLocaleTimeString('pt-BR')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}