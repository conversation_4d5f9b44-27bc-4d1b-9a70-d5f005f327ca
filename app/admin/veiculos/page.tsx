'use client';

import { useState } from 'react';
import { useVehicles } from '@/hooks/useVehicles';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { CreateVehicleModal } from '@/components/admin/CreateVehicleModal';
import { EditVehicleModal } from '@/components/admin/EditVehicleModal';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { EmptyState } from '@/components/ui/EmptyState';
import { ErrorState } from '@/components/ui/ErrorState';
import { useToast, ToastContainer } from '@/components/ui/Toast';
import { 
  Car, 
  Plus,
  Search,
  Filter,
  Eye,
  EyeOff,
  Edit,
  Trash2,
  DollarSign,
  MapPin,
  TrendingUp,
} from 'lucide-react';

export default function AdminVehiclesPage() {
  const { vehicles, loading, error, createVehicle, updateVehicle, deleteVehicle } = useVehicles();
  const { toasts, toast, removeToast } = useToast();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [showUnavailable, setShowUnavailable] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const vehicleTypes = ['scooter', 'bike', 'e-bike', 'skateboard'];
  const locations = [...new Set(vehicles.map(v => v.location).filter(Boolean))] as string[];

  const filteredVehicles = vehicles.filter(vehicle => {
    const matchesSearch = !searchQuery || 
      vehicle.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      vehicle.description?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesType = !selectedType || vehicle.type === selectedType;
    const matchesLocation = !selectedLocation || vehicle.location === selectedLocation;
    const matchesAvailability = showUnavailable || vehicle.is_available;

    return matchesSearch && matchesType && matchesLocation && matchesAvailability;
  });

  const vehicleStats = {
    total: vehicles.length,
    available: vehicles.filter(v => v.is_available).length,
    unavailable: vehicles.filter(v => !v.is_available).length,
    avgPrice: vehicles.length > 0 ? 
      Math.round(vehicles.reduce((sum, v) => sum + v.hourly_price, 0) / vehicles.length) : 0,
  };

  const handleToggleAvailability = async (vehicleId: string, isAvailable: boolean) => {
    setActionLoading(vehicleId);
    try {
      await updateVehicle(vehicleId, { is_available: !isAvailable });
      toast.success(
        `Veículo ${!isAvailable ? 'disponibilizado' : 'indisponibilizado'} com sucesso!`
      );
    } catch (error) {
      console.error('Erro ao atualizar disponibilidade do veículo:', error);
      toast.error('Erro ao atualizar disponibilidade do veículo. Tente novamente.');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async (vehicleId: string) => {
    if (window.confirm('Tem certeza que deseja excluir este veículo?')) {
      setActionLoading(vehicleId);
      try {
        await deleteVehicle(vehicleId);
        toast.success('Veículo excluído com sucesso!');
      } catch (error) {
        console.error('Erro ao excluir veículo:', error);
        toast.error('Erro ao excluir veículo. Tente novamente.');
      } finally {
        setActionLoading(null);
      }
    }
  };

  const handleEdit = (vehicle: any) => {
    setEditingVehicle(vehicle);
  };

  const handleEditSuccess = async (vehicleId: string, updatedData: any) => {
    try {
      await updateVehicle(vehicleId, updatedData);
      setEditingVehicle(null);
      toast.success('Veículo atualizado com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar veículo:', error);
      toast.error('Erro ao atualizar veículo. Tente novamente.');
      throw error;
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Card>
          <CardContent className="p-8">
            <ErrorState 
              message={error}
              onRetry={() => window.location.reload()}
            />
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gerenciar Veículos</h1>
            <p className="text-gray-600">
              Adicione e gerencie veículos para aluguel
            </p>
          </div>
          
          <Button onClick={() => setShowCreateModal(true)} className="flex items-center">
            <Plus className="w-4 h-4 mr-2" />
            Novo Veículo
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <Car className="w-8 h-8 text-primary-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{vehicleStats.total}</div>
              <div className="text-sm text-gray-600">Total de Veículos</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Eye className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{vehicleStats.available}</div>
              <div className="text-sm text-gray-600">Disponíveis</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <EyeOff className="w-8 h-8 text-red-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{vehicleStats.unavailable}</div>
              <div className="text-sm text-gray-600">Indisponíveis</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <DollarSign className="w-8 h-8 text-gold mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">R$ {vehicleStats.avgPrice}</div>
              <div className="text-sm text-gray-600">Preço Médio/Hora</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    type="text"
                    placeholder="Buscar veículos..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Todos os Tipos</option>
                  <option value="scooter">Patinete</option>
                  <option value="bike">Bicicleta</option>
                  <option value="e-bike">E-Bike</option>
                  <option value="skateboard">Skate</option>
                </select>

                <select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Todas as Localizações</option>
                  {locations.map(location => (
                    <option key={location} value={location}>
                      {location}
                    </option>
                  ))}
                </select>

                <Button
                  variant={showUnavailable ? "primary" : "outline"}
                  onClick={() => setShowUnavailable(!showUnavailable)}
                  className="whitespace-nowrap"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  {showUnavailable ? 'Todos' : 'Disponíveis'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Vehicles Grid */}
        {filteredVehicles.length === 0 ? (
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={Car}
                title="Nenhum veículo encontrado"
                description="Tente ajustar os filtros ou adicionar um novo veículo"
                action={{
                  label: "Criar Primeiro Veículo",
                  onClick: () => setShowCreateModal(true)
                }}
              />
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredVehicles.map((vehicle) => (
              <Card key={vehicle.id} className={`relative ${!vehicle.is_available ? 'opacity-60' : ''}`}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                          <Car className="w-5 h-5 text-primary-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{vehicle.name}</h3>
                          <Badge 
                            variant={vehicle.is_available ? 'success' : 'secondary'} 
                            size="sm"
                          >
                            {vehicle.is_available ? 'Disponível' : 'Indisponível'}
                          </Badge>
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3">{vehicle.description}</p>
                      
                      <div className="flex items-center justify-between text-sm mb-2">
                        <span className="text-primary-600 font-medium">
                          R$ {vehicle.hourly_price}/hora
                        </span>
                        <Badge variant="outline" size="sm" className="capitalize">
                          {vehicle.type}
                        </Badge>
                      </div>
                      
                      {vehicle.location && (
                        <div className="flex items-center text-xs text-gray-500">
                          <MapPin className="w-3 h-3 mr-1" />
                          {vehicle.location}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 pt-3 border-t border-gray-100">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleAvailability(vehicle.id, vehicle.is_available)}
                      disabled={actionLoading === vehicle.id}
                      className="flex-1"
                    >
                      {actionLoading === vehicle.id ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-1" />
                          {vehicle.is_available ? 'Indisponibilizando...' : 'Disponibilizando...'}
                        </>
                      ) : vehicle.is_available ? (
                        <>
                          <EyeOff className="w-4 h-4 mr-1" />
                          Indisponível
                        </>
                      ) : (
                        <>
                          <Eye className="w-4 h-4 mr-1" />
                          Disponível
                        </>
                      )}
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(vehicle)}
                      className="text-gray-600 hover:text-primary-600"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(vehicle.id)}
                      disabled={actionLoading === vehicle.id}
                      className="text-gray-600 hover:text-red-600"
                    >
                      {actionLoading === vehicle.id ? (
                        <LoadingSpinner size="sm" />
                      ) : (
                        <Trash2 className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Popular Vehicles */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Veículos por Preço
            </h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {vehicles
                .sort((a, b) => b.hourly_price - a.hourly_price)
                .slice(0, 5)
                .map((vehicle) => (
                  <div key={vehicle.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                        <Car className="w-4 h-4 text-primary-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{vehicle.name}</h4>
                        <p className="text-sm text-gray-600 capitalize">{vehicle.type}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge 
                        variant={vehicle.is_available ? 'success' : 'secondary'} 
                        size="sm"
                      >
                        {vehicle.is_available ? 'Disponível' : 'Indisponível'}
                      </Badge>
                      <span className="text-sm font-medium text-primary-600">
                        R$ {vehicle.hourly_price}/h
                      </span>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        {/* Create Vehicle Modal */}
        {showCreateModal && (
          <CreateVehicleModal
            onClose={() => setShowCreateModal(false)}
            onSubmit={async (vehicleData) => {
              try {
                await createVehicle(vehicleData);
                toast.success('Veículo criado com sucesso!');
              } catch (error) {
                toast.error('Erro ao criar veículo!');
                throw error;
              }
            }}
          />
        )}

        {/* Edit Vehicle Modal */}
        {editingVehicle && (
          <EditVehicleModal
            vehicle={editingVehicle}
            onClose={() => setEditingVehicle(null)}
            onSubmit={handleEditSuccess}
          />
        )}
      </div>

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </AdminLayout>
  );
}