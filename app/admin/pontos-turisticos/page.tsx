'use client';

import { useState } from 'react';
import { useTouristSpots } from '@/hooks/useTouristSpots';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { CreateTouristSpotModal } from '@/components/admin/CreateTouristSpotModal';
import { EditTouristSpotModal } from '@/components/admin/EditTouristSpotModal';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { EmptyState } from '@/components/ui/EmptyState';
import { ErrorState } from '@/components/ui/ErrorState';
import { useToast, ToastContainer } from '@/components/ui/Toast';
import { 
  MapPin, 
  Plus,
  Search,
  Filter,
  Eye,
  <PERSON>Off,
  Edit,
  Trash2,
  <PERSON>,
  Star,
} from 'lucide-react';

export default function AdminTouristSpotsPage() {
  const { touristSpots, checkIns, loading, error, createTouristSpot, updateTouristSpot, deleteTouristSpot } = useTouristSpots();
  const { toasts, toast, removeToast } = useToast();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingSpot, setEditingSpot] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showInactive, setShowInactive] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const filteredSpots = touristSpots.filter(spot => {
    const matchesSearch = !searchQuery || 
      spot.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      spot.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      spot.address?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = showInactive || spot.is_active;

    return matchesSearch && matchesStatus;
  });

  const spotStats = touristSpots.reduce((stats, spot) => {
    const checkInCount = checkIns.filter(ci => ci.tourist_spot_id === spot.id).length;
    const uniqueUsers = new Set(checkIns.filter(ci => ci.tourist_spot_id === spot.id).map(ci => ci.user_id)).size;
    stats[spot.id] = { checkIns: checkInCount, users: uniqueUsers };
    return stats;
  }, {} as Record<string, { checkIns: number; users: number }>);

  const handleToggleActive = async (spotId: string, isActive: boolean) => {
    setActionLoading(spotId);
    try {
      await updateTouristSpot(spotId, { is_active: !isActive });
      toast.success(
        `Ponto turístico ${!isActive ? 'ativado' : 'desativado'} com sucesso!`
      );
    } catch (error) {
      console.error('Erro ao atualizar ponto turístico:', error);
      toast.error('Erro ao atualizar ponto turístico. Tente novamente.');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDelete = async (spotId: string) => {
    if (window.confirm('Tem certeza que deseja excluir este ponto turístico?')) {
      setActionLoading(spotId);
      try {
        await deleteTouristSpot(spotId);
        toast.success('Ponto turístico excluído com sucesso!');
      } catch (error) {
        console.error('Erro ao excluir ponto turístico:', error);
        toast.error('Erro ao excluir ponto turístico. Tente novamente.');
      } finally {
        setActionLoading(null);
      }
    }
  };

  const handleCreateSuccess = () => {
    setShowCreateModal(false);
    toast.success('Ponto turístico criado com sucesso!');
  };

  const handleEdit = (spot: any) => {
    setEditingSpot(spot);
  };

  const handleEditSuccess = async (spotId: string, updatedData: any) => {
    try {
      await updateTouristSpot(spotId, updatedData);
      setEditingSpot(null);
      toast.success('Ponto turístico atualizado com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar ponto turístico:', error);
      toast.error('Erro ao atualizar ponto turístico. Tente novamente.');
      throw error;
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Card>
          <CardContent className="p-8">
            <ErrorState 
              message={error}
              onRetry={() => window.location.reload()}
            />
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gerenciar Pontos Turísticos</h1>
            <p className="text-gray-600">
              Adicione e gerencie locais para check-in dos usuários
            </p>
          </div>
          
          <Button onClick={() => setShowCreateModal(true)} className="flex items-center">
            <Plus className="w-4 h-4 mr-2" />
            Novo Ponto Turístico
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <MapPin className="w-8 h-8 text-primary-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{touristSpots.length}</div>
              <div className="text-sm text-gray-600">Total de Pontos</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Eye className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">
                {touristSpots.filter(s => s.is_active).length}
              </div>
              <div className="text-sm text-gray-600">Ativos</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Users className="w-8 h-8 text-secondary-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{checkIns.length}</div>
              <div className="text-sm text-gray-600">Check-ins Realizados</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Star className="w-8 h-8 text-gold mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">
                {Math.round(checkIns.length / Math.max(touristSpots.length, 1))}
              </div>
              <div className="text-sm text-gray-600">Média de Check-ins</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    type="text"
                    placeholder="Buscar pontos turísticos..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  variant={showInactive ? "primary" : "outline"}
                  onClick={() => setShowInactive(!showInactive)}
                  className="whitespace-nowrap"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  {showInactive ? 'Todos' : 'Ativos'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tourist Spots Grid */}
        {filteredSpots.length === 0 ? (
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={MapPin}
                title="Nenhum ponto turístico encontrado"
                description="Tente ajustar os filtros ou adicionar um novo ponto turístico"
                action={{
                  label: "Criar Primeiro Ponto Turístico",
                  onClick: () => setShowCreateModal(true)
                }}
              />
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredSpots.map((spot) => {
              const stats = spotStats[spot.id] || { checkIns: 0, users: 0 };
              
              return (
                <Card key={spot.id} className={`relative ${!spot.is_active ? 'opacity-60' : ''}`}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                            <MapPin className="w-5 h-5 text-primary-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">{spot.name}</h3>
                            <Badge variant={spot.is_active ? 'success' : 'secondary'} size="sm">
                              {spot.is_active ? 'Ativo' : 'Inativo'}
                            </Badge>
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2">{spot.description}</p>
                        {spot.address && (
                          <p className="text-xs text-gray-500 mb-3">{spot.address}</p>
                        )}
                        
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-primary-600 font-medium">
                            {spot.points_reward} pontos
                          </span>
                          <Badge variant="outline" size="sm">
                            {spot.check_in_radius}m raio
                          </Badge>
                        </div>
                        
                        <div className="flex items-center justify-between text-xs text-gray-500 mt-2">
                          <span>{stats.checkIns} check-ins</span>
                          <span>{stats.users} usuários únicos</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 pt-3 border-t border-gray-100">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleActive(spot.id, spot.is_active)}
                        disabled={actionLoading === spot.id}
                        className="flex-1"
                      >
                        {actionLoading === spot.id ? (
                          <>
                            <LoadingSpinner size="sm" className="mr-1" />
                            {spot.is_active ? 'Desativando...' : 'Ativando...'}
                          </>
                        ) : spot.is_active ? (
                          <>
                            <EyeOff className="w-4 h-4 mr-1" />
                            Desativar
                          </>
                        ) : (
                          <>
                            <Eye className="w-4 h-4 mr-1" />
                            Ativar
                          </>
                        )}
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(spot)}
                        className="text-gray-600 hover:text-primary-600"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(spot.id)}
                        disabled={actionLoading === spot.id}
                        className="text-gray-600 hover:text-red-600"
                      >
                        {actionLoading === spot.id ? (
                          <LoadingSpinner size="sm" />
                        ) : (
                          <Trash2 className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* Create Tourist Spot Modal */}
        {showCreateModal && (
          <CreateTouristSpotModal
            onClose={() => setShowCreateModal(false)}
            onSubmit={createTouristSpot}
          />
        )}

        {/* Edit Tourist Spot Modal */}
        {editingSpot && (
          <EditTouristSpotModal
            spot={editingSpot}
            onClose={() => setEditingSpot(null)}
            onSubmit={handleEditSuccess}
          />
        )}
      </div>

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </AdminLayout>
  );
}