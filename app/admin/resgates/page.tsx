'use client';

import { useState } from 'react';
import { useRedemptions } from '@/hooks/useRedemptions';
import { useAuth } from '@/hooks/useAuth';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { EmptyState } from '@/components/ui/EmptyState';
import { ErrorState } from '@/components/ui/ErrorState';
import { useToast, ToastContainer } from '@/components/ui/Toast';
import { 
  Gift, 
  Search,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Coins,
  Calendar,
  MessageSquare,
  Package
} from 'lucide-react';

const statusConfig = {
  pending: { 
    label: 'Pendente', 
    color: 'bg-yellow-100 text-yellow-800', 
    icon: Clock 
  },
  approved: { 
    label: 'Aprovado', 
    color: 'bg-green-100 text-green-800', 
    icon: CheckCircle 
  },
  used: { 
    label: 'Utilizado', 
    color: 'bg-blue-100 text-blue-800', 
    icon: Package 
  },
  expired: { 
    label: 'Rejeitado', 
    color: 'bg-red-100 text-red-800', 
    icon: XCircle 
  }
};

const rewardTypeConfig = {
  discount: { label: 'Desconto', color: 'bg-purple-100 text-purple-800' },
  free_rental: { label: 'Aluguel Grátis', color: 'bg-green-100 text-green-800' },
  merchandise: { label: 'Produto', color: 'bg-blue-100 text-blue-800' },
  experience: { label: 'Experiência', color: 'bg-orange-100 text-orange-800' },
  partner_benefit: { label: 'Benefício Parceiro', color: 'bg-indigo-100 text-indigo-800' }
};

export default function AdminRedemptionsPage() {
  const { redemptions, loading, error, approveRedemption, rejectRedemption, markAsUsed, getStats } = useRedemptions();
  const { profile } = useAuth();
  const { toasts, toast, removeToast } = useToast();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [noteModal, setNoteModal] = useState<{ id: string; action: string; show: boolean }>({
    id: '',
    action: '',
    show: false
  });
  const [notes, setNotes] = useState('');

  const stats = getStats();

  const filteredRedemptions = redemptions.filter(redemption => {
    const matchesSearch = !searchQuery || 
      redemption.user_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      redemption.user_email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      redemption.reward_name.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = !selectedStatus || redemption.status === selectedStatus;

    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <ErrorState 
          title="Erro ao carregar resgates"
          message={error}
        />
      </AdminLayout>
    );
  }

  const handleAction = async (redemptionId: string, action: 'approve' | 'reject' | 'use') => {
    if (!profile?.id) return;

    setActionLoading(redemptionId);
    
    try {
      let result;
      
      switch (action) {
        case 'approve':
          result = await approveRedemption(redemptionId, profile.id, notes);
          break;
        case 'reject':
          result = await rejectRedemption(redemptionId, profile.id, notes);
          break;
        case 'use':
          result = await markAsUsed(redemptionId, profile.id, notes);
          break;
      }
      
      if (result.success) {
        toast.success(
          `Resgate ${action === 'approve' ? 'aprovado' : action === 'reject' ? 'rejeitado' : 'marcado como usado'} com sucesso.`,
          'Sucesso!'
        );
        setNoteModal({ id: '', action: '', show: false });
        setNotes('');
      } else {
        toast.error(result.error || 'Erro ao processar ação', 'Erro');
      }
    } catch {
      toast.error('Erro inesperado ao processar ação', 'Erro');
    } finally {
      setActionLoading(null);
    }
  };

  const openNoteModal = (redemptionId: string, action: string) => {
    setNoteModal({ id: redemptionId, action, show: true });
    setNotes('');
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Gift className="w-7 h-7 mr-3 text-purple-600" />
              Gestão de Resgates
            </h1>
            <p className="text-gray-600">
              Aprove, rejeite e gerencie os resgates de recompensas dos usuários
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="rounded-full p-2 bg-yellow-100">
                  <Clock className="h-5 w-5 text-yellow-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">Pendentes</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="rounded-full p-2 bg-green-100">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">Aprovados</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.approved}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="rounded-full p-2 bg-blue-100">
                  <Package className="h-5 w-5 text-blue-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">Utilizados</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.used}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="rounded-full p-2 bg-purple-100">
                  <Coins className="h-5 w-5 text-purple-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-500">Pontos Usados</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalPointsUsed.toLocaleString('pt-BR')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Buscar por usuário ou recompensa..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="flex gap-2">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="">Todos os status</option>
                  <option value="pending">Pendentes</option>
                  <option value="approved">Aprovados</option>
                  <option value="used">Utilizados</option>
                  <option value="expired">Rejeitados</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Redemptions List */}
        {filteredRedemptions.length === 0 ? (
          <EmptyState
            icon={Gift}
            title="Nenhum resgate encontrado"
            description="Não há resgates que correspondam aos filtros selecionados."
          />
        ) : (
          <div className="space-y-4">
            {filteredRedemptions.map((redemption) => {
              const StatusIcon = statusConfig[redemption.status].icon;
              
              return (
                <Card key={redemption.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-start gap-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-lg font-semibold text-gray-900">
                                {redemption.reward_name}
                              </h3>
                              <Badge className={statusConfig[redemption.status].color}>
                                <StatusIcon className="w-3 h-3 mr-1" />
                                {statusConfig[redemption.status].label}
                              </Badge>
                              <Badge className={rewardTypeConfig[redemption.reward_type as keyof typeof rewardTypeConfig]?.color || 'bg-gray-100 text-gray-800'}>
                                {rewardTypeConfig[redemption.reward_type as keyof typeof rewardTypeConfig]?.label || redemption.reward_type}
                              </Badge>
                            </div>
                            
                            <p className="text-gray-600 mb-3">{redemption.reward_description}</p>
                            
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500">
                              <div className="flex items-center">
                                <User className="w-4 h-4 mr-2" />
                                <div>
                                  <p className="font-medium text-gray-900">{redemption.user_name}</p>
                                  <p>{redemption.user_email}</p>
                                </div>
                              </div>
                              
                              <div className="flex items-center">
                                <Coins className="w-4 h-4 mr-2" />
                                <span className="font-medium text-gray-900">
                                  {(redemption.points_spent || 0).toLocaleString('pt-BR')} pontos
                                </span>
                              </div>
                              
                              <div className="flex items-center">
                                <Calendar className="w-4 h-4 mr-2" />
                                <span>
                                  {new Date(redemption.created_at).toLocaleDateString('pt-BR', {
                                    day: '2-digit',
                                    month: '2-digit',
                                    year: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </span>
                              </div>
                            </div>

                            {redemption.notes && (
                              <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-start">
                                  <MessageSquare className="w-4 h-4 mr-2 mt-0.5 text-gray-400" />
                                  <div>
                                    <p className="text-sm font-medium text-gray-700">Observações:</p>
                                    <p className="text-sm text-gray-600">{redemption.notes}</p>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex flex-col gap-2 ml-4">
                        {redemption.status === 'pending' && (
                          <>
                            <Button
                              size="sm"
                              onClick={() => openNoteModal(redemption.id, 'approve')}
                              disabled={actionLoading === redemption.id}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CheckCircle className="w-4 h-4 mr-1" />
                              Aprovar
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => openNoteModal(redemption.id, 'reject')}
                              disabled={actionLoading === redemption.id}
                              className="text-red-600 border-red-300 hover:bg-red-50"
                            >
                              <XCircle className="w-4 h-4 mr-1" />
                              Rejeitar
                            </Button>
                          </>
                        )}
                        
                        {redemption.status === 'approved' && (
                          <Button
                            size="sm"
                            onClick={() => openNoteModal(redemption.id, 'use')}
                            disabled={actionLoading === redemption.id}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            <Package className="w-4 h-4 mr-1" />
                            Marcar como Usado
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* Note Modal */}
        {noteModal.show && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-semibold mb-4">
                {noteModal.action === 'approve' && 'Aprovar Resgate'}
                {noteModal.action === 'reject' && 'Rejeitar Resgate'}
                {noteModal.action === 'use' && 'Marcar como Usado'}
              </h3>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Observações {noteModal.action === 'reject' ? '(obrigatório)' : '(opcional)'}
                </label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Adicione observações sobre esta ação..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  rows={3}
                />
              </div>
              
              <div className="flex gap-3">
                <Button
                  onClick={() => handleAction(noteModal.id, noteModal.action as 'approve' | 'reject' | 'use')}
                  disabled={actionLoading === noteModal.id || (noteModal.action === 'reject' && !notes.trim())}
                  className={
                    noteModal.action === 'approve' ? 'bg-green-600 hover:bg-green-700' :
                    noteModal.action === 'reject' ? 'bg-red-600 hover:bg-red-700' :
                    'bg-blue-600 hover:bg-blue-700'
                  }
                >
                  {actionLoading === noteModal.id ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    <>
                      {noteModal.action === 'approve' && <CheckCircle className="w-4 h-4 mr-1" />}
                      {noteModal.action === 'reject' && <XCircle className="w-4 h-4 mr-1" />}
                      {noteModal.action === 'use' && <Package className="w-4 h-4 mr-1" />}
                      Confirmar
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setNoteModal({ id: '', action: '', show: false })}
                  disabled={actionLoading === noteModal.id}
                >
                  Cancelar
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      <ToastContainer toasts={toasts} onClose={removeToast} />
    </AdminLayout>
  );
}