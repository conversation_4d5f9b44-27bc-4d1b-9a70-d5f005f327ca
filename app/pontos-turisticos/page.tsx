import Link from 'next/link';
import Image from 'next/image';
import { ArrowR<PERSON>, MapPin, Trophy, Star, Camera, Navigation, Target, Award, Utensils, Car } from 'lucide-react';
import { Button } from '@/components/ui/Button';

export const metadata = {
  title: 'Pontos Turísticos - Trivvy',
  description: 'Descubra pontos turísticos incríveis e ganhe pontos extras fazendo check-ins em locais especiais durante seus aluguéis.',
};

export default function PontosTuristicosPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Link href="/">
              <Image
                src="/assets/logo-2.png"
                alt="Trivvy"
                width={148}
                height={148}
                className="rounded-lg cursor-pointer"
              />
            </Link>
          </div>
          
          <div className="flex items-center space-x-4">
            <Link href="/login">
              <Button variant="ghost" className="hidden sm:inline-flex">Entrar</Button>
            </Link>
            <Link href="/cadastro">
              <Button className="bg-primary-600 hover:bg-primary-700 min-h-[44px] px-6">
                Cadastrar Grátis
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-green-600 via-green-700 to-green-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Pontos Turísticos <span className="text-yellow-400">Especiais</span>
          </h1>
          <p className="text-xl text-green-100 max-w-3xl mx-auto leading-relaxed">
            Explore a cidade de forma gamificada! Faça check-ins em pontos turísticos especiais e ganhe pontos extras no sistema Ponto X.
          </p>
        </div>
      </section>

      {/* Como Funciona Check-ins */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Como Funcionam os Check-ins Turísticos
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Sistema exclusivo que transforma seus passeios em oportunidades de ganhar pontos extras
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Navigation className="w-10 h-10 text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                1. Chegue ao Local
              </h3>
              <p className="text-gray-600 text-lg">
                Durante seu aluguel, visite qualquer ponto turístico cadastrado na nossa plataforma.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Camera className="w-10 h-10 text-blue-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                2. Faça o Check-in
              </h3>
              <p className="text-gray-600 text-lg">
                Use o app ou WhatsApp para registrar sua presença e tirar uma foto no local.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Trophy className="w-10 h-10 text-purple-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                3. Ganhe Pontos
              </h3>
              <p className="text-gray-600 text-lg">
                Receba entre 100-500 pontos instantaneamente, dependendo da raridade do local!
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Categorias de Pontos */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Categorias de Pontos e Estabelecimentos
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Descubra pontos turísticos, restaurantes e serviços de locação. Cada categoria oferece pontos únicos!
            </p>
          </div>

          <div className="grid lg:grid-cols-3 xl:grid-cols-6 gap-8">
            <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <MapPin className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                Pontos Históricos
              </h3>
              <div className="text-center mb-4">
                <span className="text-3xl font-bold text-green-600">+100</span>
                <span className="block text-sm text-gray-500">pontos por check-in</span>
              </div>
              <ul className="space-y-2 text-gray-600 text-sm">
                <li>• Museus e centros culturais</li>
                <li>• Monumentos históricos</li>
                <li>• Igrejas e templos antigos</li>
                <li>• Casarões coloniais</li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Star className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                Atrações Populares
              </h3>
              <div className="text-center mb-4">
                <span className="text-3xl font-bold text-blue-600">+150</span>
                <span className="block text-sm text-gray-500">pontos por check-in</span>
              </div>
              <ul className="space-y-2 text-gray-600 text-sm">
                <li>• Parques e praças famosas</li>
                <li>• Mirantes e pontos de vista</li>
                <li>• Estádios e arenas</li>
                <li>• Centros comerciais icônicos</li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Target className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                Locais Especiais
              </h3>
              <div className="text-center mb-4">
                <span className="text-3xl font-bold text-purple-600">+300</span>
                <span className="block text-sm text-gray-500">pontos por check-in</span>
              </div>
              <ul className="space-y-2 text-gray-600 text-sm">
                <li>• Patrimônios da UNESCO</li>
                <li>• Locais de gravação famosos</li>
                <li>• Marcos arquitetônicos únicos</li>
                <li>• Festivais e eventos especiais</li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-yellow-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Award className="w-8 h-8 text-yellow-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                Locais Exclusivos
              </h3>
              <div className="text-center mb-4">
                <span className="text-3xl font-bold text-yellow-600">+500</span>
                <span className="block text-sm text-gray-500">pontos por check-in</span>
              </div>
              <ul className="space-y-2 text-gray-600 text-sm">
                <li>• Locais de acesso limitado</li>
                <li>• Experiências VIP exclusivas</li>
                <li>• Eventos temporários especiais</li>
                <li>• Conquistas de descoberta</li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Utensils className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                🍽 Restaurantes
              </h3>
              <div className="text-center mb-4">
                <span className="text-3xl font-bold text-orange-600">+30</span>
                <span className="block text-sm text-gray-500">pontos por check-in</span>
              </div>
              <ul className="space-y-2 text-gray-600 text-sm">
                <li>• Restaurantes tradicionais</li>
                <li>• Bistrôs e cafeterias</li>
                <li>• Culinária brasileira</li>
                <li>• Gastronomia internacional</li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-teal-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Car className="w-8 h-8 text-teal-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                🚲 Locação de Passeios
              </h3>
              <div className="text-center mb-4">
                <span className="text-3xl font-bold text-teal-600">+40</span>
                <span className="block text-sm text-gray-500">pontos por check-in</span>
              </div>
              <ul className="space-y-2 text-gray-600 text-sm">
                <li>• Aluguel de bicicletas</li>
                <li>• Tours e passeios guiados</li>
                <li>• Atividades aquáticas</li>
                <li>• Esportes de aventura</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Conquistas Especiais */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Conquistas de Explorador
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Desbloqueie conquistas especiais visitando múltiplos pontos turísticos
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl border border-green-200">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Trophy className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  🥉 Explorador Iniciante
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Visite 5 pontos turísticos diferentes
                </p>
                <div className="text-2xl font-bold text-green-600">+1.000 pontos</div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl border border-blue-200">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Trophy className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  🥈 Aventureiro Urbano
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Visite 15 pontos turísticos diferentes
                </p>
                <div className="text-2xl font-bold text-blue-600">+3.000 pontos</div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl border border-purple-200">
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Trophy className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  🥇 Mestre Explorador
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Visite 50 pontos turísticos diferentes
                </p>
                <div className="text-2xl font-bold text-purple-600">+10.000 pontos</div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 p-8 rounded-2xl border border-yellow-200">
              <div className="text-center">
                <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  🏆 Colecionador de Cidades
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Visite pontos em 5 cidades diferentes
                </p>
                <div className="text-2xl font-bold text-yellow-600">+5.000 pontos</div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-red-50 to-red-100 p-8 rounded-2xl border border-red-200">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Star className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  ⭐ Caçador de Raridades
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Encontre 10 locais exclusivos (+500 pontos)
                </p>
                <div className="text-2xl font-bold text-red-600">+15.000 pontos</div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-cyan-50 to-cyan-100 p-8 rounded-2xl border border-cyan-200">
              <div className="text-center">
                <div className="w-16 h-16 bg-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MapPin className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  🗺️ Descobridor Secreto
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Seja o primeiro a visitar um novo ponto
                </p>
                <div className="text-2xl font-bold text-cyan-600">+2.500 pontos</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-20 bg-gradient-to-r from-green-600 to-green-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Comece sua Jornada de Exploração!
          </h2>
          <p className="text-xl text-green-100 mb-8 max-w-2xl mx-auto">
            Cadastre-se gratuitamente e transforme seus passeios em uma aventura gamificada cheia de recompensas!
          </p>
          
          <Link href="/cadastro">
            <Button 
              variant="secondary" 
              size="lg" 
              className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-10 py-5 text-xl font-bold min-h-[64px] shadow-2xl hover:shadow-primary-500/25 transition-all duration-500 group relative overflow-hidden"
            >
              <MapPin className="mr-3 w-6 h-6" />
              Começar a Explorar
              <ArrowRight className="ml-3 w-6 h-6" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <Link href="/">
                  <Image
                    src="/assets/logo-2.png"
                    alt="STrivvy"
                    width={48}
                    height={48}
                    className="rounded-lg cursor-pointer"
                  />
                </Link>
                <span className="text-2xl font-bold">Trivvy</span>
              </div>
              <p className="text-gray-300 text-lg mb-6 max-w-md">
                A primeira plataforma de mobilidade urbana com sistema completo de gamificação do Brasil.
              </p>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Produto</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/como-funciona" className="hover:text-white transition-colors">Como Funciona</Link></li>
                <li><Link href="/veiculos" className="hover:text-white transition-colors">Veículos Disponíveis</Link></li>
                <li><Link href="/pontos-turisticos" className="hover:text-white transition-colors">Pontos Turísticos</Link></li>
                <li><Link href="/recompensas" className="hover:text-white transition-colors">Sistema de Recompensas</Link></li>
                <li><Link href="/niveis" className="hover:text-white transition-colors">Níveis Ponto X</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Suporte & Legal</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/ajuda" className="hover:text-white transition-colors">Central de Ajuda</Link></li>
                <li><Link href="/contato" className="hover:text-white transition-colors">Fale Conosco</Link></li>
                <li><Link href="/termos" className="hover:text-white transition-colors">Termos de Uso</Link></li>
                <li><Link href="/privacidade" className="hover:text-white transition-colors">Política de Privacidade</Link></li>
                <li><Link href="/cookies" className="hover:text-white transition-colors">Política de Cookies</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="text-center md:text-left">
                <p className="text-gray-400 text-sm">
                  &copy; 2025 Trivvy. Todos os direitos reservados.
                </p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}