import Link from 'next/link';
import Image from 'next/image';
import { <PERSON><PERSON><PERSON>, Star, MapPin, Gift, Trophy, Smartphone, Shield, Clock, Users, CheckCircle, Zap, Target, Sparkles, Crown, Gamepad2, TrendingUp, Award, Heart } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/Button';
import { VehicleMarketplace } from '@/components/landing/VehicleMarketplace';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Image
              src="/assets/logo-2.png"
              alt="Trivvy"
              width={148}
              height={148}
              className="rounded-lg"
            />
          </div>
          
          <div className="flex items-center space-x-4">
            <Link href="/login">
              <Button variant="ghost" className="hidden sm:inline-flex">Entrar</Button>
            </Link>
            <Link href="/cadastro">
              <Button className="bg-primary-600 hover:bg-primary-700 min-h-[44px] px-6">
                Cadastrar Grátis
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section - Gradiente Limpo */}
      <section className="relative min-h-screen flex items-center bg-gradient-to-br from-primary-500 via-primary-600 to-primary-800 overflow-hidden">
        {/* Gradiente Background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-primary-400/30 via-primary-600/20 to-primary-800/40"></div>
          <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4wNSI+PGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMiIvPjwvZz48L2c+PC9zdmc+')] opacity-30"></div>
        </div>

        <div className="container mx-auto px-4 py-10 lg:py-22 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
              {/* Left Column - Content */}
              <div className="text-center lg:text-left">
                {/* Trust Badge with Animation */}
                <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-full text-sm font-medium mb-8 border border-white/20 hover:bg-white/20 transition-all duration-500">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <Sparkles className="w-4 h-4 text-green-400" />
                  </div>
                  <span>+10.000 brasileiros economizando com a Trivvy</span>
                </div>
                
                {/* Main Headline - Novo Copy */}
                <h1 className="text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 leading-none tracking-tight">
                  Seu app de{' '}
                  <span className="relative inline-block">
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 via-white to-cyan-300">
                      turismo
                    </span>
                  </span>,{' '}
                  <br />
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-400">
                    lazer
                  </span>{' '}
                  e{' '}
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-emerald-400">
                    liberdade
                  </span>
                </h1>
                
                {/* Enhanced Value Proposition */}
                <p className="text-xl md:text-2xl text-white/90 mb-10 max-w-3xl leading-relaxed">
                  Explore, se divirta e economize com nossa plataforma de mobilidade gamificada.
                  <strong className="text-white"> Ganhe pontos</strong> e desbloqueie benefícios exclusivos.
                </p>
                
                {/* Modern Value Props with Icons */}
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-12">
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-300 group">
                    <Zap className="w-8 h-8 text-yellow-300 mb-2 group-hover:scale-110 transition-transform" />
                    <div className="text-white font-bold text-lg">2min</div>
                    <div className="text-white/80 text-xs">Aluguel</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-300 group">
                    <Trophy className="w-8 h-8 text-purple-300 mb-2 group-hover:scale-110 transition-transform" />
                    <div className="text-white font-bold text-lg">25%</div>
                    <div className="text-white/80 text-xs">Desconto</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-300 group">
                    <Target className="w-8 h-8 text-cyan-300 mb-2 group-hover:scale-110 transition-transform" />
                    <div className="text-white font-bold text-lg">5+</div>
                    <div className="text-white/80 text-xs">Níveis</div>
                  </div>
                </div>
                
                {/* Modern CTA Section */}
                <div className="space-y-6">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <Link href="/cadastro">
                      <Button 
                        size="lg"
                        className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-10 py-5 text-xl font-bold min-h-[64px] shadow-2xl hover:shadow-primary-500/25 transition-all duration-500 group relative overflow-hidden"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <Crown className="mr-3 w-7 h-7 group-hover:rotate-12 transition-transform" />
                        Começar a Ganhar
                        <ArrowRight className="ml-3 w-7 h-7 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </Link>
                    
                    <Link href="#marketplace">
                      <Button
                        variant="outline"
                        size="lg"
                        className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-10 py-5 text-xl font-bold min-h-[64px] shadow-2xl hover:shadow-primary-500/25 transition-all duration-500 group relative overflow-hidden"
                      >
                        <Gamepad2 className="mr-2 w-6 h-6" />
                        Ver Veículos
                      </Button>
                    </Link>
                  </div>
                  
                  {/* Social Proof */}
                  <div className="flex items-center justify-center lg:justify-start gap-6 text-sm text-gray-300">
                    <div className="flex items-center gap-2">
                      <div className="flex -space-x-2">
                        <div className="w-8 h-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full border-2 border-white/20"></div>
                        <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded-full border-2 border-white/20"></div>
                        <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full border-2 border-white/20"></div>
                      </div>
                      <span>+10K usuários ativos</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span>4.9/5.0 avaliação</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column - Visual Elements */}
              <div className="relative lg:block hidden">
                {/* Floating Cards Animation */}
                <div className="relative w-full h-96">
                  {/* Main Phone Mockup */}
                  <div className="absolute top-0 right-0 w-64 h-80 bg-gradient-to-br from-white to-gray-100 rounded-3xl shadow-2xl border-8 border-white/20 backdrop-blur-sm transform rotate-6 hover:rotate-3 transition-transform duration-500">
                    <div className="p-6 space-y-4">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center">
                          <Crown className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <div className="font-bold text-gray-900">Nível Gold</div>
                          <div className="text-sm text-gray-500">1.847 pontos</div>
                        </div>
                      </div>
                      <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-2xl p-4">
                        <div className="text-sm text-primary-600 font-medium mb-1">Próximo aluguel</div>
                        <div className="text-2xl font-bold text-primary-700">10% OFF</div>
                      </div>
                      <div className="space-y-2">
                        <div className="bg-gray-100 rounded-lg p-3 flex items-center justify-between">
                          <span className="text-sm">🛴 Patinete Pro</span>
                          <span className="font-bold text-green-600">R$ 18</span>
                        </div>
                        <div className="bg-gray-100 rounded-lg p-3 flex items-center justify-between">
                          <span className="text-sm">🚲 Bike Elétrica</span>
                          <span className="font-bold text-green-600">R$ 25</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Floating Achievement Card */}
                  <div className="absolute top-20 left-0 w-48 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl p-4 shadow-xl transform -rotate-12 hover:-rotate-6 transition-transform duration-500">
                    <div className="text-white">
                      <Trophy className="w-8 h-8 mb-2" />
                      <div className="font-bold">Conquista!</div>
                      <div className="text-sm opacity-90">+100 pontos ganhos</div>
                    </div>
                  </div>

                  {/* Floating Stats Card */}
                  <div className="absolute bottom-0 left-10 w-40 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-4 shadow-xl transform rotate-12 hover:rotate-6 transition-transform duration-500">
                    <div className="text-white">
                      <TrendingUp className="w-8 h-8 mb-2" />
                      <div className="font-bold">R$ 847</div>
                      <div className="text-sm opacity-90">Economizados</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2"></div>
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap items-center justify-center gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-gray-900">10.000+</div>
              <div className="text-sm text-gray-600">Usuários Ativos</div>
            </div>
            <div className="w-px h-12 bg-gray-300 hidden sm:block"></div>
            <div>
              <div className="text-3xl font-bold text-gray-900">50.000+</div>
              <div className="text-sm text-gray-600">Viagens Realizadas</div>
            </div>
            <div className="w-px h-12 bg-gray-300 hidden sm:block"></div>
            <div>
              <div className="text-3xl font-bold text-gray-900">4.9★</div>
              <div className="text-sm text-gray-600">Avaliação Média</div>
            </div>
            <div className="w-px h-12 bg-gray-300 hidden sm:block"></div>
            <div>
              <div className="text-3xl font-bold text-gray-900">15</div>
              <div className="text-sm text-gray-600">Cidades Atendidas</div>
            </div>
          </div>
        </div>
      </section>

      {/* Vehicle Marketplace Section */}
      <div id="marketplace">
        <VehicleMarketplace />
      </div>

      {/* Pontos Turísticos Section */}
      <section className="py-20 bg-gradient-to-br from-green-50 to-green-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <MapPin className="inline-block w-12 h-12 text-green-600 mr-4" />
              Pontos Turísticos
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Explore a cidade e ganhe pontos extras visitando locais especiais
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 lg:gap-12">
            {/* Benefit 1 - Financial */}
            <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl transition-all duration-500 group">
              <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                <TrendingUp className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center">
                Economie Dinheiro Real
              </h3>
              <div className="space-y-4 mb-6">
                <div className="bg-green-50 rounded-2xl p-4 border border-green-100">
                  <div className="font-bold text-green-700 text-lg">R$ 15/mês economia média</div>
                  <div className="text-green-600 text-sm">Versus transporte tradicional</div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-gray-700">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span><strong>R$ 15 de bônus</strong> no primeiro cadastro</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span><strong>Até 25% de desconto</strong> progressivo por nível</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span><strong>Aluguéis gratuitos</strong> trocando pontos</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span><strong>Sem taxas</strong> de adesão ou mensalidade</span>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-2xl text-center">
                <div className="font-bold">💰 Exemplo Real</div>
                <div className="text-sm opacity-90">João economizou R$ 240 em 2 meses usando o nível Silver</div>
              </div>
            </div>

            {/* Benefit 2 - Convenience & Gamification */}
            <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl transition-all duration-500 group">
              <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Gamepad2 className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center">
                Experiência Gamificada
              </h3>
              <div className="space-y-4 mb-6">
                <div className="bg-purple-50 rounded-2xl p-4 border border-purple-100">
                  <div className="font-bold text-purple-700 text-lg">5 Níveis de Evolução</div>
                  <div className="text-purple-600 text-sm">De Bronze ao Diamond VIP</div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-gray-700">
                    <Trophy className="w-4 h-4 text-purple-500" />
                    <span><strong>+100 pontos</strong> por check-in turístico</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700">
                    <Award className="w-4 h-4 text-purple-500" />
                    <span><strong>Conquistas exclusivas</strong> desbloqueáveis</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700">
                    <Star className="w-4 h-4 text-purple-500" />
                    <span><strong>Ranking mensal</strong> com prêmios</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700">
                    <Target className="w-4 h-4 text-purple-500" />
                    <span><strong>Desafios diários</strong> personalizados</span>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-2xl text-center">
                <div className="font-bold">🎮 Diversão Real</div>
                <div className="text-sm opacity-90">Ana virou Diamond e ganhou 5 aluguéis grátis + acesso VIP</div>
              </div>
            </div>

            {/* Benefit 3 - Exclusive Access */}
            <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl transition-all duration-500 group">
              <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Crown className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center">
                Acesso VIP Exclusivo
              </h3>
              <div className="space-y-4 mb-6">
                <div className="bg-primary-50 rounded-2xl p-4 border border-primary-100">
                  <div className="font-bold text-primary-700 text-lg">Benefícios Exclusivos</div>
                  <div className="text-primary-600 text-sm">Disponíveis apenas para membros</div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-gray-700">
                    <Shield className="w-4 h-4 text-primary-500" />
                    <span><strong>Suporte prioritário</strong> 24/7</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700">
                    <Clock className="w-4 h-4 text-primary-500" />
                    <span><strong>Reserva antecipada</strong> de veículos</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700">
                    <MapPin className="w-4 h-4 text-primary-500" />
                    <span><strong>Locais exclusivos</strong> para membros</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700">
                    <Sparkles className="w-4 h-4 text-primary-500" />
                    <span><strong>Promoções especiais</strong> mensais</span>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-primary-500 to-primary-600 text-white p-4 rounded-2xl text-center">
                <div className="font-bold">👑 Status VIP</div>
                <div className="text-sm opacity-90">Carlos nunca fica sem veículo com a reserva prioritária</div>
              </div>
            </div>
          </div>

          {/* Comparison Table */}
          <div className="mt-20">
            <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-100">
              <div className="bg-gradient-to-r from-primary-600 to-primary-700 p-8 text-center">
                <h3 className="text-3xl font-bold text-white mb-2">
                  Compare: WhatsApp vs Sistema Ponto X
                </h3>
                <p className="text-primary-100">Veja a diferença real entre as duas opções</p>
              </div>
              
              <div className="grid md:grid-cols-2 divide-y md:divide-y-0 md:divide-x divide-gray-200">
                {/* WhatsApp Direct */}
                <div className="p-8">
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Smartphone className="w-8 h-8 text-green-600" />
                    </div>
                    <h4 className="text-2xl font-bold text-gray-900">Aluguel Direto</h4>
                    <p className="text-gray-500">Via WhatsApp</p>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500" />
                      <span>Reserva rápida (2 minutos)</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500" />
                      <span>Sem necessidade de cadastro</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500" />
                      <span>Atendimento humanizado</span>
                    </div>
                    <div className="flex items-center gap-3 opacity-50">
                      <div className="w-5 h-5 border-2 border-gray-300 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                      </div>
                      <span>Sem descontos progressivos</span>
                    </div>
                    <div className="flex items-center gap-3 opacity-50">
                      <div className="w-5 h-5 border-2 border-gray-300 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                      </div>
                      <span>Sem programa de fidelidade</span>
                    </div>
                    <div className="flex items-center gap-3 opacity-50">
                      <div className="w-5 h-5 border-2 border-gray-300 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                      </div>
                      <span>Sem gamificação</span>
                    </div>
                  </div>
                </div>

                {/* Gamification System */}
                <div className="p-8 bg-gradient-to-br from-primary-50/50 to-white relative">
                  <div className="absolute top-4 right-4">
                    <div className="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                      💎 Recomendado
                    </div>
                  </div>
                  
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Trophy className="w-8 h-8 text-primary-600" />
                    </div>
                    <h4 className="text-2xl font-bold text-gray-900">Sistema Ponto X</h4>
                    <p className="text-gray-500">Programa de Fidelidade</p>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-primary-500" />
                      <span><strong>R$ 15 de bônus</strong> no cadastro</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-primary-500" />
                      <span><strong>Até 25% de desconto</strong> progressivo</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-primary-500" />
                      <span><strong>Aluguéis gratuitos</strong> por pontos</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-primary-500" />
                      <span><strong>5 níveis</strong> de evolução</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-primary-500" />
                      <span><strong>Conquistas exclusivas</strong></span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-primary-500" />
                      <span><strong>Acesso VIP</strong> no Diamond</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              O que nossos usuários dizem
            </h2>
            <p className="text-xl text-gray-600">
              Depoimentos reais de quem já transformou sua mobilidade conosco
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100">
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 mb-6 text-lg">
                &ldquo;Incrível como é fácil alugar! Em 2 minutos já estava andando de patinete pela cidade. O sistema de pontos é viciante, já ganhei 3 aluguéis grátis!&rdquo;
              </p>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-primary-600 font-bold">M</span>
                </div>
                <div className="ml-4">
                  <div className="font-semibold text-gray-900">Marina Silva</div>
                  <div className="text-gray-500 text-sm">São Paulo, SP</div>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100">
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 mb-6 text-lg">
                &ldquo;Uso todos os dias para ir ao trabalho. Economizo R$ 200 por mês e ainda ganho pontos que viro desconto. Melhor decisão que tomei!&rdquo;
              </p>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-primary-600 font-bold">R</span>
                </div>
                <div className="ml-4">
                  <div className="font-semibold text-gray-900">Rafael Santos</div>
                  <div className="text-gray-500 text-sm">Rio de Janeiro, RJ</div>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100">
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 mb-6 text-lg">
                &ldquo;Adoro os check-ins turísticos! Conheci lugares novos na cidade que nem sabia que existiam. E ainda ganho pontos por isso!&rdquo;
              </p>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-primary-600 font-bold">A</span>
                </div>
                <div className="ml-4">
                  <div className="font-semibold text-gray-900">Ana Costa</div>
                  <div className="text-gray-500 text-sm">Belo Horizonte, MG</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Como Funciona? É Simples!
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Em apenas 3 passos você está pronto para se mover de forma inteligente pela cidade
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 lg:gap-12">
            <div className="relative text-center lg:text-left">
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-lg lg:static lg:w-12 lg:h-12 lg:mb-6 lg:mx-auto lg:text-xl">
                1
              </div>
              <div className="bg-gradient-to-br from-primary-50 to-primary-100 p-8 rounded-2xl border border-primary-200">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                   Cadastre-se Grátis
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed mb-4">
                  Crie sua conta em menos de 1 minuto e ganhe <strong>R$ 15 de bônus</strong> para começar no sistema Ponto X.
                </p>
                <div className="text-sm text-primary-600 font-medium">
                  💰 R$ 15 de bônus • 🎮 100 pontos grátis • 🏆 Nível Bronze
                </div>
              </div>
            </div>

            <div className="relative text-center lg:text-left">
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-lg lg:static lg:w-12 lg:h-12 lg:mb-6 lg:mx-auto lg:text-xl">
                2
              </div>
              <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 p-8 rounded-2xl border border-yellow-200">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Escolha seu Veículo
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed mb-4">
                  Patinetes, bikes ou carros. Veja disponibilidade em tempo real e reserve instantaneamente via WhatsApp.
                </p>
                <div className="text-sm text-primary-600 font-medium">
                  🛴 Patinetes • 🚲 Bikes • 🚗 Carros • 📱 WhatsApp direto
                </div>
              </div>
            </div>

            <div className="relative text-center lg:text-left">
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-lg lg:static lg:w-12 lg:h-12 lg:mb-6 lg:mx-auto lg:text-xl">
                3
              </div>
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl border border-green-200">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Rode e Ganhe Pontos
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed mb-4">
                  A cada viagem, check-in turístico e conquista você ganha pontos. Troque por descontos e prêmios.
                </p>
                <div className="text-sm text-primary-600 font-medium">
                  🎯 +50 pontos por viagem • 📍 +100 por check-in • 🏆 Conquistas especiais
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <Link href="/cadastro">
              <Button 
                size="lg" 
                className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 text-lg font-semibold min-h-[56px] shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Começar Agora - É Grátis!
                <ArrowRight className="ml-2 w-6 h-6" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Gamification Section - Enhanced */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden relative">
        <div className="absolute inset-0 bg-[url('/assets/hero-bg.webp')] opacity-5"></div>
        <div className="container mx-auto px-4 relative">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">
              Sistema Ponto X: Gamificação que Recompensa
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Cada ação gera pontos, cada ponto te leva mais próximo de benefícios incríveis
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-6">
            <div className="bg-gradient-to-br from-amber-600 to-amber-700 p-6 rounded-2xl text-center transform hover:scale-105 transition-all duration-300 shadow-xl">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-white" />
              </div>
              <h3 className="font-bold text-xl mb-2">Bronze</h3>
              <p className="text-sm text-amber-100 mb-3">Nível inicial</p>
              <p className="text-xs text-amber-200">0+ pontos</p>
              <div className="mt-4 text-xs text-amber-100">
                ✓ Acesso básico
              </div>
            </div>

            <div className="bg-gradient-to-br from-gray-400 to-gray-500 p-6 rounded-2xl text-center transform hover:scale-105 transition-all duration-300 shadow-xl">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-white" />
              </div>
              <h3 className="font-bold text-xl mb-2">Silver</h3>
              <p className="text-sm text-gray-100 mb-3">5% desconto</p>
              <p className="text-xs text-gray-200">500+ pontos</p>
              <div className="mt-4 text-xs text-gray-100">
                ✓ Desconto 5% • ✓ Pontos bônus
              </div>
            </div>

            <div className="bg-gradient-to-br from-yellow-500 to-yellow-600 p-6 rounded-2xl text-center transform hover:scale-105 transition-all duration-300 shadow-xl">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-white" />
              </div>
              <h3 className="font-bold text-xl mb-2">Gold</h3>
              <p className="text-sm text-yellow-100 mb-3">10% desconto</p>
              <p className="text-xs text-yellow-200">1.500+ pontos</p>
              <div className="mt-4 text-xs text-yellow-100">
                ✓ Desconto 10% • ✓ Suporte prioritário
              </div>
            </div>

            <div className="bg-gradient-to-br from-purple-600 to-purple-700 p-6 rounded-2xl text-center transform hover:scale-105 transition-all duration-300 shadow-xl">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-white" />
              </div>
              <h3 className="font-bold text-xl mb-2">Platinum</h3>
              <p className="text-sm text-purple-100 mb-3">15% desconto</p>
              <p className="text-xs text-purple-200">5.000+ pontos</p>
              <div className="mt-4 text-xs text-purple-100">
                ✓ 15% desconto • ✓ 2 aluguéis grátis
              </div>
            </div>

            <div className="bg-gradient-to-br from-cyan-400 to-blue-500 p-6 rounded-2xl text-center transform hover:scale-105 transition-all duration-300 shadow-xl">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-white" />
              </div>
              <h3 className="font-bold text-xl mb-2">Diamond</h3>
              <p className="text-sm text-cyan-100 mb-3">25% desconto</p>
              <p className="text-xs text-cyan-200">15.000+ pontos</p>
              <div className="mt-4 text-xs text-cyan-100">
                ✓ 25% desconto • ✓ Acesso VIP • ✓ 5 aluguéis grátis
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-300 mb-6 text-lg">
              Comece com <strong className="text-white">100 pontos grátis</strong> e R$ 15 de bônus no seu cadastro!
            </p>
            <Link href="/cadastro">
              <Button variant="primary" size="lg" className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 text-lg font-semibold min-h-[56px] shadow-lg hover:shadow-xl transition-all duration-300">
                <Gift className="mr-2 w-6 h-6" />
                Quero Começar no Bronze
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="container mx-auto px-4 text-center relative">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Sua Mobilidade Inteligente Começa Agora
            </h2>
            <p className="text-xl text-primary-100 mb-8 max-w-3xl mx-auto leading-relaxed">
              Junte-se a <strong>mais de 10.000 brasileiros</strong> que já economizam dinheiro, ganham pontos e se movem de forma sustentável pela cidade
            </p>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8 max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-white mb-4">🎁 Oferta de Lançamento</h3>
              <div className="grid sm:grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-3xl font-bold text-white">R$ 15</div>
                  <div className="text-primary-100 text-sm">Bônus de Cadastro</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-white">100</div>
                  <div className="text-primary-100 text-sm">Pontos Grátis</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-white">0%</div>
                  <div className="text-primary-100 text-sm">Taxa de Adesão</div>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <Link href="/cadastro">
                <Button 
                  variant="secondary" 
                  size="lg" 
                  className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 text-lg font-semibold min-h-[56px] shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Gift className="mr-3 w-7 h-7" />
                  Cadastrar Grátis e Ganhar R$ 15
                  <ArrowRight className="ml-3 w-7 h-7" />
                </Button>
              </Link>
              
              <p className="text-primary-100 text-sm">
                ✓ Sem cartão de crédito • ✓ Ativação instantânea • ✓ Suporte 24/7
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          {/* Main Footer Content */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <Image
                  src="/assets/logo-2.png"
                  alt="Trivvy"
                  width={48}
                  height={48}
                  className="rounded-lg"
                />
                <span className="text-2xl font-bold">Trivvy</span>
              </div>
              <p className="text-gray-300 text-lg mb-6 max-w-md">
                A primeira plataforma de mobilidade urbana com sistema completo de gamificação do Brasil.
              </p>
              
              {/* Trust Signals */}
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-sm text-gray-300">
                  <Shield className="w-5 h-5 text-green-400" />
                  <span>Plataforma 100% segura e confiável</span>
                </div>
                <div className="flex items-center gap-3 text-sm text-gray-300">
                  <Users className="w-5 h-5 text-green-400" />
                  <span>Mais de 10.000 usuários satisfeitos</span>
                </div>
                <div className="flex items-center gap-3 text-sm text-gray-300">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>Suporte 24/7 em português</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Produto</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/como-funciona" className="hover:text-white transition-colors">Como Funciona</Link></li>
                <li><Link href="/veiculos" className="hover:text-white transition-colors">Veículos Disponíveis</Link></li>
                <li><Link href="/pontos-turisticos" className="hover:text-white transition-colors">Pontos Turísticos</Link></li>
                <li><Link href="/recompensas" className="hover:text-white transition-colors">Sistema de Recompensas</Link></li>
                <li><Link href="/niveis" className="hover:text-white transition-colors">Níveis Ponto X</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Suporte & Legal</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/ajuda" className="hover:text-white transition-colors">Central de Ajuda</Link></li>
                <li><Link href="/contato" className="hover:text-white transition-colors">Fale Conosco</Link></li>
                <li><Link href="/termos" className="hover:text-white transition-colors">Termos de Uso</Link></li>
                <li><Link href="/privacidade" className="hover:text-white transition-colors">Política de Privacidade</Link></li>
                <li><Link href="/cookies" className="hover:text-white transition-colors">Política de Cookies</Link></li>
              </ul>
            </div>
          </div>

          {/* Contact Section */}
          <div className="bg-gray-800 rounded-2xl p-8 mb-8">
            <div className="grid md:grid-cols-3 gap-6 text-center md:text-left">
              <div>
                <h4 className="font-bold text-white mb-2">📞 Atendimento</h4>
                <p className="text-gray-300">WhatsApp: (11) 99999-9999</p>
                <p className="text-gray-300 text-sm">Seg-Dom: 6h às 22h</p>
              </div>
              <div>
                <h4 className="font-bold text-white mb-2">✉️ Email</h4>
                <p className="text-gray-300"><EMAIL></p>
                <p className="text-gray-300 text-sm">Resposta em até 2h</p>
              </div>
              <div>
                <h4 className="font-bold text-white mb-2">📱 Redes Sociais</h4>
                <p className="text-gray-300">@Trivvy</p>
                <p className="text-gray-300 text-sm">Novidades e promoções</p>
              </div>
            </div>
          </div>

          {/* Stats Footer */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center mb-8">
            <div>
              <div className="text-3xl font-bold text-primary-400">10K+</div>
              <div className="text-gray-400 text-sm">Usuários Ativos</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary-400">50K+</div>
              <div className="text-gray-400 text-sm">Viagens Realizadas</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary-400">15</div>
              <div className="text-gray-400 text-sm">Cidades Atendidas</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary-400">4.9★</div>
              <div className="text-gray-400 text-sm">Avaliação Média</div>
            </div>
          </div>

          {/* Bottom Footer */}
          <div className="border-t border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="text-center md:text-left">
                <p className="text-gray-400 text-sm">
                  &copy; 2025 Trivvy. Todos os direitos reservados.
                </p>
               
              </div>
              <div className="flex items-center gap-4">
                <div className="text-xs text-gray-500">
                  🇧🇷 Feito no Brasil para brasileiros
                </div>
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <Shield className="w-4 h-4" />
                  SSL Seguro
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}