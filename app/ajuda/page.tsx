import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight, HelpCircle, MessageCircle, Phone, Mail, Clock, Search } from 'lucide-react';
import { Button } from '@/components/ui/Button';

export const metadata = {
  title: 'Central de Ajuda - Trivvy',
  description: 'Encontre respostas para suas dúvidas sobre o sistema Trivvy, aluguel de veículos e sistema de pontos.',
};

export default function AjudaPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Link href="/">
              <Image
                src="/assets/logo-2.png"
                alt="Trivvy"
                width={148}
                height={148}
                className="rounded-lg cursor-pointer"
              />
            </Link>
          </div>
          
          <div className="flex items-center space-x-4">
            <Link href="/login">
              <Button variant="ghost" className="hidden sm:inline-flex">Entrar</Button>
            </Link>
            <Link href="/cadastro">
              <Button className="bg-primary-600 hover:bg-primary-700 min-h-[44px] px-6">
                Cadastrar Grátis
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Central de <span className="text-yellow-400">Ajuda</span>
          </h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed mb-8">
            Encontre respostas rápidas para suas dúvidas ou entre em contato com nossa equipe de suporte.
          </p>
          
          <div className="max-w-md mx-auto relative">
            <input 
              type="text" 
              placeholder="Pesquisar na central de ajuda..." 
              className="w-full px-6 py-4 rounded-full text-gray-900 pr-12 text-lg"
            />
            <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" />
          </div>
        </div>
      </section>

      {/* FAQ Categories */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Perguntas Frequentes
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              As respostas para as dúvidas mais comuns dos nossos usuários
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Getting Started */}
            <div className="bg-gray-50 p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                🚀 Primeiros Passos
              </h3>
              <div className="space-y-4">
                <details className="bg-white p-4 rounded-lg shadow-sm">
                  <summary className="font-semibold text-gray-900 cursor-pointer">
                    Como me cadastro na Trivvy?
                  </summary>
                  <p className="mt-3 text-gray-600">
                    É muito simples! Clique em &ldquo;Cadastrar Grátis&rdquo;, preencha seus dados básicos e pronto! 
                    Você ganha R$ 15 de bônus + 100 pontos para começar.
                  </p>
                </details>
                
                <details className="bg-white p-4 rounded-lg shadow-sm">
                  <summary className="font-semibold text-gray-900 cursor-pointer">
                    Como faço meu primeiro aluguel?
                  </summary>
                  <p className="mt-3 text-gray-600">
                    Após o cadastro, escolha um veículo disponível e reserve via WhatsApp. 
                    Nossa equipe te ajuda com todo o processo em menos de 2 minutos.
                  </p>
                </details>
                
                <details className="bg-white p-4 rounded-lg shadow-sm">
                  <summary className="font-semibold text-gray-900 cursor-pointer">
                    O que é o sistema Ponto X?
                  </summary>
                  <p className="mt-3 text-gray-600">
                    É nosso sistema de gamificação! Você ganha pontos em cada ação e evolui de nível, 
                    desbloqueando descontos progressivos e benefícios exclusivos.
                  </p>
                </details>
              </div>
            </div>

            {/* Points and Levels */}
            <div className="bg-gray-50 p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                🎮 Pontos e Níveis
              </h3>
              <div className="space-y-4">
                <details className="bg-white p-4 rounded-lg shadow-sm">
                  <summary className="font-semibold text-gray-900 cursor-pointer">
                    Como ganho pontos?
                  </summary>
                  <p className="mt-3 text-gray-600">
                    Você ganha pontos fazendo viagens (+50), check-ins turísticos (+100 a +500), 
                    desbloqueando conquistas (+200 a +15.000) e avaliando experiências (+25).
                  </p>
                </details>
                
                <details className="bg-white p-4 rounded-lg shadow-sm">
                  <summary className="font-semibold text-gray-900 cursor-pointer">
                    Quando evoluo de nível?
                  </summary>
                  <p className="mt-3 text-gray-600">
                    A evolução é automática! Silver (500+ pontos), Gold (1.500+), Platinum (5.000+) 
                    e Diamond VIP (15.000+). Cada nível oferece descontos maiores.
                  </p>
                </details>
                
                <details className="bg-white p-4 rounded-lg shadow-sm">
                  <summary className="font-semibold text-gray-900 cursor-pointer">
                    Como troco pontos por recompensas?
                  </summary>
                  <p className="mt-3 text-gray-600">
                    Acesse a loja de recompensas no seu perfil e troque pontos por aluguéis grátis, 
                    produtos exclusivos ou experiências VIP.
                  </p>
                </details>
              </div>
            </div>

            {/* Rentals */}
            <div className="bg-gray-50 p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                🚗 Aluguéis
              </h3>
              <div className="space-y-4">
                <details className="bg-white p-4 rounded-lg shadow-sm">
                  <summary className="font-semibold text-gray-900 cursor-pointer">
                    Quais veículos vocês têm disponíveis?
                  </summary>
                  <p className="mt-3 text-gray-600">
                    Temos patinetes elétricos, bikes tradicionais e elétricas, e carros para diferentes necessidades. 
                    Veja a disponibilidade em tempo real na nossa plataforma.
                  </p>
                </details>
                
                <details className="bg-white p-4 rounded-lg shadow-sm">
                  <summary className="font-semibold text-gray-900 cursor-pointer">
                    Como funciona a cobrança?
                  </summary>
                  <p className="mt-3 text-gray-600">
                    Cobramos por hora ou período, com descontos progressivos baseados no seu nível. 
                    Pagamento via PIX, cartão ou saldo da conta.
                  </p>
                </details>
                
                <details className="bg-white p-4 rounded-lg shadow-sm">
                  <summary className="font-semibold text-gray-900 cursor-pointer">
                    Posso cancelar ou reagendar?
                  </summary>
                  <p className="mt-3 text-gray-600">
                    Sim! Cancelamentos com até 2h de antecedência são gratuitos. 
                    Reagendamentos podem ser feitos pelo WhatsApp sem custos extras.
                  </p>
                </details>
              </div>
            </div>

            {/* Payment and Account */}
            <div className="bg-gray-50 p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                💳 Pagamentos e Conta
              </h3>
              <div className="space-y-4">
                <details className="bg-white p-4 rounded-lg shadow-sm">
                  <summary className="font-semibold text-gray-900 cursor-pointer">
                    Quais formas de pagamento aceitam?
                  </summary>
                  <p className="mt-3 text-gray-600">
                    Aceitamos PIX (instantâneo), cartões de crédito/débito, e você pode usar 
                    seu saldo de bônus ou pontos convertidos.
                  </p>
                </details>
                
                <details className="bg-white p-4 rounded-lg shadow-sm">
                  <summary className="font-semibold text-gray-900 cursor-pointer">
                    Como uso meus bônus e descontos?
                  </summary>
                  <p className="mt-3 text-gray-600">
                    Os descontos de nível são aplicados automaticamente. Bônus em dinheiro 
                    podem ser usados como forma de pagamento durante o aluguel.
                  </p>
                </details>
                
                <details className="bg-white p-4 rounded-lg shadow-sm">
                  <summary className="font-semibold text-gray-900 cursor-pointer">
                    Posso alterar meus dados?
                  </summary>
                  <p className="mt-3 text-gray-600">
                    Claro! Acesse seu perfil para atualizar informações pessoais, 
                    foto, preferências e dados de pagamento a qualquer momento.
                  </p>
                </details>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Options */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Ainda Precisa de Ajuda?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Nossa equipe está sempre pronta para ajudar você
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <MessageCircle className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                WhatsApp
              </h3>
              <p className="text-gray-600 mb-6">
                Atendimento rápido e personalizado via WhatsApp
              </p>
              <div className="mb-4">
                <p className="font-semibold text-green-600">(11) 99999-9999</p>
                <p className="text-sm text-gray-500">Seg-Dom: 6h às 22h</p>
              </div>
              <Button className="bg-green-600 hover:bg-green-700 text-white">
                <MessageCircle className="mr-2 w-4 h-4" />
                Abrir WhatsApp
              </Button>
            </div>

            <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Mail className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Email
              </h3>
              <p className="text-gray-600 mb-6">
                Para dúvidas detalhadas ou sugestões
              </p>
              <div className="mb-4">
                <p className="font-semibold text-blue-600"><EMAIL></p>
                <p className="text-sm text-gray-500">Resposta em até 2h</p>
              </div>
              <Link href="/contato">
                <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50">
                  <Mail className="mr-2 w-4 h-4" />
                  Enviar Email
                </Button>
              </Link>
            </div>

            <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Clock className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Suporte 24/7
              </h3>
              <p className="text-gray-600 mb-6">
                Para membros Gold, Platinum e Diamond
              </p>
              <div className="mb-4">
                <p className="font-semibold text-purple-600">Sempre Disponível</p>
                <p className="text-sm text-gray-500">Resposta prioritária</p>
              </div>
              <Button variant="outline" className="border-purple-600 text-purple-600 hover:bg-purple-50">
                <Clock className="mr-2 w-4 h-4" />
                Suporte VIP
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <Link href="/">
                  <Image
                    src="/assets/logo-2.png"
                    alt="Trivvy"
                    width={48}
                    height={48}
                    className="rounded-lg cursor-pointer"
                  />
                </Link>
                <span className="text-2xl font-bold">Trivvy</span>
              </div>
              <p className="text-gray-300 text-lg mb-6 max-w-md">
                A primeira plataforma de mobilidade urbana com sistema completo de gamificação do Brasil.
              </p>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Produto</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/como-funciona" className="hover:text-white transition-colors">Como Funciona</Link></li>
                <li><Link href="/veiculos" className="hover:text-white transition-colors">Veículos Disponíveis</Link></li>
                <li><Link href="/pontos-turisticos" className="hover:text-white transition-colors">Pontos Turísticos</Link></li>
                <li><Link href="/recompensas" className="hover:text-white transition-colors">Sistema de Recompensas</Link></li>
                <li><Link href="/niveis" className="hover:text-white transition-colors">Níveis Ponto X</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Suporte & Legal</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/ajuda" className="hover:text-white transition-colors">Central de Ajuda</Link></li>
                <li><Link href="/contato" className="hover:text-white transition-colors">Fale Conosco</Link></li>
                <li><Link href="/termos" className="hover:text-white transition-colors">Termos de Uso</Link></li>
                <li><Link href="/privacidade" className="hover:text-white transition-colors">Política de Privacidade</Link></li>
                <li><Link href="/cookies" className="hover:text-white transition-colors">Política de Cookies</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="text-center md:text-left">
                <p className="text-gray-400 text-sm">
                  &copy; 2025 Trivvy. Todos os direitos reservados.
                </p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}