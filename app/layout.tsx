import type { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import '@/styles/globals.css';
import { AuthProvider } from '@/contexts/AuthContext';
import { Toaster } from 'react-hot-toast';

const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  preload: true,
  variable: '--font-inter'
});

// Desabilitar cache para toda a aplicação
export const dynamic = 'force-dynamic';
export const revalidate = 0;

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: '#19964b',
  colorScheme: 'light',
  viewportFit: 'cover',
}

export const metadata: Metadata = {
  title: {
    default: 'Trivvy - Mobilidade Urbana com Gamificação',
    template: '%s | Trivvy'
  },
  description: 'Alugue patinetes, bikes e veículos recreativos enquanto ganha pontos e recompensas no sistema Ponto X. A primeira plataforma de mobilidade urbana com gamificação do Brasil.',
  keywords: [
    'aluguel', 'patinete', 'bicicleta', 'mobilidade urbana', 'gamificação', 
    'pontos', 'recompensas', 'bike sharing', 'scooter', 'sustentabilidade',
    'transporte', 'eco-friendly', 'pontos turísticos', 'check-in', 'Brasil'
  ],
  authors: [{ name: 'Trivvy', url: 'https://trivvy.com.br' }],
  creator: 'Trivvy - websytems',
  publisher: 'Trivvy',
  category: 'Mobility',
  classification: 'Business',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
    languages: {
      'pt-BR': '/',
    }
  },
  openGraph: {
    title: 'Trivvy- Mobilidade Urbana com Gamificação',
    description: 'Alugue patinetes, bikes e veículos recreativos enquanto ganha pontos e recompensas no sistema Ponto X',
    url: '/',
    siteName: 'Trivvy',
    locale: 'pt_BR',
    type: 'website',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Trivvy - Mobilidade Urbana',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Trivvy - Mobilidade Urbana com Gamificação',
    description: 'Alugue patinetes, bikes e veículos recreativos enquanto ganha pontos e recompensas no sistema Ponto X',
    images: ['/og-image.jpg'],
    creator: '@trivvy',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Trivvy',
    startupImage: [
      {
        url: '/apple-startup-640x1136.png',
        media: '(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)',
      },
      {
        url: '/apple-startup-750x1334.png', 
        media: '(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)',
      },
      {
        url: '/apple-startup-828x1792.png',
        media: '(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)',
      },
    ],
  },
  applicationName: 'Trivvy',
  referrer: 'origin-when-cross-origin',
  verification: {
    google: process.env.GOOGLE_VERIFICATION_CODE,
  },
  other: {
    'theme-color': '#19964b',
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'Trivvy',
    'msapplication-TileColor': '#19964b',
    'msapplication-TileImage': '/ms-icon-144x144.png',
    'msapplication-config': '/browserconfig.xml',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html 
      lang="pt-BR" 
      className={inter.variable}
      data-scroll-behavior="smooth"
      suppressHydrationWarning
    >
      <head>
        {/* Preconnect to critical third-party origins */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Critical CSS and fonts preload */}
        <link rel="preload" href="/assets/logo-2.png" as="image" type="image/png" />
        
        {/* Favicon and icons */}
        <link rel="icon" href="/favicon.ico" sizes="32x32" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        
        {/* Service Worker Registration */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js', {
                    scope: '/',
                    updateViaCache: 'none'
                  }).then(function(registration) {
                    console.log('SW registered: ', registration);
                  }).catch(function(registrationError) {
                    console.log('SW registration failed: ', registrationError);
                  });
                });
              }
            `,
          }}
        />
      </head>
      <body 
        className={inter.className}
        suppressHydrationWarning
      >
        <AuthProvider>
          <div id="root" className="min-h-screen bg-gray-50">
            {children}
          </div>
          <Toaster 
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#fff',
                color: '#374151',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                fontSize: '14px',
                padding: '12px 16px',
              },
              success: {
                style: {
                  border: '1px solid #10b981',
                  background: '#f0fdf4',
                },
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#f0fdf4',
                },
              },
              error: {
                style: {
                  border: '1px solid #ef4444',
                  background: '#fef2f2',
                },
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#fef2f2',
                },
              },
            }}
          />
        </AuthProvider>
        
        {/* Skip to main content for accessibility */}
        <a 
          href="#main-content" 
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-md z-50"
        >
          Pular para o conteúdo principal
        </a>
      </body>
    </html>
  );
}