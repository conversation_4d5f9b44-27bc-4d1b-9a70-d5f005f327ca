'use client';

import { CheckCircle, Mail, ArrowLeft, Home } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import Link from 'next/link';

export default function SuccessPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card className="shadow-xl border-0">
          <CardContent className="p-8 text-center">
            {/* Ícone de sucesso */}
            <div className="mb-6">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </div>

            {/* Título principal */}
            <h1 className="text-2xl font-bold text-gray-900 mb-3">
              Cadastro Realizado com Sucesso!
            </h1>

            {/* Subtítulo */}
            <p className="text-gray-600 mb-6">
              Bem-vindo ao Trivvy! Sua conta foi criada com sucesso.
            </p>

            {/* Seção de confirmação de email */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-center mb-3">
                <Mail className="w-5 h-5 text-blue-600 mr-2" />
                <span className="text-sm font-medium text-blue-800">
                  Confirmação de Email
                </span>
              </div>
              <p className="text-sm text-blue-700 leading-relaxed">
                Enviamos um email de confirmação para o endereço cadastrado. 
                Verifique sua caixa de entrada e clique no link para ativar sua conta.
              </p>
            </div>

            {/* Instruções adicionais */}
            <div className="text-sm text-gray-500 mb-8">
              <p className="mb-2">
                <strong>Não recebeu o email?</strong>
              </p>
              <ul className="text-left space-y-1">
                <li>• Verifique sua caixa de spam</li>
                <li>• Aguarde alguns minutos</li>
                <li>• Entre em contato conosco se necessário</li>
              </ul>
            </div>

            {/* Botões de ação */}
            <div className="space-y-3">
              <Link href="/login" className="w-full">
                <Button className="w-full">
                  <Home className="w-4 h-4 mr-2" />
                  Fazer Login
                </Button>
              </Link>
              
              <Link href="/" className="w-full">
                <Button variant="outline" className="w-full">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Voltar ao Início
                </Button>
              </Link>
            </div>

            {/* Link de suporte */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-xs text-gray-500">
                Precisa de ajuda?{' '}
                <Link 
                  href="/contato" 
                  className="text-primary-600 hover:text-primary-700 font-medium"
                >
                  Entre em contato
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Informações adicionais */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Explore pontos turísticos, ganhe recompensas e alugue veículos!
          </p>
        </div>
      </div>
    </div>
  );
}