import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight, Mail, MessageCircle, MapPin, Clock, Phone } from 'lucide-react';
import { Button } from '@/components/ui/Button';

export const metadata = {
  title: 'Contato - Trivvy',
  description: 'Entre em contato com a equipe da Trivvy. WhatsApp, email e suporte especializado para ajudar você.',
};

export default function ContatoPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Link href="/">
              <Image
                src="/assets/logo-2.png"
                alt="Trivvy"
                width={148}
                height={148}
                className="rounded-lg cursor-pointer"
              />
            </Link>
          </div>
          
          <div className="flex items-center space-x-4">
            <Link href="/login">
              <Button variant="ghost" className="hidden sm:inline-flex">Entrar</Button>
            </Link>
            <Link href="/cadastro">
              <Button className="bg-primary-600 hover:bg-primary-700 min-h-[44px] px-6">
                Cadastrar Grátis
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Fale <span className="text-yellow-400">Conosco</span>
          </h1>
          <p className="text-xl text-primary-100 max-w-3xl mx-auto leading-relaxed">
            Nossa equipe está sempre pronta para ajudar você. Entre em contato através dos canais abaixo!
          </p>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <div className="bg-green-50 p-8 rounded-2xl border border-green-200 text-center">
              <div className="w-16 h-16 bg-green-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <MessageCircle className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                WhatsApp
              </h3>
              <p className="text-gray-600 mb-6">
                Atendimento rápido e personalizado
              </p>
              <div className="mb-6">
                <p className="text-2xl font-bold text-green-600">(11) 99999-9999</p>
                <p className="text-sm text-gray-500">Seg-Dom: 6h às 22h</p>
              </div>
              <Button className="bg-green-600 hover:bg-green-700 text-white w-full">
                <MessageCircle className="mr-2 w-4 h-4" />
                Abrir WhatsApp
              </Button>
            </div>

            <div className="bg-blue-50 p-8 rounded-2xl border border-blue-200 text-center">
              <div className="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Mail className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Email
              </h3>
              <p className="text-gray-600 mb-6">
                Para dúvidas detalhadas e sugestões
              </p>
              <div className="mb-6">
                <p className="text-xl font-bold text-blue-600"><EMAIL></p>
                <p className="text-sm text-gray-500">Resposta em até 2h</p>
              </div>
              <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50 w-full">
                <Mail className="mr-2 w-4 h-4" />
                Enviar Email
              </Button>
            </div>

            <div className="bg-purple-50 p-8 rounded-2xl border border-purple-200 text-center">
              <div className="w-16 h-16 bg-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Clock className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Suporte VIP
              </h3>
              <p className="text-gray-600 mb-6">
                Para membros Gold+ e Diamond
              </p>
              <div className="mb-6">
                <p className="text-xl font-bold text-purple-600">24/7 Disponível</p>
                <p className="text-sm text-gray-500">Atendimento prioritário</p>
              </div>
              <Button variant="outline" className="border-purple-600 text-purple-600 hover:bg-purple-50 w-full">
                <Clock className="mr-2 w-4 h-4" />
                Acesso VIP
              </Button>
            </div>
          </div>

          {/* Contact Form */}
          <div className="max-w-2xl mx-auto">
            <div className="bg-gray-50 p-8 rounded-2xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                Envie uma Mensagem
              </h3>
              
              <form className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nome Completo
                    </label>
                    <input 
                      type="text" 
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent"
                      placeholder="Seu nome completo"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <input 
                      type="email" 
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Assunto
                  </label>
                  <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent">
                    <option>Selecione um assunto</option>
                    <option>Dúvidas sobre aluguel</option>
                    <option>Problemas com pontos/níveis</option>
                    <option>Sugestões</option>
                    <option>Parceria/Negócios</option>
                    <option>Outros</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mensagem
                  </label>
                  <textarea 
                    rows={5}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent"
                    placeholder="Descreva sua dúvida ou sugestão..."
                  ></textarea>
                </div>
                
                <Button className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3">
                  <Mail className="mr-2 w-5 h-5" />
                  Enviar Mensagem
                </Button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Location */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Nossa Localização
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Estamos espalhados por todo o Brasil, levando mobilidade inteligente para sua cidade
            </p>
          </div>
          
          <div className="bg-white p-8 rounded-2xl shadow-sm border border-gray-100">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">
                  🏢 Escritório Principal
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-primary-600" />
                    <span>São Paulo, SP - Brasil</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-primary-600" />
                    <span>(11) 99999-9999</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-primary-600" />
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Clock className="w-5 h-5 text-primary-600" />
                    <span>Seg-Sex: 8h às 18h</span>
                  </div>
                </div>
                
                <div className="mt-8">
                  <h4 className="font-bold text-gray-900 mb-4">🌍 Cidades Atendidas:</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                    <div>• São Paulo, SP</div>
                    <div>• Rio de Janeiro, RJ</div>
                    <div>• Belo Horizonte, MG</div>
                    <div>• Brasília, DF</div>
                    <div>• Porto Alegre, RS</div>
                    <div>• Salvador, BA</div>
                    <div>• Curitiba, PR</div>
                    <div>• Fortaleza, CE</div>
                    <div>• Recife, PE</div>
                    <div>• Goiânia, GO</div>
                    <div>• Belém, PA</div>
                    <div>• Manaus, AM</div>
                    <div>• Natal, RN</div>
                    <div>• Campo Grande, MS</div>
                    <div>• João Pessoa, PB</div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-100 rounded-2xl h-80 flex items-center justify-center">
                <div className="text-center">
                  <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Mapa interativo em breve</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <Link href="/">
                  <Image
                    src="/assets/logo-2.png"
                    alt="Trivvy"
                    width={48}
                    height={48}
                    className="rounded-lg cursor-pointer"
                  />
                </Link>
                <span className="text-2xl font-bold">Trivvy</span>
              </div>
              <p className="text-gray-300 text-lg mb-6 max-w-md">
                A primeira plataforma de mobilidade urbana com sistema completo de gamificação do Brasil.
              </p>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Produto</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/como-funciona" className="hover:text-white transition-colors">Como Funciona</Link></li>
                <li><Link href="/veiculos" className="hover:text-white transition-colors">Veículos Disponíveis</Link></li>
                <li><Link href="/pontos-turisticos" className="hover:text-white transition-colors">Pontos Turísticos</Link></li>
                <li><Link href="/recompensas" className="hover:text-white transition-colors">Sistema de Recompensas</Link></li>
                <li><Link href="/niveis" className="hover:text-white transition-colors">Níveis Ponto X</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Suporte & Legal</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/ajuda" className="hover:text-white transition-colors">Central de Ajuda</Link></li>
                <li><Link href="/contato" className="hover:text-white transition-colors">Fale Conosco</Link></li>
                <li><Link href="/termos" className="hover:text-white transition-colors">Termos de Uso</Link></li>
                <li><Link href="/privacidade" className="hover:text-white transition-colors">Política de Privacidade</Link></li>
                <li><Link href="/cookies" className="hover:text-white transition-colors">Política de Cookies</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="text-center md:text-left">
                <p className="text-gray-400 text-sm">
                  &copy; 2025 Trivvy. Todos os direitos reservados.
                </p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}