import Link from 'next/link';
import Image from 'next/image';
import { <PERSON>R<PERSON>, Trophy, Crown, Star, Shield, Zap, Gift, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/Button';

export const metadata = {
  title: 'Níveis Ponto X - Trivvy',
  description: 'Conheça os 5 níveis do sistema Ponto X da Trivvy e descubra como evoluir de Bronze ao Diamond VIP com descontos progressivos.',
};

export default function NiveisPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Link href="/">
              <Image
                src="/assets/logo-2.png"
                alt="Trivvy"
                width={148}
                height={148}
                className="rounded-lg cursor-pointer"
              />
            </Link>
          </div>
          
          <div className="flex items-center space-x-4">
            <Link href="/login">
              <Button variant="ghost" className="hidden sm:inline-flex">Entrar</Button>
            </Link>
            <Link href="/cadastro">
              <Button className="bg-primary-600 hover:bg-primary-700 min-h-[44px] px-6">
                Cadastrar Grátis
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Níveis <span className="text-yellow-400">Ponto X</span>
          </h1>
          <p className="text-xl text-purple-100 max-w-3xl mx-auto leading-relaxed">
            Evolua através dos 5 níveis exclusivos e desbloqueie descontos progressivos, benefícios VIP e recompensas incríveis!
          </p>
        </div>
      </section>

      {/* Níveis Cards */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              5 Níveis de Evolução
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Cada nível oferece benefícios únicos que fazem seus aluguéis ficarem mais baratos e sua experiência mais especial
            </p>
          </div>

          <div className="space-y-8">
            {/* Bronze */}
            <div className="bg-gradient-to-r from-amber-50 to-amber-100 rounded-3xl p-8 border-2 border-amber-200">
              <div className="grid lg:grid-cols-3 gap-8 items-center">
                <div className="text-center lg:text-left">
                  <div className="w-20 h-20 bg-gradient-to-br from-amber-500 to-amber-600 rounded-2xl flex items-center justify-center mx-auto lg:mx-0 mb-4">
                    <Trophy className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-2">Nível Bronze</h3>
                  <p className="text-amber-700 font-semibold text-lg">Nível Inicial</p>
                  <div className="mt-4">
                    <span className="text-2xl font-bold text-amber-600">0+ pontos</span>
                  </div>
                </div>
                
                <div className="lg:col-span-2">
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Benefícios Bronze:</h4>
                  <div className="grid md:grid-cols-2 gap-4">
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                        <span>Acesso básico à plataforma</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                        <span>R$ 15 de bônus no cadastro</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                        <span>100 pontos grátis para começar</span>
                      </li>
                    </ul>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                        <span>Suporte padrão por WhatsApp</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                        <span>Acesso a todos os veículos</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                        <span>Check-ins turísticos</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Silver */}
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-3xl p-8 border-2 border-gray-300">
              <div className="grid lg:grid-cols-3 gap-8 items-center">
                <div className="text-center lg:text-left">
                  <div className="w-20 h-20 bg-gradient-to-br from-gray-400 to-gray-500 rounded-2xl flex items-center justify-center mx-auto lg:mx-0 mb-4">
                    <Trophy className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-2">Nível Silver</h3>
                  <p className="text-gray-600 font-semibold text-lg">5% de Desconto</p>
                  <div className="mt-4">
                    <span className="text-2xl font-bold text-gray-600">500+ pontos</span>
                  </div>
                </div>
                
                <div className="lg:col-span-2">
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Benefícios Silver:</h4>
                  <div className="grid md:grid-cols-2 gap-4">
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-gray-600" />
                        <span><strong>5% de desconto</strong> em todos os aluguéis</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-gray-600" />
                        <span><strong>+25% pontos bônus</strong> em viagens</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-gray-600" />
                        <span>Acesso a promoções exclusivas</span>
                      </li>
                    </ul>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-gray-600" />
                        <span>Badge Silver no perfil</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-gray-600" />
                        <span>Reserva com 1 dia de antecedência</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-gray-600" />
                        <span>Suporte prioritário</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Gold */}
            <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-3xl p-8 border-2 border-yellow-300">
              <div className="grid lg:grid-cols-3 gap-8 items-center">
                <div className="text-center lg:text-left">
                  <div className="w-20 h-20 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-2xl flex items-center justify-center mx-auto lg:mx-0 mb-4">
                    <Trophy className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-2">Nível Gold</h3>
                  <p className="text-yellow-700 font-semibold text-lg">10% de Desconto</p>
                  <div className="mt-4">
                    <span className="text-2xl font-bold text-yellow-600">1.500+ pontos</span>
                  </div>
                </div>
                
                <div className="lg:col-span-2">
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Benefícios Gold:</h4>
                  <div className="grid md:grid-cols-2 gap-4">
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <Crown className="w-4 h-4 text-yellow-600" />
                        <span><strong>10% de desconto</strong> em todos os aluguéis</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Crown className="w-4 h-4 text-yellow-600" />
                        <span><strong>+50% pontos bônus</strong> em viagens</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Crown className="w-4 h-4 text-yellow-600" />
                        <span>Suporte prioritário 24/7</span>
                      </li>
                    </ul>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <Crown className="w-4 h-4 text-yellow-600" />
                        <span>Badge Gold no perfil</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Crown className="w-4 h-4 text-yellow-600" />
                        <span>Reserva com 3 dias de antecedência</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Crown className="w-4 h-4 text-yellow-600" />
                        <span>Acesso a veículos premium</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Platinum */}
            <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-3xl p-8 border-2 border-purple-300">
              <div className="grid lg:grid-cols-3 gap-8 items-center">
                <div className="text-center lg:text-left">
                  <div className="w-20 h-20 bg-gradient-to-br from-purple-600 to-purple-700 rounded-2xl flex items-center justify-center mx-auto lg:mx-0 mb-4">
                    <Trophy className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-2">Nível Platinum</h3>
                  <p className="text-purple-700 font-semibold text-lg">15% de Desconto</p>
                  <div className="mt-4">
                    <span className="text-2xl font-bold text-purple-600">5.000+ pontos</span>
                  </div>
                </div>
                
                <div className="lg:col-span-2">
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Benefícios Platinum:</h4>
                  <div className="grid md:grid-cols-2 gap-4">
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <Shield className="w-4 h-4 text-purple-600" />
                        <span><strong>15% de desconto</strong> em todos os aluguéis</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Shield className="w-4 h-4 text-purple-600" />
                        <span><strong>2 aluguéis grátis</strong> por mês</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Shield className="w-4 h-4 text-purple-600" />
                        <span><strong>+75% pontos bônus</strong> em viagens</span>
                      </li>
                    </ul>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <Shield className="w-4 h-4 text-purple-600" />
                        <span>Badge Platinum exclusiva</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Shield className="w-4 h-4 text-purple-600" />
                        <span>Reserva com 7 dias de antecedência</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Shield className="w-4 h-4 text-purple-600" />
                        <span>Concierge personalizado</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Diamond */}
            <div className="bg-gradient-to-r from-cyan-50 to-cyan-100 rounded-3xl p-8 border-2 border-cyan-300 relative overflow-hidden">
              <div className="absolute top-4 right-4">
                <div className="bg-cyan-600 text-white px-4 py-2 rounded-full text-sm font-bold flex items-center gap-2">
                  <Sparkles className="w-4 h-4" />
                  NÍVEL VIP
                </div>
              </div>
              
              <div className="grid lg:grid-cols-3 gap-8 items-center">
                <div className="text-center lg:text-left">
                  <div className="w-20 h-20 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-2xl flex items-center justify-center mx-auto lg:mx-0 mb-4">
                    <Crown className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-2">Nível Diamond</h3>
                  <p className="text-cyan-700 font-semibold text-lg">25% de Desconto + VIP</p>
                  <div className="mt-4">
                    <span className="text-2xl font-bold text-cyan-600">15.000+ pontos</span>
                  </div>
                </div>
                
                <div className="lg:col-span-2">
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Benefícios Diamond VIP:</h4>
                  <div className="grid md:grid-cols-2 gap-4">
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <Sparkles className="w-4 h-4 text-cyan-600" />
                        <span><strong>25% de desconto</strong> em todos os aluguéis</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Sparkles className="w-4 h-4 text-cyan-600" />
                        <span><strong>5 aluguéis grátis</strong> por mês</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Sparkles className="w-4 h-4 text-cyan-600" />
                        <span><strong>Acesso VIP exclusivo</strong></span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Sparkles className="w-4 h-4 text-cyan-600" />
                        <span><strong>+100% pontos bônus</strong> em viagens</span>
                      </li>
                    </ul>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <Sparkles className="w-4 h-4 text-cyan-600" />
                        <span>Badge Diamond exclusiva animada</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Sparkles className="w-4 h-4 text-cyan-600" />
                        <span>Reserva ilimitada com antecedência</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Sparkles className="w-4 h-4 text-cyan-600" />
                        <span>Gerente de conta dedicado</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Sparkles className="w-4 h-4 text-cyan-600" />
                        <span>Eventos VIP exclusivos</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Como Evoluir */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Como Evoluir de Nível
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Acumule pontos através das suas atividades na plataforma e evolua automaticamente
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">
                Faça Viagens
              </h3>
              <p className="text-gray-600 text-sm mb-3">
                +50 pontos por cada viagem realizada
              </p>
              <div className="text-2xl font-bold text-green-600">+50</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">
                Check-ins Turísticos
              </h3>
              <p className="text-gray-600 text-sm mb-3">
                +100 a +500 pontos por check-in
              </p>
              <div className="text-2xl font-bold text-blue-600">+100</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Gift className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">
                Desbloqueie Conquistas
              </h3>
              <p className="text-gray-600 text-sm mb-3">
                +200 a +15.000 pontos por conquista
              </p>
              <div className="text-2xl font-bold text-purple-600">+200</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-yellow-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">
                Avalie Experiências
              </h3>
              <p className="text-gray-600 text-sm mb-3">
                +25 pontos por avaliação positiva
              </p>
              <div className="text-2xl font-bold text-yellow-600">+25</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-purple-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Comece Sua Evolução Agora!
          </h2>
          <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
            Cadastre-se gratuitamente, ganhe 100 pontos iniciais + R$ 15 de bônus e comece no nível Bronze!
          </p>
          
          <Link href="/cadastro">
            <Button 
              variant="secondary" 
              size="lg" 
              className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-10 py-5 text-xl font-bold min-h-[64px] shadow-2xl hover:shadow-primary-500/25 transition-all duration-500 group relative overflow-hidden"
            >
              <Trophy className="mr-3 w-6 h-6" />
              Começar no Bronze
              <ArrowRight className="ml-3 w-6 h-6" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <Link href="/">
                  <Image
                    src="/assets/logo-2.png"
                    alt="Trivvy"
                    width={48}
                    height={48}
                    className="rounded-lg cursor-pointer"
                  />
                </Link>
                <span className="text-2xl font-bold">Trivvy</span>
              </div>
              <p className="text-gray-300 text-lg mb-6 max-w-md">
                A primeira plataforma de mobilidade urbana com sistema completo de gamificação do Brasil.
              </p>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Produto</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/como-funciona" className="hover:text-white transition-colors">Como Funciona</Link></li>
                <li><Link href="/veiculos" className="hover:text-white transition-colors">Veículos Disponíveis</Link></li>
                <li><Link href="/pontos-turisticos" className="hover:text-white transition-colors">Pontos Turísticos</Link></li>
                <li><Link href="/recompensas" className="hover:text-white transition-colors">Sistema de Recompensas</Link></li>
                <li><Link href="/niveis" className="hover:text-white transition-colors">Níveis Ponto X</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Suporte & Legal</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/ajuda" className="hover:text-white transition-colors">Central de Ajuda</Link></li>
                <li><Link href="/contato" className="hover:text-white transition-colors">Fale Conosco</Link></li>
                <li><Link href="/termos" className="hover:text-white transition-colors">Termos de Uso</Link></li>
                <li><Link href="/privacidade" className="hover:text-white transition-colors">Política de Privacidade</Link></li>
                <li><Link href="/cookies" className="hover:text-white transition-colors">Política de Cookies</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="text-center md:text-left">
                <p className="text-gray-400 text-sm">
                  &copy; 2025 Trivvy. Todos os direitos reservados.
                </p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}