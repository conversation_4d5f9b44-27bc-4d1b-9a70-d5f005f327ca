'use client';

import { useState } from 'react';
import { useGamification } from '@/hooks/useGamification';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { AchievementCard } from '@/components/gamification/AchievementCard';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Trophy, Filter, CheckCircle, Lock, Sparkles } from 'lucide-react';

export default function AchievementsPage() {
  const {
    achievements,
    userAchievements,
    isAchievementEarned,
    loading,
  } = useGamification();
  
  const [filter, setFilter] = useState<'all' | 'earned' | 'unearned'>('all');

  const filteredAchievements = achievements.filter(achievement => {
    const isEarned = isAchievementEarned(achievement.id);
    if (filter === 'earned') return isEarned;
    if (filter === 'unearned') return !isEarned;
    return true;
  });

  const earnedCount = achievements.filter(achievement => 
    isAchievementEarned(achievement.id)
  ).length;

  const totalPoints = userAchievements.reduce((sum, ua) => 
    sum + (ua.achievement_id ? 100 : 0), 0
  );

  const achievementTypes = [
    { type: 'check_in', label: 'Check-in', color: 'bg-blue-100 text-blue-800' },
    { type: 'rental', label: 'Aluguel', color: 'bg-green-100 text-green-800' },
    { type: 'points', label: 'Pontos', color: 'bg-yellow-100 text-yellow-800' },
    { type: 'social', label: 'Social', color: 'bg-purple-100 text-purple-800' },
    { type: 'special', label: 'Especial', color: 'bg-pink-100 text-pink-800' },
    { type: 'streak', label: 'Sequência', color: 'bg-red-100 text-red-800' },
    { type: 'explorer', label: 'Explorador', color: 'bg-indigo-100 text-indigo-800' },
    { type: 'champion', label: 'Campeão', color: 'bg-orange-100 text-orange-800' },
  ];

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Carregando conquistas...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-4 md:p-6 space-y-4 md:space-y-6">
        {/* Header - Mobile-first */}
        <div className="bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg p-4 md:p-6 text-white">
          <div className="space-y-3 md:space-y-0 md:flex md:items-center md:justify-between">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold mb-1 md:mb-2 flex items-center">
                <Trophy className="w-6 h-6 md:w-8 md:h-8 mr-2 md:mr-3" />
                Conquistas
              </h1>
              <p className="text-yellow-100 text-sm md:text-base">
                Desbloqueie conquistas e ganhe pontos extras
              </p>
            </div>
            
            {/* Progress - Always visible on mobile */}
            <div className="bg-white/10 rounded-lg p-3 md:bg-transparent md:p-0 md:text-right">
              <p className="text-xl md:text-2xl font-bold">{earnedCount}/{achievements.length}</p>
              <p className="text-yellow-200 text-sm">conquistadas</p>
            </div>
          </div>
        </div>

        {/* Stats - Mobile-first grid */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-6">
          <Card>
            <CardContent className="p-4 md:p-6 text-center">
              <CheckCircle className="w-6 h-6 md:w-8 md:h-8 text-green-600 mx-auto mb-2" />
              <p className="text-xl md:text-2xl font-bold text-gray-900">{earnedCount}</p>
              <p className="text-xs md:text-sm text-gray-600">Conquistadas</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 md:p-6 text-center">
              <Lock className="w-6 h-6 md:w-8 md:h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-xl md:text-2xl font-bold text-gray-900">{achievements.length - earnedCount}</p>
              <p className="text-xs md:text-sm text-gray-600">Restantes</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 md:p-6 text-center">
              <Sparkles className="w-6 h-6 md:w-8 md:h-8 text-yellow-600 mx-auto mb-2" />
              <p className="text-xl md:text-2xl font-bold text-gray-900">{totalPoints.toLocaleString('pt-BR')}</p>
              <p className="text-xs md:text-sm text-gray-600">Pontos ganhos</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters - Mobile-optimized */}
        <Card>
          <CardHeader className="pb-3">
            <h3 className="text-base md:text-lg font-semibold text-gray-900 flex items-center">
              <Filter className="w-4 h-4 md:w-5 md:h-5 mr-2" />
              Filtros
            </h3>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-4">
              {/* Filter buttons - Touch-friendly */}
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={filter === 'all' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setFilter('all')}
                  className="min-h-[44px] px-3 md:px-4"
                >
                  Todas ({achievements.length})
                </Button>
                <Button
                  variant={filter === 'earned' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setFilter('earned')}
                  className="min-h-[44px] px-3 md:px-4"
                >
                  Conquistadas ({earnedCount})
                </Button>
                <Button
                  variant={filter === 'unearned' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setFilter('unearned')}
                  className="min-h-[44px] px-3 md:px-4"
                >
                  Restantes ({achievements.length - earnedCount})
                </Button>
              </div>

              {/* Categories - Better mobile layout */}
              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">Categorias:</p>
                <div className="flex flex-wrap gap-1.5 md:gap-2">
                  {achievementTypes.map((type) => {
                    const count = achievements.filter(a => a.type === type.type).length;
                    if (count === 0) return null;
                    
                    return (
                      <span
                        key={type.type}
                        className={`px-2 py-1 rounded-full text-xs font-medium ${type.color}`}
                      >
                        {type.label} ({count})
                      </span>
                    );
                  })}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Achievements Grid */}
        {filteredAchievements.length === 0 ? (
          <Card>
            <CardContent className="text-center p-8">
              <Trophy className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Nenhuma conquista encontrada
              </h3>
              <p className="text-gray-600">
                {filter === 'earned' 
                  ? 'Você ainda não conquistou nenhum achievement'
                  : filter === 'unearned'
                  ? 'Parabéns! Você conquistou todos os achievements disponíveis!'
                  : 'Não há conquistas disponíveis no momento'
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            {filteredAchievements.map((achievement) => {
              const userAchievement = userAchievements.find(
                ua => ua.achievement_id === achievement.id
              );
              
              return (
                <AchievementCard
                  key={achievement.id}
                  achievement={achievement}
                  isEarned={!!userAchievement}
                  earnedAt={userAchievement?.earned_at}
                  size="md"
                />
              );
            })}
          </div>
        )}

        {/* Progress Card */}
        {earnedCount > 0 && (
          <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                🎉 Progresso Excelente!
              </h3>
              <p className="text-gray-700">
                Você já conquistou {earnedCount} de {achievements.length} conquistas disponíveis.
                Continue explorando e participando para desbloquear todas!
              </p>
              
              <div className="mt-4">
                <div className="bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${(earnedCount / achievements.length) * 100}%` }}
                  />
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  {Math.round((earnedCount / achievements.length) * 100)}% completo
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}