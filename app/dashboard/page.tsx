'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useGamification } from '@/hooks/useGamification';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { StatsCards } from '@/components/dashboard/StatsCards';
import { LevelProgress } from '@/components/gamification/LevelProgress';
import { PointsDisplay } from '@/components/gamification/PointsDisplay';
import { RecentActivity } from '@/components/dashboard/RecentActivity';
import { QuickActions } from '@/components/dashboard/QuickActions';
import { Card, CardContent } from '@/components/ui/Card';
import { Loader2, Sparkles } from 'lucide-react';

export default function DashboardPage() {
  return (
    <ProtectedRoute loadingMessage="Carregando seu dashboard...">
      <DashboardContent />
    </ProtectedRoute>
  );
}

function DashboardContent() {
  const { profile, loading: authLoading } = useAuth();
  const {
    levels,
    userAchievements,
    touristSpots,
    pointTransactions,
    levelProgress,
    loading: gamificationLoading,
    refreshUserData,
  } = useGamification();
  
  const [checkIns, setCheckIns] = useState<any[]>([]);
  const [welcomeMessage, setWelcomeMessage] = useState('');

  useEffect(() => {
    if (profile) {
      // Set welcome message based on time of day
      const hour = new Date().getHours();
      let greeting = 'Boa noite';
      if (hour < 12) greeting = 'Bom dia';
      else if (hour < 18) greeting = 'Boa tarde';

      setWelcomeMessage(`${greeting}, ${profile.full_name || 'Usuário'}!`);
      
      // Refresh user data when component loads
      refreshUserData();
    }
  }, [profile, refreshUserData]);

  if (authLoading || gamificationLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="w-8 h-8 text-primary-600 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Carregando seu dashboard...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!profile) {
    return (
      <DashboardLayout>
        <Card>
          <CardContent className="text-center p-8">
            <p className="text-red-600">Erro ao carregar perfil do usuário</p>
          </CardContent>
        </Card>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-4 md:space-y-6 p-4 md:p-6">
        {/* Welcome Section - Mobile-first design */}
        <div className="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-lg p-4 md:p-6 text-white">
          <div className="space-y-3 md:space-y-0 md:flex md:items-center md:justify-between">
            <div>
              <h1 className="text-xl md:text-2xl font-bold mb-1 md:mb-2 flex items-center">
                <Sparkles className="w-5 h-5 md:w-6 md:h-6 mr-2" />
                {welcomeMessage}
              </h1>
              <p className="text-primary-100 text-sm md:text-base">
                Continue explorando e ganhe mais pontos no sistema Ponto X
              </p>
            </div>
            
            {/* Points display - Always visible on mobile */}
            <div className="bg-white/10 rounded-lg p-3 md:bg-transparent md:p-0 md:text-right">
              <p className="text-2xl md:text-3xl font-bold">{profile.points.toLocaleString('pt-BR')}</p>
              <p className="text-primary-200 text-sm">pontos disponíveis</p>
            </div>
          </div>
        </div>

        {/* Stats Cards - Improved mobile layout */}
        <StatsCards
          profile={profile}
          achievements={userAchievements}
          checkIns={checkIns}
          totalTouristSpots={touristSpots.length}
        />

        {/* Level Progress - Always visible and prominent */}
        {levelProgress && (
          <LevelProgress 
            levelProgress={levelProgress} 
            showDetails={true}
            size="lg"
          />
        )}

        {/* Points Display & Quick Actions - Mobile first */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <PointsDisplay
            profile={profile}
            showTotal={true}
            showRedeemButton={true}
            size="md"
            onRedeem={() => window.location.href = '/dashboard/recompensas'}
          />
          <QuickActions />
        </div>

        {/* Achievement Preview - Enhanced mobile design */}
        {userAchievements.length > 0 && (
          <Card>
            <CardContent className="p-4 md:p-6">
              <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">
                🏆 Última Conquista
              </h3>
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 md:w-14 md:h-14 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Sparkles className="w-6 h-6 md:w-7 md:h-7 text-yellow-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="font-medium text-gray-900 text-sm md:text-base truncate">
                    {userAchievements[0]?.achievement_id ? 'Nova Conquista!' : 'Conquista'}
                  </p>
                  <p className="text-sm text-gray-600">
                    +{userAchievements[0]?.achievement_id ? 100 : 0} pontos
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recent Activity - Mobile optimized */}
        <RecentActivity
          pointTransactions={pointTransactions}
          userAchievements={userAchievements}
          checkIns={checkIns}
        />

        {/* Tips Section - Improved mobile design */}
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-4 md:p-6">
            <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2 flex items-center">
              💡 <span className="ml-2">Dica do Dia</span>
            </h3>
            <p className="text-gray-700 text-sm md:text-base leading-relaxed">
              Visite pontos turísticos próximos para ganhar pontos extras e subir de nível mais rapidamente!
              Cada check-in pode render até 100 pontos.
            </p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}