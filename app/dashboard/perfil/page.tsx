'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { toast } from 'react-hot-toast';
import { 
  User, 
  Mail, 
  Phone, 
  Calendar,
  Edit3,
  Save,
  X,
  Camera,
  Shield,
} from 'lucide-react';

export default function ProfilePage() {
  const { profile, updateProfile, loading } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    full_name: profile?.full_name || '',
    email: profile?.email || '',
    phone: profile?.phone || '',
    birth_date: '',
  });

  // Update form data when profile changes
  useEffect(() => {
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        email: profile.email || '',
        phone: profile.phone || '',
        birth_date: '',
      });
    }
  }, [profile]);

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // Validate required fields
      if (!formData.full_name.trim()) {
        toast.error('Nome completo é obrigatório');
        setSaving(false);
        return;
      }

      //console.log('🔄 Starting profile update...');
      //console.log('📊 Data to update:', formData);
      
      const updateData = {
        full_name: formData.full_name.trim(),
        phone: formData.phone.trim() || null,
      };
      
      //console.log('📤 Calling updateProfile with:', updateData);
      
      const result = await updateProfile(updateData);
      
      //console.log('📥 Update result received:', result);
      
      if (result?.error) {
        console.error('❌ Update failed with error:', result.error);
        toast.error(result.error);
      } else {
        //console.log('✅ Update successful!');
        toast.success('Perfil atualizado com sucesso!');
        setIsEditing(false);
      }
    } catch (error) {
      console.error('💥 Unexpected error during update:', error);
      toast.error('Erro inesperado ao atualizar perfil');
    } finally {
      //console.log('🏁 Setting saving to false');
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      full_name: profile?.full_name || '',
      email: profile?.email || '',
      phone: profile?.phone || '',
      birth_date: '',
    });
    setIsEditing(false);
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-4 md:space-y-6 p-4 md:p-6">
        {/* Header - Mobile-first */}
        <div className="flex flex-col space-y-3 md:space-y-0 md:flex-row md:items-center md:justify-between">
          <h1 className="text-xl md:text-2xl font-bold text-gray-900">Meu Perfil</h1>
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)} className="flex items-center justify-center min-h-[44px]">
              <Edit3 className="w-4 h-4 mr-2" />
              Editar Perfil
            </Button>
          ) : (
            <div className="flex flex-col sm:flex-row gap-2">
              <Button 
                onClick={handleSave} 
                disabled={saving}
                className="flex items-center justify-center min-h-[44px]"
              >
                {saving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {saving ? 'Salvando...' : 'Salvar'}
              </Button>
              <Button 
                variant="outline" 
                onClick={handleCancel} 
                disabled={saving}
                className="flex items-center justify-center min-h-[44px]"
              >
                <X className="w-4 h-4 mr-2" />
                Cancelar
              </Button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
          {/* Profile Avatar & Basic Info - Mobile-optimized */}
          <Card>
            <CardContent className="p-4 md:p-6 text-center">
              <div className="relative mb-4">
                <div className="w-20 h-20 md:w-24 md:h-24 bg-primary-600 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-white text-2xl md:text-3xl font-bold">
                    {(profile?.full_name || 'U')[0].toUpperCase()}
                  </span>
                </div>
                <button className="absolute bottom-0 right-1/2 transform translate-x-1/2 translate-y-1/2 w-8 h-8 bg-secondary-600 rounded-full flex items-center justify-center text-white hover:bg-secondary-700 min-h-[32px] min-w-[32px]">
                  <Camera className="w-4 h-4" />
                </button>
              </div>
              
              <h2 className="text-lg md:text-xl font-semibold text-gray-900 mb-1">
                {profile?.full_name || 'Usuário'}
              </h2>
              <p className="text-gray-600 mb-4 text-sm md:text-base truncate">{profile?.email}</p>
              
              <div className="bg-primary-50 rounded-lg p-3 md:p-4">
                <div className="flex items-center justify-center mb-2">
                  <Shield className="w-4 h-4 md:w-5 md:h-5 text-primary-600 mr-2" />
                  <span className="text-sm font-medium text-primary-800">
                    Nível {profile?.level || 'Bronze'}
                  </span>
                </div>
                <div className="text-xl md:text-2xl font-bold text-primary-600 mb-1">
                  {profile?.total_points_earned || 0} pts
                </div>
                <div className="text-xs text-primary-700">
                  Próximo nível: {(500) - (profile?.total_points_earned || 0)} pts
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Personal Information - Mobile-optimized */}
          <Card className="lg:col-span-2">
            <CardHeader className="pb-3">
              <h3 className="text-base md:text-lg font-semibold text-gray-900">Informações Pessoais</h3>
            </CardHeader>
            <CardContent className="space-y-4 pt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <User className="w-4 h-4 inline mr-2" />
                    Nome Completo
                  </label>
                  {isEditing ? (
                    <Input
                      value={formData.full_name}
                      onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                      placeholder="Seu nome completo"
                    />
                  ) : (
                    <div className="p-3 bg-gray-50 rounded-lg text-gray-900">
                      {profile?.full_name || 'Não informado'}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Mail className="w-4 h-4 inline mr-2" />
                    Email
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg text-gray-900">
                    {profile?.email}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    O email não pode ser alterado
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Phone className="w-4 h-4 inline mr-2" />
                    Telefone
                  </label>
                  {isEditing ? (
                    <Input
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      placeholder="(11) 99999-9999"
                    />
                  ) : (
                    <div className="p-3 bg-gray-50 rounded-lg text-gray-900">
                      {profile?.phone || 'Não informado'}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="w-4 h-4 inline mr-2" />
                    Data de Nascimento
                  </label>
                  {isEditing ? (
                    <Input
                      type="date"
                      value={formData.birth_date}
                      onChange={(e) => setFormData({ ...formData, birth_date: e.target.value })}
                    />
                  ) : (
                    <div className="p-3 bg-gray-50 rounded-lg text-gray-900">
                      Não informado
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Account Stats - Mobile-first */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-6">
          <Card>
            <CardContent className="p-4 md:p-6 text-center">
              <div className="text-2xl md:text-3xl font-bold text-primary-600 mb-2">
                {profile?.total_points_earned || 0}
              </div>
              <div className="text-xs md:text-sm text-gray-600">Total de Pontos</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 md:p-6 text-center">
              <div className="text-2xl md:text-3xl font-bold text-secondary-600 mb-2">
                1
              </div>
              <div className="text-xs md:text-sm text-gray-600">Nível Atual</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 md:p-6 text-center">
              <div className="text-2xl md:text-3xl font-bold text-gold mb-2">
                0
              </div>
              <div className="text-xs md:text-sm text-gray-600">Conquistas</div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}