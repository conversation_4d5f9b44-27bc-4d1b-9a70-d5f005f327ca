'use client';

import { useState, useMemo } from 'react';
import { useTouristSpots } from '@/hooks/useTouristSpots';
import { useAuth } from '@/hooks/useAuth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { TouristSpotCard } from '@/components/tourist-spots/TouristSpotCard';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LoadingState, LoadingCards } from '@/components/ui/LoadingState';
import { EmptyTouristSpots } from '@/components/ui/EmptyState';
import { useToast, ToastContainer } from '@/components/ui/Toast';
import { 
  MapPin, 
  Search, 
  Filter,
  Map,
  Star,
  Clock,
  Trophy,
  Navigation,
  Utensils,
  Car,
  Building2,
} from 'lucide-react';
import { TouristSpotCategory } from '@/types';

export default function TouristSpotsPage() {
  const { touristSpots, checkIns, loading, checkInLoading, error, performCheckIn } = useTouristSpots();
  const { profile } = useAuth();
  const { toast, toasts, removeToast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [showVisited, setShowVisited] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<TouristSpotCategory | 'all'>('all');

  const filteredSpots = useMemo(() => {
    let filtered = touristSpots;

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(spot =>
        spot.name.toLowerCase().includes(query) ||
        spot.description?.toLowerCase().includes(query) ||
        spot.address?.toLowerCase().includes(query)
      );
    }

    // Category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(spot => spot.category === selectedCategory);
    }

    // Visited filter
    if (showVisited) {
      const visitedSpotIds = checkIns.map(checkIn => checkIn.tourist_spot_id);
      filtered = filtered.filter(spot => visitedSpotIds.includes(spot.id));
    }

    return filtered;
  }, [touristSpots, searchQuery, selectedCategory, showVisited, checkIns]);

  const totalPoints = checkIns.reduce((sum, checkIn) => sum + checkIn.points_earned, 0);
  const uniqueSpots = new Set(checkIns.map(checkIn => checkIn.tourist_spot_id)).size;

  const categoryOptions = [
    { value: 'all', label: 'Todas as Categorias', icon: Building2, color: 'text-gray-600' },
    { value: 'tourist_spot', label: 'Pontos Turísticos', icon: MapPin, color: 'text-blue-600' },
    { value: 'restaurant', label: 'Restaurantes', icon: Utensils, color: 'text-orange-600' },
    { value: 'rental_services', label: 'Locação de Passeios', icon: Car, color: 'text-green-600' },
  ];

  const handleCheckIn = async (spotId: string) => {
    try {
      await performCheckIn(spotId);
      toast.success('Check-in realizado com sucesso!', 'Pontos adicionados à sua conta');
    } catch (error: any) {
      toast.error(error.message || 'Erro ao fazer check-in');
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Pontos Turísticos</h1>
            <p className="text-gray-600">Explore a cidade e ganhe pontos fazendo check-ins</p>
          </div>
          <LoadingCards count={6} />
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <Card>
          <CardContent className="text-center p-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Tentar Novamente
            </Button>
          </CardContent>
        </Card>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-4 md:space-y-6 p-4 md:p-6">
        {/* Header - Mobile-first */}
        <div>
          <h1 className="text-xl md:text-2xl font-bold text-gray-900">Pontos Turísticos</h1>
          <p className="text-gray-600 text-sm md:text-base">
            Explore a cidade e ganhe pontos fazendo check-ins
          </p>
        </div>

        {/* Stats Cards - Mobile-optimized grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
          <Card>
            <CardContent className="p-3 md:p-4 text-center">
              <MapPin className="w-6 h-6 md:w-8 md:h-8 text-primary-600 mx-auto mb-2" />
              <div className="text-lg md:text-2xl font-bold text-gray-900">{touristSpots.length}</div>
              <div className="text-xs md:text-sm text-gray-600">Pontos Disponíveis</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3 md:p-4 text-center">
              <Trophy className="w-6 h-6 md:w-8 md:h-8 text-gold mx-auto mb-2" />
              <div className="text-lg md:text-2xl font-bold text-gray-900">{uniqueSpots}</div>
              <div className="text-xs md:text-sm text-gray-600">Pontos Visitados</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3 md:p-4 text-center">
              <Clock className="w-6 h-6 md:w-8 md:h-8 text-secondary-600 mx-auto mb-2" />
              <div className="text-lg md:text-2xl font-bold text-gray-900">{checkIns.length}</div>
              <div className="text-xs md:text-sm text-gray-600">Check-ins Realizados</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3 md:p-4 text-center">
              <Star className="w-6 h-6 md:w-8 md:h-8 text-primary-600 mx-auto mb-2" />
              <div className="text-lg md:text-2xl font-bold text-gray-900">{totalPoints}</div>
              <div className="text-xs md:text-sm text-gray-600">Pontos Ganhos</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="space-y-4">
              {/* Search */}
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      type="text"
                      placeholder="Buscar pontos turísticos..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant={showVisited ? "primary" : "outline"}
                    onClick={() => setShowVisited(!showVisited)}
                    className="whitespace-nowrap"
                  >
                    <Filter className="w-4 h-4 mr-2" />
                    {showVisited ? 'Todos' : 'Visitados'}
                  </Button>
                </div>
              </div>

              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Filtrar por Categoria
                </label>
                <div className="flex flex-wrap gap-2">
                  {categoryOptions.map((option) => {
                    const IconComponent = option.icon;
                    const isSelected = selectedCategory === option.value;
                    return (
                      <Button
                        key={option.value}
                        variant={isSelected ? "primary" : "outline"}
                        onClick={() => setSelectedCategory(option.value as TouristSpotCategory | 'all')}
                        className="whitespace-nowrap"
                        size="sm"
                      >
                        <IconComponent className={`w-4 h-4 mr-2 ${isSelected ? 'text-white' : option.color}`} />
                        {option.label}
                      </Button>
                    );
                  })}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        {filteredSpots.length === 0 ? (
          touristSpots.length === 0 ? (
            <EmptyTouristSpots />
          ) : (
            <Card>
              <CardContent className="text-center p-8">
                <Map className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Nenhum ponto turístico encontrado
                </h3>
                <p className="text-gray-600 mb-4">
                  Tente ajustar os filtros ou fazer uma nova busca
                </p>
                <Button 
                  onClick={() => {
                    setSearchQuery('');
                    setShowVisited(false);
                    setSelectedCategory('all');
                  }}
                >
                  Limpar Filtros
                </Button>
              </CardContent>
            </Card>
          )
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            {filteredSpots.map((spot) => {
              const hasVisited = checkIns.some(checkIn => checkIn.tourist_spot_id === spot.id);
              const lastCheckIn = checkIns
                .filter(checkIn => checkIn.tourist_spot_id === spot.id)
                .sort((a, b) => new Date(b.check_in_time).getTime() - new Date(a.check_in_time).getTime())[0];

              return (
                <TouristSpotCard
                  key={spot.id}
                  spot={spot}
                  hasVisited={hasVisited}
                  lastCheckIn={lastCheckIn}
                  onCheckIn={() => handleCheckIn(spot.id)}
                  checkInLoading={checkInLoading}
                />
              );
            })}
          </div>
        )}

        {/* Info Card */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Navigation className="w-5 h-5 mr-2" />
              Como fazer check-in?
            </h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-gray-600">
              <p>1. Chegue fisicamente no ponto turístico</p>
              <p>2. Clique no botão &ldquo;Fazer Check-in&rdquo; no cartão do local</p>
              <p>3. Confirme sua localização quando solicitado</p>
              <p>4. Ganhe pontos e desbloqueie conquistas!</p>
              {/* <p className="text-primary-600 font-medium mt-4">
                💡 Dica: Alguns pontos turísticos oferecem pontos extras em horários específicos!
              </p> */}
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </DashboardLayout>
  );
}