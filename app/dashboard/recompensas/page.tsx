'use client';

import { useState, useMemo } from 'react';
import { useRewards } from '@/hooks/useRewards';
import { useAuth } from '@/hooks/useAuth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { RewardCard } from '@/components/rewards/RewardCard';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { 
  Gift, 
  Star, 
  Coins,
  Trophy,
  Filter,
  Clock,
  CheckCircle,
} from 'lucide-react';

export default function RewardsPage() {
  const { rewards, userRewards, loading, error, redeemReward } = useRewards();
  const { profile } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showAvailable, setShowAvailable] = useState(true);

  const categories = useMemo(() => {
    const cats = new Set(rewards.map(reward => reward.category));
    return Array.from(cats);
  }, [rewards]);

  const filteredRewards = useMemo(() => {
    let filtered = rewards;

    // Category filter
    if (selectedCategory) {
      filtered = filtered.filter(reward => reward.category === selectedCategory);
    }

    // Availability filter
    if (showAvailable) {
      filtered = filtered.filter(reward => 
        reward.cost_points <= (profile?.points || 0) && 
        reward.is_active
      );
    }

    return filtered;
  }, [rewards, selectedCategory, showAvailable, profile?.points]);

  const usedRewards = userRewards.filter(ur => ur.status === 'used');
  const pendingRewards = userRewards.filter(ur => ur.status === 'pending');
  const totalPointsSpent = userRewards.reduce((sum, ur) => sum + (ur.points_spent || 0), 0);

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <Card>
          <CardContent className="text-center p-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Tentar Novamente
            </Button>
          </CardContent>
        </Card>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-4 md:space-y-6 p-4 md:p-6">
        {/* Header - Mobile-first */}
        <div className="space-y-3 md:space-y-0 md:flex md:items-center md:justify-between">
          <div>
            <h1 className="text-xl md:text-2xl font-bold text-gray-900">Loja de Recompensas</h1>
            <p className="text-gray-600 text-sm md:text-base">
              Troque seus pontos por recompensas incríveis
            </p>
          </div>
          
          {/* Points display - Always visible on mobile */}
          <div className="bg-primary-50 rounded-lg p-3 md:bg-transparent md:p-0 md:text-right">
            <div className="flex items-center text-xl md:text-2xl font-bold text-primary-600">
              <Coins className="w-5 h-5 md:w-6 md:h-6 mr-2" />
              {profile?.points || 0} pontos
            </div>
            <p className="text-xs md:text-sm text-gray-600">Disponíveis para troca</p>
          </div>
        </div>

        {/* Stats Cards - Mobile-optimized */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
          <Card>
            <CardContent className="p-3 md:p-4 text-center">
              <Gift className="w-6 h-6 md:w-8 md:h-8 text-primary-600 mx-auto mb-2" />
              <div className="text-lg md:text-2xl font-bold text-gray-900">{rewards.length}</div>
              <div className="text-xs md:text-sm text-gray-600">Recompensas Disponíveis</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3 md:p-4 text-center">
              <CheckCircle className="w-6 h-6 md:w-8 md:h-8 text-green-600 mx-auto mb-2" />
              <div className="text-lg md:text-2xl font-bold text-gray-900">{usedRewards.length}</div>
              <div className="text-xs md:text-sm text-gray-600">Recompensas Resgatadas</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3 md:p-4 text-center">
              <Clock className="w-6 h-6 md:w-8 md:h-8 text-yellow-600 mx-auto mb-2" />
              <div className="text-lg md:text-2xl font-bold text-gray-900">{pendingRewards.length}</div>
              <div className="text-xs md:text-sm text-gray-600">Aguardando Entrega</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3 md:p-4 text-center">
              <Trophy className="w-6 h-6 md:w-8 md:h-8 text-gold mx-auto mb-2" />
              <div className="text-lg md:text-2xl font-bold text-gray-900">{totalPointsSpent}</div>
              <div className="text-xs md:text-sm text-gray-600">Pontos Gastos</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <div className="flex gap-2">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Todas as Categorias</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>

                <Button
                  variant={showAvailable ? "primary" : "outline"}
                  onClick={() => setShowAvailable(!showAvailable)}
                  className="whitespace-nowrap"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  {showAvailable ? 'Disponíveis' : 'Todas'}
                </Button>
              </div>

              <div className="flex-1"></div>

              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span>Disponível</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-gray-400 rounded-full mr-2"></div>
                  <span>Pontos insuficientes</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                  <span>Indisponível</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* My Rewards Section */}
        {userRewards.length > 0 && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900">Minhas Recompensas</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {userRewards.slice(0, 3).map((userReward) => {
                  const reward = rewards.find(r => r.id === userReward.reward_id);
                  if (!reward) return null;

                  return (
                    <div key={userReward.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                          <Gift className="w-6 h-6 text-primary-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{reward.name}</h4>
                          <p className="text-sm text-gray-600">
                            Resgatado em {new Date(userReward.redeemed_at).toLocaleDateString('pt-BR')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={userReward.status === 'used' ? 'success' : userReward.status === 'approved' ? 'default' : 'warning'}>
                          {userReward.status === 'used' ? 'Utilizada' : userReward.status === 'approved' ? 'Aprovada' : userReward.status === 'expired' ? 'Expirada' : 'Pendente'}
                        </Badge>
                        <span className="text-sm font-medium text-gray-600">
                          -{userReward.points_spent} pts
                        </span>
                      </div>
                    </div>
                  );
                })}
                
                {userRewards.length > 3 && (
                  <div className="text-center pt-2">
                    <Button variant="ghost" size="sm">
                      Ver todas as recompensas ({userRewards.length})
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Rewards Grid */}
        {filteredRewards.length === 0 ? (
          <Card>
            <CardContent className="text-center p-8">
              <Gift className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Nenhuma recompensa encontrada
              </h3>
              <p className="text-gray-600 mb-4">
                Tente ajustar os filtros ou continue ganhando pontos
              </p>
              <Button 
                onClick={() => {
                  setSelectedCategory('');
                  setShowAvailable(false);
                }}
              >
                Ver Todas as Recompensas
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            {filteredRewards.map((reward) => {
              const canRedeem = reward.cost_points <= (profile?.points || 0) && reward.is_active;
              const alreadyRedeemed = userRewards.some(ur => ur.reward_id === reward.id);

              return (
                <RewardCard
                  key={reward.id}
                  reward={reward}
                  canRedeem={canRedeem}
                  alreadyRedeemed={alreadyRedeemed}
                  onRedeem={() => redeemReward(reward.id)}
                />
              );
            })}
          </div>
        )}

        {/* Info Card */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Star className="w-5 h-5 mr-2" />
              Como funciona?
            </h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-gray-600">
              <p>1. Ganhe pontos alugando veículos e fazendo check-ins</p>
              <p>2. Escolha uma recompensa que você tenha pontos suficientes</p>
              <p>3. Clique em &ldquo;Resgatar&rdquo; e confirme a troca</p>
              <p>4. Aguarde o processamento e receba sua recompensa!</p>
              <p className="text-primary-600 font-medium mt-4">
                💡 Dica: Novas recompensas são adicionadas toda semana!
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}