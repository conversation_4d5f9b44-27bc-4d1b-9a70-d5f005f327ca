import type { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://sx-locadora.com'

  return {
    rules: [
      {
        userAgent: '*',
        allow: [
          '/',
          '/veiculos',
          '/pontos-turisticos', 
          '/recompensas',
          '/niveis',
          '/como-funciona',
          '/ajuda',
          '/contato',
          '/login',
          '/cadastro',
          '/termos',
          '/privacidade',
          '/cookies'
        ],
        disallow: [
          '/dashboard/*',
          '/admin/*',
          '/api/*',
          '/auth/*',
          '/_next/*',
          '/private/*',
          '/temp/*',
          '*.json',
          '/success'
        ],
      },
      {
        userAgent: 'GoogleBot',
        allow: [
          '/',
          '/veiculos',
          '/pontos-turisticos',
          '/recompensas', 
          '/niveis',
          '/como-funciona',
          '/ajuda',
          '/contato',
          '/termos',
          '/privacidade',
          '/cookies'
        ],
        disallow: [
          '/dashboard/*',
          '/admin/*',
          '/api/*',
          '/auth/*'
        ],
        crawlDelay: 1,
      },
      {
        userAgent: 'bingbot',
        allow: [
          '/',
          '/veiculos',
          '/pontos-turisticos',
          '/recompensas',
          '/niveis'
        ],
        disallow: [
          '/dashboard/*',
          '/admin/*',
          '/api/*'
        ],
        crawlDelay: 2,
      }
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  }
}