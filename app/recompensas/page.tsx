import Link from 'next/link';
import Image from 'next/image';
import { <PERSON>R<PERSON>, Gift, Zap, Trophy, Crown, Star, Target, Sparkles, Award } from 'lucide-react';
import { Button } from '@/components/ui/Button';

export const metadata = {
  title: 'Sistema de Recompensas - Trivvy',
  description: 'Descubra como trocar seus pontos por descontos, aluguéis grátis e benefícios exclusivos no sistema de recompensas da Trivvy',
};

export default function RecompensasPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Link href="/">
              <Image
                src="/assets/logo-2.png"
                alt="Trivvy"
                width={148}
                height={148}
                className="rounded-lg cursor-pointer"
              />
            </Link>
          </div>
          
          <div className="flex items-center space-x-4">
            <Link href="/login">
              <Button variant="ghost" className="hidden sm:inline-flex">Entrar</Button>
            </Link>
            <Link href="/cadastro">
              <Button className="bg-primary-600 hover:bg-primary-700 min-h-[44px] px-6">
                Cadastrar Grátis
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-pink-600 via-pink-700 to-pink-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Sistema de <span className="text-yellow-400">Recompensas</span>
          </h1>
          <p className="text-xl text-pink-100 max-w-3xl mx-auto leading-relaxed">
            Transforme seus pontos em benefícios reais! Descontos progressivos, aluguéis grátis e acesso VIP exclusivo.
          </p>
        </div>
      </section>

      {/* Como Funciona */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Como Funcionam as Recompensas
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Seus pontos se transformam em benefícios automáticos e recompensas que você pode trocar
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Zap className="w-10 h-10 text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                1. Ganhe Pontos
              </h3>
              <p className="text-gray-600 text-lg">
                Acumule pontos através de viagens, check-ins turísticos e conquistas desbloqueadas.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Trophy className="w-10 h-10 text-blue-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                2. Evolua de Nível
              </h3>
              <p className="text-gray-600 text-lg">
                Com mais pontos você evolui automaticamente e ganha descontos progressivos permanentes.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Gift className="w-10 h-10 text-purple-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                3. Troque por Prêmios
              </h3>
              <p className="text-gray-600 text-lg">
                Use seus pontos para trocar por aluguéis grátis, produtos exclusivos e experiências VIP.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Descontos Progressivos */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Descontos Progressivos Automáticos
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Quanto mais você usar, mais você economiza! Descontos permanentes que crescem com seu nível.
            </p>
          </div>

          <div className="grid md:grid-cols-5 gap-6">
            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-amber-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Bronze</h3>
              <div className="text-3xl font-bold text-amber-600 mb-2">0%</div>
              <p className="text-sm text-gray-600">Preço normal</p>
              <div className="mt-4 text-xs text-gray-500">0+ pontos</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-gray-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Silver</h3>
              <div className="text-3xl font-bold text-gray-600 mb-2">5%</div>
              <p className="text-sm text-gray-600">R$ 20 → R$ 19</p>
              <div className="mt-4 text-xs text-gray-500">500+ pontos</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-yellow-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Gold</h3>
              <div className="text-3xl font-bold text-yellow-600 mb-2">10%</div>
              <p className="text-sm text-gray-600">R$ 20 → R$ 18</p>
              <div className="mt-4 text-xs text-gray-500">1.500+ pontos</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Platinum</h3>
              <div className="text-3xl font-bold text-purple-600 mb-2">15%</div>
              <p className="text-sm text-gray-600">R$ 20 → R$ 17</p>
              <div className="mt-4 text-xs text-gray-500">5.000+ pontos</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center hover:shadow-lg transition-shadow relative">
              <div className="absolute -top-2 -right-2">
                <div className="bg-cyan-500 text-white px-2 py-1 rounded-full text-xs font-bold">VIP</div>
              </div>
              <div className="w-16 h-16 bg-cyan-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Crown className="w-8 h-8 text-cyan-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Diamond</h3>
              <div className="text-3xl font-bold text-cyan-600 mb-2">25%</div>
              <p className="text-sm text-gray-600">R$ 20 → R$ 15</p>
              <div className="mt-4 text-xs text-gray-500">15.000+ pontos</div>
            </div>
          </div>
        </div>
      </section>

      {/* Loja de Recompensas */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Loja de Recompensas
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Troque seus pontos por benefícios exclusivos e experiências únicas
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Aluguéis Grátis */}
            <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-3xl border border-green-200">
              <div className="text-center">
                <div className="w-20 h-20 bg-green-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Zap className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Aluguéis Grátis
                </h3>
                <p className="text-gray-600 mb-6">
                  Troque seus pontos por aluguéis completamente gratuitos
                </p>
                
                <div className="space-y-4">
                  <div className="bg-white p-4 rounded-2xl border border-green-200">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">🛴 Patinete (2h)</span>
                      <span className="font-bold text-green-600">800 pontos</span>
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-2xl border border-green-200">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">🚲 Bike (4h)</span>
                      <span className="font-bold text-green-600">1.200 pontos</span>
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-2xl border border-green-200">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">🚗 Carro (1 dia)</span>
                      <span className="font-bold text-green-600">3.000 pontos</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Produtos Exclusivos */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-3xl border border-blue-200">
              <div className="text-center">
                <div className="w-20 h-20 bg-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Gift className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Produtos Exclusivos
                </h3>
                <p className="text-gray-600 mb-6">
                  Itens e acessórios exclusivos da marca Trivvy
                </p>
                
                <div className="space-y-4">
                  <div className="bg-white p-4 rounded-2xl border border-blue-200">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">🎒 Mochila Trivvy</span>
                      <span className="font-bold text-blue-600">1.500 pontos</span>
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-2xl border border-blue-200">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">👕 Camiseta Trivvy</span>
                      <span className="font-bold text-blue-600">2.000 pontos</span>
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-2xl border border-blue-200">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">🧢 Boné Trivvy</span>
                      <span className="font-bold text-blue-600">1.000 pontos</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Experiências VIP */}
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-3xl border border-purple-200">
              <div className="text-center">
                <div className="w-20 h-20 bg-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Crown className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Experiências VIP
                </h3>
                <p className="text-gray-600 mb-6">
                  Experiências exclusivas apenas para membros especiais
                </p>
                
                <div className="space-y-4">
                  <div className="bg-white p-4 rounded-2xl border border-purple-200">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">🎟️ Evento VIP</span>
                      <span className="font-bold text-purple-600">5.000 pontos</span>
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-2xl border border-purple-200">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">🏆 Meet & Greet</span>
                      <span className="font-bold text-purple-600">7.500 pontos</span>
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-2xl border border-purple-200">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">✈️ Viagem Exclusiva</span>
                      <span className="font-bold text-purple-600">25.000 pontos</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Conquistas Especiais */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Conquistas que Dão Pontos Extras
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Desbloqueie conquistas especiais e ganhe milhares de pontos extras
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-8 h-8 text-yellow-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-2">🎯 Primeira Viagem</h3>
              <p className="text-sm text-gray-600 mb-3">Complete sua primeira viagem</p>
              <div className="text-xl font-bold text-yellow-600">+200 pontos</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-2">🗓️ Usuário Semanal</h3>
              <p className="text-sm text-gray-600 mb-3">Use a plataforma por 7 dias seguidos</p>
              <div className="text-xl font-bold text-green-600">+500 pontos</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trophy className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-2">📸 Primeiro Check-in</h3>
              <p className="text-sm text-gray-600 mb-3">Faça seu primeiro check-in turístico</p>
              <div className="text-xl font-bold text-blue-600">+300 pontos</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-2">👥 Indique um Amigo</h3>
              <p className="text-sm text-gray-600 mb-3">Convide um amigo que se cadastre</p>
              <div className="text-xl font-bold text-purple-600">+1.000 pontos</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-2">🌟 Avaliador 5 Estrelas</h3>
              <p className="text-sm text-gray-600 mb-3">Dê 10 avaliações 5 estrelas</p>
              <div className="text-xl font-bold text-red-600">+750 pontos</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-cyan-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-8 h-8 text-cyan-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-2">🎂 Aniversariante</h3>
              <p className="text-sm text-gray-600 mb-3">Faça uma viagem no seu aniversário</p>
              <div className="text-xl font-bold text-cyan-600">+1.500 pontos</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Crown className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-2">🏆 Maratonista</h3>
              <p className="text-sm text-gray-600 mb-3">Complete 100 viagens</p>
              <div className="text-xl font-bold text-orange-600">+5.000 pontos</div>
            </div>

            <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-8 h-8 text-pink-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-2">💎 Membro VIP</h3>
              <p className="text-sm text-gray-600 mb-3">Alcance o nível Diamond</p>
              <div className="text-xl font-bold text-pink-600">+10.000 pontos</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-20 bg-gradient-to-r from-pink-600 to-pink-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Comece a Coletar Recompensas!
          </h2>
          <p className="text-xl text-pink-100 mb-8 max-w-2xl mx-auto">
            Cadastre-se gratuitamente e ganhe 100 pontos iniciais + R$ 15 de bônus para começar a trocar por recompensas!
          </p>
          
          <Link href="/cadastro">
            <Button 
              variant="secondary" 
              size="lg" 
              className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-10 py-5 text-xl font-bold min-h-[64px] shadow-2xl hover:shadow-primary-500/25 transition-all duration-500 group relative overflow-hidden"
            >
              <Gift className="mr-3 w-6 h-6" />
              Começar a Ganhar Recompensas
              <ArrowRight className="ml-3 w-6 h-6" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <Link href="/">
                  <Image
                    src="/assets/logo-2.png"
                    alt="STrivvy"
                    width={48}
                    height={48}
                    className="rounded-lg cursor-pointer"
                  />
                </Link>
                <span className="text-2xl font-bold">Trivvy</span>
              </div>
              <p className="text-gray-300 text-lg mb-6 max-w-md">
                A primeira plataforma de mobilidade urbana com sistema completo de gamificação do Brasil.
              </p>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Produto</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/como-funciona" className="hover:text-white transition-colors">Como Funciona</Link></li>
                <li><Link href="/veiculos" className="hover:text-white transition-colors">Veículos Disponíveis</Link></li>
                <li><Link href="/pontos-turisticos" className="hover:text-white transition-colors">Pontos Turísticos</Link></li>
                <li><Link href="/recompensas" className="hover:text-white transition-colors">Sistema de Recompensas</Link></li>
                <li><Link href="/niveis" className="hover:text-white transition-colors">Níveis Ponto X</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Suporte & Legal</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/ajuda" className="hover:text-white transition-colors">Central de Ajuda</Link></li>
                <li><Link href="/contato" className="hover:text-white transition-colors">Fale Conosco</Link></li>
                <li><Link href="/termos" className="hover:text-white transition-colors">Termos de Uso</Link></li>
                <li><Link href="/privacidade" className="hover:text-white transition-colors">Política de Privacidade</Link></li>
                <li><Link href="/cookies" className="hover:text-white transition-colors">Política de Cookies</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="text-center md:text-left">
                <p className="text-gray-400 text-sm">
                  &copy; 2025 Trivvy. Todos os direitos reservados.
                </p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}