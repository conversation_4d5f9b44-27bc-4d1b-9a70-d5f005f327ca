-- Deletar conquistas de teste malformadas
DELETE FROM achievements WHERE name IN ('teste', 'Teste', 'oioioi', 'PRIMEIRÃO', 'tetetet', 'Teste Teste Teste', 'aeaeaeae');

-- Inserir conquistas corretas baseadas em pontos
INSERT INTO achievements (name, description, icon, type, condition, reward_points, is_active) VALUES
('Primeiro Passo', 'Acumule seus primeiros 50 pontos', 'star', 'points', '{"total_points": 50}', 25, true),
('Centena', 'Acumule 100 pontos totais', 'trophy', 'points', '{"total_points": 100}', 50, true),
('Colecionador', 'Acumule 500 pontos totais', 'coins', 'points', '{"total_points": 500}', 100, true),
('Mi<PERSON><PERSON>rio', 'Acumule 1000 pontos totais', 'gem', 'points', '{"total_points": 1000}', 200, true)
ON CONFLICT (name) DO UPDATE SET
  description = EXCLUDED.description,
  condition = EXCLUDED.condition,
  reward_points = EXCLUDED.reward_points,
  is_active = EXCLUDED.is_active;