-- Adicionar uma conquista simples para 100 pontos para teste
INSERT INTO achievements (name, description, icon, type, condition, reward_points, is_active) VALUES
('Primeiros Passos', 'Acumule seus primeiros 100 pontos', 'star', 'points', '{"total_points": 100}', 50, true)
ON CONFLICT (name) DO UPDATE SET
  description = EXCLUDED.description,
  condition = EXCLUDED.condition,
  reward_points = EXCLUDED.reward_points,
  is_active = EXCLUDED.is_active;

-- Adicionar conquista ainda mais simples para 50 pontos
INSERT INTO achievements (name, description, icon, type, condition, reward_points, is_active) VALUES
('Iní<PERSON> da Jornada', 'Acumule seus primeiros 50 pontos', 'trophy', 'points', '{"total_points": 50}', 25, true)
ON CONFLICT (name) DO UPDATE SET
  description = EXCLUDED.description,
  condition = EXCLUDED.condition,
  reward_points = EXCLUDED.reward_points,
  is_active = EXCLUDED.is_active;