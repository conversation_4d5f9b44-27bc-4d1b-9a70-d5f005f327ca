// Service Worker para SX Locadora PWA
// Versão baseada nas melhores práticas do Next.js 15

const CACHE_NAME = 'sx-locadora-v1'
const STATIC_CACHE_NAME = 'sx-static-v1'
const DYNAMIC_CACHE_NAME = 'sx-dynamic-v1'

// Recursos para cache estático
const STATIC_ASSETS = [
  '/',
  '/assets/logo-2.png',
  '/manifest.json',
  '/offline',
  // Adicionar outros recursos críticos conforme necessário
]

// Instalar Service Worker
self.addEventListener('install', (event) => {
  console.log('[SW] Installing Service Worker...')
  
  event.waitUntil(
    Promise.all([
      // Cache recursos estáticos
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        console.log('[SW] Caching static assets')
        return cache.addAll(STATIC_ASSETS)
      }),
      // Pular espera para ativar imediatamente
      self.skipWaiting()
    ])
  )
})

// Ativar Service Worker
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating Service Worker...')
  
  event.waitUntil(
    Promise.all([
      // Limpar caches antigos
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (
              cacheName !== STATIC_CACHE_NAME &&
              cacheName !== DYNAMIC_CACHE_NAME &&
              cacheName !== CACHE_NAME
            ) {
              console.log('[SW] Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      }),
      // Tomar controle de todas as abas
      self.clients.claim()
    ])
  )
})

// Interceptar requisições
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Ignorar requisições para outros domínios e extensões
  if (
    !url.origin.includes(self.location.origin) ||
    url.pathname.startsWith('/api/') ||
    url.pathname.includes('.') && !url.pathname.endsWith('.html')
  ) {
    return
  }

  // Estratégia: Cache First para recursos estáticos
  if (STATIC_ASSETS.includes(url.pathname)) {
    event.respondWith(
      caches.match(request).then((response) => {
        return response || fetch(request)
      })
    )
    return
  }

  // Estratégia: Network First para páginas dinâmicas
  if (request.mode === 'navigate') {
    event.respondWith(
      fetch(request)
        .then((response) => {
          // Cache successful responses
          if (response.status === 200) {
            const responseClone = response.clone()
            caches.open(DYNAMIC_CACHE_NAME).then((cache) => {
              cache.put(request, responseClone)
            })
          }
          return response
        })
        .catch(() => {
          // Fallback para cache ou página offline
          return caches.match(request).then((response) => {
            return response || caches.match('/offline')
          })
        })
    )
    return
  }

  // Estratégia: Cache First para outros recursos
  event.respondWith(
    caches.match(request).then((response) => {
      if (response) {
        return response
      }

      return fetch(request).then((response) => {
        // Cache apenas respostas válidas
        if (response.status === 200 && request.method === 'GET') {
          const responseClone = response.clone()
          caches.open(DYNAMIC_CACHE_NAME).then((cache) => {
            cache.put(request, responseClone)
          })
        }
        return response
      })
    })
  )
})

// Push Notifications
self.addEventListener('push', (event) => {
  if (!event.data) return

  const data = event.data.json()
  const options = {
    body: data.body || 'Nova notificação da SX Locadora',
    icon: data.icon || '/assets/logo-2.png',
    badge: '/assets/logo-2.png',
    image: data.image,
    vibrate: [100, 50, 100, 50, 100],
    data: {
      ...data,
      dateOfArrival: Date.now(),
      primaryKey: '1'
    },
    actions: [
      {
        action: 'explore',
        title: 'Ver no App',
        icon: '/icons/explore.png'
      },
      {
        action: 'close',
        title: 'Fechar',
        icon: '/icons/close.png'
      }
    ],
    requireInteraction: false,
    persistent: true,
    renotify: true,
    tag: data.tag || 'sx-notification'
  }

  event.waitUntil(
    self.registration.showNotification(
      data.title || 'SX Locadora',
      options
    )
  )
})

// Clique em notificação
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification click received:', event.notification.data)
  
  event.notification.close()

  if (event.action === 'close') {
    return
  }

  const urlToOpen = event.notification.data?.url || '/'

  event.waitUntil(
    self.clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    }).then((clientList) => {
      // Verificar se já existe uma aba aberta
      for (const client of clientList) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          client.navigate(urlToOpen)
          return client.focus()
        }
      }
      
      // Abrir nova aba se não existir
      if (self.clients.openWindow) {
        return self.clients.openWindow(urlToOpen)
      }
    })
  )
})

// Background Sync
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync event:', event.tag)
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Implementar sincronização de dados offline
      doBackgroundSync()
    )
  }
})

async function doBackgroundSync() {
  try {
    // Implementar lógica de sincronização
    console.log('[SW] Performing background sync...')
    // Sync pending data, user actions, etc.
  } catch (error) {
    console.error('[SW] Background sync failed:', error)
  }
}

// Atualização do Service Worker
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
})

console.log('[SW] Service Worker loaded successfully')