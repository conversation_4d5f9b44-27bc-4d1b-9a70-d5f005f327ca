/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Brand colors for SX Locadora
        primary: {
          50: '#e8f5ed',
          100: '#d1eadb',
          200: '#a3d5b7',
          300: '#75c093',
          400: '#47ab6f',
          500: '#19964b',
          600: '#05a658',
          700: '#047a44',
          800: '#035530',
          900: '#022f1c',
          950: '#011408',
        },
        secondary: {
          50: '#f8f7f5',
          100: '#f0efeb',
          200: '#e1dfd7',
          300: '#d1cfc2',
          400: '#c2bfae',
          500: '#b3af9a',
          600: '#a49f86',
          700: '#82c5a1',
          800: '#6a9e83',
          900: '#527765',
          950: '#3a5047',
        },
        black: '#000000',
        // Gamification colors
        bronze: '#cd7f32',
        silver: '#c0c0c0',
        gold: '#ffd700',
        platinum: '#e5e4e2',
        diamond: '#b9f2ff',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'bounce-light': 'bounceLight 0.6s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        bounceLight: {
          '0%, 20%, 50%, 80%, 100%': { transform: 'translateY(0)' },
          '40%': { transform: 'translateY(-5px)' },
          '60%': { transform: 'translateY(-3px)' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
};