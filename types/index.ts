import { Database } from './database.types';

// Database types
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Level = Database['public']['Tables']['levels']['Row'];
export type Achievement = Database['public']['Tables']['achievements']['Row'];
export type UserAchievement = Database['public']['Tables']['user_achievements']['Row'];
export type TouristSpot = Database['public']['Tables']['tourist_spots']['Row'];
export type CheckIn = Database['public']['Tables']['check_ins']['Row'];
export type PointTransaction = Database['public']['Tables']['point_transactions']['Row'];
export type Reward = Database['public']['Tables']['rewards']['Row'];
export type RewardRedemption = Database['public']['Tables']['reward_redemptions']['Row'];
export type Vehicle = Database['public']['Tables']['vehicles']['Row'];
export type AppSetting = Database['public']['Tables']['app_settings']['Row'];

// Insert types
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type LevelInsert = Database['public']['Tables']['levels']['Insert'];
export type AchievementInsert = Database['public']['Tables']['achievements']['Insert'];
export type UserAchievementInsert = Database['public']['Tables']['user_achievements']['Insert'];
export type TouristSpotInsert = Database['public']['Tables']['tourist_spots']['Insert'];
export type CheckInInsert = Database['public']['Tables']['check_ins']['Insert'];
export type PointTransactionInsert = Database['public']['Tables']['point_transactions']['Insert'];
export type RewardInsert = Database['public']['Tables']['rewards']['Insert'];
export type RewardRedemptionInsert = Database['public']['Tables']['reward_redemptions']['Insert'];
export type VehicleInsert = Database['public']['Tables']['vehicles']['Insert'];
export type AppSettingInsert = Database['public']['Tables']['app_settings']['Insert'];

// Update types
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];
export type LevelUpdate = Database['public']['Tables']['levels']['Update'];
export type AchievementUpdate = Database['public']['Tables']['achievements']['Update'];
export type UserAchievementUpdate = Database['public']['Tables']['user_achievements']['Update'];
export type TouristSpotUpdate = Database['public']['Tables']['tourist_spots']['Update'];
export type CheckInUpdate = Database['public']['Tables']['check_ins']['Update'];
export type PointTransactionUpdate = Database['public']['Tables']['point_transactions']['Update'];
export type RewardUpdate = Database['public']['Tables']['rewards']['Update'];
export type RewardRedemptionUpdate = Database['public']['Tables']['reward_redemptions']['Update'];
export type VehicleUpdate = Database['public']['Tables']['vehicles']['Update'];
export type AppSettingUpdate = Database['public']['Tables']['app_settings']['Update'];

// Extended types with relations
export interface ProfileWithLevel extends Profile {
  level_info?: Level;
}

export interface UserAchievementWithDetails extends UserAchievement {
  achievement?: Achievement;
}

export interface CheckInWithDetails extends CheckIn {
  tourist_spot?: TouristSpot;
}

export interface RewardRedemptionWithDetails extends RewardRedemption {
  reward?: Reward;
}

// Enums
export type UserRole = 'user' | 'admin';
export type TransactionType = 'earned' | 'redeemed' | 'bonus' | 'penalty';
export type TransactionSource = 'check_in' | 'achievement' | 'admin' | 'referral' | 'rental' | 'social';
export type AchievementType = 'check_in' | 'rental' | 'points' | 'social' | 'special' | 'streak' | 'explorer' | 'champion';
export type RewardType = 'discount' | 'free_rental' | 'merchandise' | 'experience' | 'partner_benefit';
export type RedemptionStatus = 'pending' | 'approved' | 'used' | 'expired';
export type VehicleType = 'scooter' | 'bike' | 'e-bike' | 'skateboard';
export type TouristSpotCategory = 'tourist_spot' | 'restaurant' | 'rental_services';

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
  phone: string;
}

export interface ProfileForm {
  fullName: string;
  phone: string;
  email: string;
}

// API Response types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

// Gamification types
export interface LevelProgress {
  currentLevel: Level;
  nextLevel?: Level;
  progress: number; // 0-100
  pointsToNext: number;
}

export interface LeaderboardEntry {
  profile: Profile;
  rank: number;
  points: number;
}

// WhatsApp integration
export interface WhatsAppMessage {
  vehicle: Vehicle;
  userPhone: string;
  userName: string;
  duration: string;
  location?: string;
}