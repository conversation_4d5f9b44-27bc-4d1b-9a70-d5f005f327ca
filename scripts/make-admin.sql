-- Script para tornar um usuário admin
-- Substitua '<EMAIL>' pelo email do usuário que deve ser admin

UPDATE profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';

-- Para verificar se funcionou:
SELECT id, email, full_name, role FROM profiles WHERE role = 'admin';

-- Para tornar o primeiro usuário cadastrado admin (caso não saiba o email):
-- UPDATE profiles 
-- SET role = 'admin' 
-- WHERE id = (SELECT id FROM profiles ORDER BY created_at ASC LIMIT 1);