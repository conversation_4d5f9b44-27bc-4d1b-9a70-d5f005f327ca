import { Vehicle, Profile } from '@/types';

export class WhatsAppService {
  private baseWhatsAppUrl = 'https://wa.me/';


  // removed itens
// ⏰ *Duração:* ${duration}
// 📋 *Características:*
// ${this.formatVehicleFeatures(vehicle)}

  generateRentalMessage(vehicle: Vehicle, profile: Profile, duration: string): string {
    const userName = profile.full_name || 'Usuário';
    const userPhone = profile.phone || 'Não informado';
    
    const message = `🚲 *Solicitação de Aluguel - Trivvy*

👤 *Cliente:* ${userName}
📱 *Telefone:* ${userPhone}

🛴 *Veículo:* ${vehicle.name}
💰 *Preço:* R$ ${vehicle.hourly_price.toFixed(2)}


📍 *Localização:* ${vehicle.location || 'A definir'}

---
_Mensagem gerada automaticamente pelo app Trivvy_`;

    return message;
  }

  generateWhatsAppUrl(phoneNumber: string, message: string): string {
    // Validate inputs
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      throw new Error('Número de telefone inválido');
    }
    
    if (!message || typeof message !== 'string') {
      throw new Error('Mensagem inválida');
    }
    
    // Clean phone number (remove all non-digits)
    const cleanPhone = phoneNumber.replace(/\D/g, '');
    
    if (!cleanPhone) {
      throw new Error('Número de telefone não contém dígitos válidos');
    }
    
    // Ensure it starts with country code
    const formattedPhone = cleanPhone.startsWith('55') ? cleanPhone : `55${cleanPhone}`;
    
    // Encode message for URL
    const encodedMessage = encodeURIComponent(message);
    
    return `${this.baseWhatsAppUrl}${formattedPhone}?text=${encodedMessage}`;
  }

  openWhatsApp(phoneNumber: string, message: string): void {
    try {
      const url = this.generateWhatsAppUrl(phoneNumber, message);
      window.open(url, '_blank');
    } catch (error) {
      console.error('Erro ao abrir WhatsApp:', error);
      alert('Erro ao abrir WhatsApp. Verifique se o número está configurado corretamente.');
    }
  }

  private formatVehicleFeatures(vehicle: Vehicle): string {
    if (!vehicle.features) return 'Características não informadas';
    
    try {
      const features = typeof vehicle.features === 'string' 
        ? JSON.parse(vehicle.features) 
        : vehicle.features;
      
      return Object.entries(features)
        .map(([key, value]) => `• ${this.formatFeatureKey(key)}: ${value}`)
        .join('\n');
    } catch (error) {
      return 'Características não informadas';
    }
  }

  private formatFeatureKey(key: string): string {
    const keyMap: Record<string, string> = {
      'max_speed': 'Velocidade máxima',
      'autonomy': 'Autonomia',
      'weight': 'Peso',
      'lights': 'Luzes',
      'gears': 'Marchas',
      'basket': 'Cesta',
      'comfort_seat': 'Assento confortável',
      'battery_range': 'Autonomia da bateria',
      'power': 'Potência',
      'display': 'Display',
      'length': 'Comprimento',
      'trucks': 'Trucks',
      'wheels': 'Rodas',
      'grip_tape': 'Grip tape',
      'style': 'Estilo',
      'bell': 'Campainha',
      'comfort_grip': 'Punhos confortáveis',
      'foldable': 'Dobrável',
    };
    
    return keyMap[key] || key;
  }
}

export const whatsappService = new WhatsAppService();