import { createClient } from '@/utils/supabase/client';
import { 
  Profile, 
  Level, 
  Achievement, 
  UserAchievement, 
  TouristSpot, 
  CheckIn, 
  PointTransaction,
  LevelProgress,
  PointTransactionInsert,
  CheckInInsert,
  UserAchievementInsert,
} from '@/types';

export class GamificationService {
  private supabase = createClient();
  private checkingAchievements = new Set<string>(); // Track users currently being processed
  private debugMode = process.env.NODE_ENV === 'development';

  // Points management
  async addPoints(
    userId: string, 
    points: number, 
    source: PointTransactionInsert['source'], 
    description: string,
    metadata?: any,
    skipAchievementCheck = false // Flag para evitar loop infinito
  ): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('addPoints', `Adding ${points} points for user ${userId} from ${source}`);
      
      // 1) Criar a transação de pontos
    const { error: transactionError } = await this.supabase
      .from('point_transactions')
      .insert({
        user_id: userId,
        points: points,
        type: 'earned',                // mesmo valor que você passava no RPC
        source: source,
        description: description,
        metadata: metadata ? JSON.stringify(metadata) : null,
        created_at: new Date().toISOString()
      });

    if (transactionError) {
      console.error('Error inserting point transaction:', transactionError);
      return { success: false, error: transactionError.message };
    }

    // 2) Buscar os pontos atuais do usuário
    const { data: profileData, error: profileFetchError } = await this.supabase
      .from('profiles')
      .select('points, total_points_earned')
      .eq('id', userId)
      .single();

    if (profileFetchError || !profileData) {
      console.error('Error fetching profile before update:', profileFetchError);
      return { success: false, error: profileFetchError?.message || 'Perfil não encontrado' };
    }

    // 3) Atualizar os valores somando os pontos
    const { error: profileUpdateError } = await this.supabase
      .from('profiles')
      .update({
        points: profileData.points + points,
        total_points_earned: profileData.total_points_earned + points,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (profileUpdateError) {
      console.error('Error updating profile:', profileUpdateError);
      return { success: false, error: profileUpdateError.message };
    }

      this.log('addPoints', `Successfully added ${points} points for user ${userId}`);

      // Check for new achievements apenas se não for de achievement para evitar loop
      if (!skipAchievementCheck && source !== 'achievement') {
        this.log('addPoints', 'Checking achievements after adding points...');
        await this.checkAchievements(userId);
      }

      return { success: true };
    } catch (error) {
      console.error('Error adding points:', error);
      return { success: false, error: 'Erro ao adicionar pontos' };
    }
  }

  async deductPoints(
    userId: string, 
    points: number, 
    source: PointTransactionInsert['source'], 
    description: string,
    metadata?: any
  ): Promise<{ success: boolean; error?: string }> {
    try {
      try {
        // 1) Inserir transação de pontos (redeemed)
        const { error: transactionError } = await this.supabase
          .from('point_transactions')
          .insert({
            user_id: userId,
            points: points,
            type: 'redeemed',
            source: source,
            description: description,
            metadata: metadata ? JSON.stringify(metadata) : null,
            created_at: new Date().toISOString(),
          });

        if (transactionError) {
          console.error('Error inserting redeemed transaction:', transactionError);
          return { success: false, error: transactionError.message };
        }

        // 2) Buscar pontos atuais
        const { data: profileData, error: profileFetchError } = await this.supabase
          .from('profiles')
          .select('points')
          .eq('id', userId)
          .single();

        if (profileFetchError || !profileData) {
          console.error('Error fetching profile before redeem:', profileFetchError);
          return { success: false, error: profileFetchError?.message || 'Perfil não encontrado' };
        }

        // 3) Atualizar o saldo (subtraindo os pontos)
        const { error: profileUpdateError } = await this.supabase
          .from('profiles')
          .update({
            points: profileData.points - points,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        if (profileUpdateError) {
          console.error('Error updating profile after redeem:', profileUpdateError);
          return { success: false, error: profileUpdateError.message };
        }

        return { success: true };
      } catch (err: any) {
        console.error('Unexpected error in redeem points:', err);
        return { success: false, error: 'Erro ao resgatar pontos' };
      }

    } catch (error) {
      console.error('Error deducting points:', error);
      return { success: false, error: 'Erro ao resgatar pontos' };
    }
  }

  // Level management
  async getLevels(): Promise<Level[]> {
    try {
      const { data, error } = await this.supabase
        .from('levels')
        .select('*')
        .order('min_points', { ascending: true });

      if (error) {
        console.error('Error fetching levels:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Exception fetching levels:', {
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  async calculateLevelProgress(profile: Profile): Promise<LevelProgress> {
    const levels = await this.getLevels();
    
    // Find the highest level the user qualifies for by checking from highest to lowest
    let currentLevel = levels[0]; // Default to Bronze
    for (let i = levels.length - 1; i >= 0; i--) {
      if (profile.points >= levels[i].min_points) {
        currentLevel = levels[i];
        break;
      }
    }

    const currentLevelIndex = levels.findIndex(level => level.id === currentLevel.id);
    const nextLevel = levels[currentLevelIndex + 1];

    let progress = 0;
    let pointsToNext = 0;

    if (nextLevel) {
      const pointsInCurrentLevel = profile.points - currentLevel.min_points;
      const pointsNeededForNext = nextLevel.min_points - currentLevel.min_points;
      progress = Math.round((pointsInCurrentLevel / pointsNeededForNext) * 100);
      pointsToNext = nextLevel.min_points - profile.points;
    } else {
      progress = 100; // Max level reached
      pointsToNext = 0;
    }

    return {
      currentLevel,
      nextLevel,
      progress: Math.max(0, Math.min(100, progress)),
      pointsToNext: Math.max(0, pointsToNext),
    };
  }

  // Achievement management
  async getAchievements(): Promise<Achievement[]> {
    try {
      const { data, error } = await this.supabase
        .from('achievements')
        .select('*')
        .eq('is_active', true)
        .order('reward_points', { ascending: true });

      if (error) {
        console.error('Error fetching achievements:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Exception fetching achievements:', {
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  async getUserAchievements(userId: string): Promise<UserAchievement[]> {
    try {
      const { data, error } = await this.supabase
        .from('user_achievements')
        .select(`
          *,
          achievement:achievements(*)
        `)
        .eq('user_id', userId)
        .order('earned_at', { ascending: false });

      if (error) {
        console.error('Error fetching user achievements:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          userId
        });
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Exception fetching user achievements:', {
        error: error instanceof Error ? error.message : String(error),
        userId
      });
      return [];
    }
  }

  async checkAchievements(userId: string): Promise<void> {
    // Evitar múltiplas execuções simultâneas para o mesmo usuário
    if (this.checkingAchievements.has(userId)) {
      this.log('checkAchievements', `Achievement check already in progress for user ${userId}, skipping...`);
      return;
    }

    this.checkingAchievements.add(userId);
    
    try {
      this.log('checkAchievements', `Starting achievement check for user ${userId}`);
      
      // Get user profile with fresh data
      const { data: profile, error: profileError } = await this.supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError || !profile) {
        this.log('checkAchievements', `Failed to get profile for user ${userId}:`, profileError);
        return;
      }

      this.log('checkAchievements', `Profile loaded - Points: ${profile.points}, Total earned: ${profile.total_points_earned}`);

      // Get all achievements
      const achievements = await this.getAchievements();
      this.log('checkAchievements', `Found ${achievements.length} total achievements`);

      // Get user's current achievements
      const userAchievements = await this.getUserAchievements(userId);
      const earnedAchievementIds = userAchievements.map(ua => ua.achievement_id);
      this.log('checkAchievements', `User has ${earnedAchievementIds.length} earned achievements`);

      let newAchievementsCount = 0;

      // Check each achievement
      for (const achievement of achievements) {
        if (earnedAchievementIds.includes(achievement.id)) continue;

        this.log('checkAchievements', `Evaluating achievement: ${achievement.name} (${achievement.type})`);
        const earned = await this.evaluateAchievement(userId, achievement, profile);
        
        if (earned) {
          this.log('checkAchievements', `🏆 Achievement earned: ${achievement.name}`);
          await this.awardAchievement(userId, achievement.id, achievement.reward_points);
          newAchievementsCount++;
        }
      }

      this.log('checkAchievements', `Achievement check completed for user ${userId}. New achievements: ${newAchievementsCount}`);
    } catch (error) {
      console.error('Error checking achievements:', error);
    } finally {
      this.checkingAchievements.delete(userId);
    }
  }

  private async evaluateAchievement(
    userId: string, 
    achievement: Achievement, 
    profile: Profile
  ): Promise<boolean> {
    const condition = achievement.condition as any;

    switch (achievement.type) {
      case 'points':
        if (condition.total_points) {
          return profile.total_points_earned >= condition.total_points;
        }
        break;

      case 'check_in':
        if (condition.check_ins) {
          const { count } = await this.supabase
            .from('check_ins')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', userId);
          
          return (count || 0) >= condition.check_ins;
        }
        
        if (condition.unique_spots) {
          const { data } = await this.supabase
            .from('check_ins')
            .select('tourist_spot_id')
            .eq('user_id', userId);
          
          const uniqueSpots = new Set(data?.map(ci => ci.tourist_spot_id)).size;
          return uniqueSpots >= condition.unique_spots;
        }
        break;

      case 'rental':
        if (condition.rental_count) {
          // Implementar contagem de aluguéis quando tabela existir
          // Por enquanto retorna false
          return false;
        }
        break;

      case 'streak':
        if (condition.consecutive_days) {
          return await this.checkConsecutiveDays(userId, condition.consecutive_days);
        }
        break;

      case 'explorer':
        if (condition.unique_spots) {
          const { data } = await this.supabase
            .from('check_ins')
            .select('tourist_spot_id')
            .eq('user_id', userId);
          
          const uniqueSpots = new Set(data?.map(ci => ci.tourist_spot_id)).size;
          return uniqueSpots >= condition.unique_spots;
        }
        
        if (condition.all_spots) {
          const { count: totalSpots } = await this.supabase
            .from('tourist_spots')
            .select('*', { count: 'exact', head: true })
            .eq('is_active', true);

          const { data } = await this.supabase
            .from('check_ins')
            .select('tourist_spot_id')
            .eq('user_id', userId);
          
          const uniqueSpots = new Set(data?.map(ci => ci.tourist_spot_id)).size;
          return uniqueSpots >= (totalSpots || 0);
        }
        break;
    }

    return false;
  }

  private async checkConsecutiveDays(userId: string, requiredDays: number): Promise<boolean> {
    const { data } = await this.supabase
      .from('check_ins')
      .select('check_in_time')
      .eq('user_id', userId)
      .order('check_in_time', { ascending: false });

    if (!data || data.length === 0) return false;

    // Group check-ins by date
    const checkInDates = new Set();
    data.forEach(checkIn => {
      const date = new Date(checkIn.check_in_time).toDateString();
      checkInDates.add(date);
    });

    const sortedDates = Array.from(checkInDates).sort((a, b) => 
      new Date(b as string).getTime() - new Date(a as string).getTime()
    );

    // Check for consecutive days
    let consecutiveDays = 1;
    let currentDate = new Date(sortedDates[0] as string);

    for (let i = 1; i < sortedDates.length; i++) {
      const prevDate = new Date(currentDate);
      prevDate.setDate(prevDate.getDate() - 1);
      
      const checkDate = new Date(sortedDates[i] as string);
      
      if (checkDate.toDateString() === prevDate.toDateString()) {
        consecutiveDays++;
        currentDate = checkDate;
      } else {
        break;
      }
    }

    return consecutiveDays >= requiredDays;
  }

  private async awardAchievement(userId: string, achievementId: string, points: number): Promise<void> {
    try {
      this.log('awardAchievement', `Awarding achievement ${achievementId} to user ${userId} with ${points} points`);
      
      // Award achievement first
      const { error: achievementError } = await this.supabase
        .from('user_achievements')
        .insert({
          user_id: userId,
          achievement_id: achievementId,
        });

      if (achievementError) {
        console.error('Error awarding achievement:', achievementError);
        return;
      }

      this.log('awardAchievement', `Achievement ${achievementId} recorded successfully`);

      // Award points diretamente via RPC para evitar loop infinito
      if (points > 0) {
        this.log('awardAchievement', `Adding ${points} points for achievement ${achievementId}`);
        
        // 1) Inserir transação de pontos
        const { error: transactionError } = await this.supabase
          .from('point_transactions')
          .insert({
            user_id: userId,
            points: points,
            type: 'earned',
            source: 'achievement',
            description: `Conquista desbloqueada: ${achievementId}`,
            metadata: JSON.stringify({ achievement_id: achievementId }),
            created_at: new Date().toISOString(),
          });

        if (transactionError) {
          console.error('Error adding achievement points transaction:', transactionError);
        } else {
          // 2) Buscar pontos atuais do perfil
          const { data: profileData, error: profileFetchError } = await this.supabase
            .from('profiles')
            .select('points, total_points_earned')
            .eq('id', userId)
            .single();

          if (profileFetchError || !profileData) {
            console.error('Error fetching profile before achievement update:', profileFetchError);
          } else {
            // 3) Atualizar os pontos no perfil
            const { error: profileUpdateError } = await this.supabase
              .from('profiles')
              .update({
                points: profileData.points + points,
                total_points_earned: profileData.total_points_earned + points,
                updated_at: new Date().toISOString(),
              })
              .eq('id', userId);

            if (profileUpdateError) {
              console.error('Error updating profile after achievement:', profileUpdateError);
            } else {
              this.log(
                'awardAchievement',
                `Points added successfully for achievement ${achievementId}`
              );
            }
          }
        }

      }
    } catch (error) {
      console.error('Error awarding achievement:', error);
    }
  }

  // Check-in management
  async checkInToSpot(
    userId: string, 
    spotId: string, 
    userLat: number, 
    userLng: number
  ): Promise<{ success: boolean; points?: number; error?: string }> {
    try {
      this.log('checkInToSpot', `User ${userId} attempting check-in at spot ${spotId}`);
      
      // Get tourist spot
      const { data: spot, error: spotError } = await this.supabase
        .from('tourist_spots')
        .select('*')
        .eq('id', spotId)
        .eq('is_active', true)
        .single();

      if (spotError || !spot) {
        return { success: false, error: 'Ponto turístico não encontrado' };
      }

      // Check distance
      const distance = this.calculateDistance(
        userLat, userLng, 
        spot.latitude, spot.longitude
      );

      this.log('checkInToSpot', `Distance to spot: ${distance}m (max: ${spot.check_in_radius}m)`);

      if (distance > spot.check_in_radius) {
        // Format distance for better user feedback
        let distanceText = '';
        if (distance > 1000) {
          const km = (distance / 1000).toFixed(1);
          distanceText = `${km} km`;
        } else {
          distanceText = `${Math.round(distance)} metros`;
        }
        
        return { 
          success: false, 
          error: `Você está a ${distanceText} do local. É necessário estar a menos de ${spot.check_in_radius} metros para fazer check-in.` 
        };
      }

      // Check if already checked in today
      const today = new Date().toISOString().split('T')[0];
      const { data: existingCheckIn } = await this.supabase
        .from('check_ins')
        .select('id')
        .eq('user_id', userId)
        .eq('tourist_spot_id', spotId)
        .gte('check_in_time', `${today}T00:00:00.000Z`)
        .lt('check_in_time', `${today}T23:59:59.999Z`)
        .single();

      if (existingCheckIn) {
        return { success: false, error: 'Você já fez check-in neste local hoje' };
      }

      // Create check-in
      const checkInData: CheckInInsert = {
        user_id: userId,
        tourist_spot_id: spotId,
        points_earned: spot.points_reward,
      };

      const { error: checkInError } = await this.supabase
        .from('check_ins')
        .insert(checkInData);

      if (checkInError) {
        return { success: false, error: 'Erro ao fazer check-in' };
      }

      this.log('checkInToSpot', `Check-in successful, adding ${spot.points_reward} points`);

      // Add points
      const pointResult = await this.addPoints(
        userId,
        spot.points_reward,
        'check_in',
        `Check-in em ${spot.name}`,
        { tourist_spot_id: spotId, location: { lat: userLat, lng: userLng } }
      );

      if (!pointResult.success) {
        this.log('checkInToSpot', `Failed to add points: ${pointResult.error}`);
      }

      return { success: true, points: spot.points_reward };
    } catch (error) {
      console.error('Error checking in:', error);
      return { success: false, error: 'Erro inesperado ao fazer check-in' };
    }
  }

  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    // Debug coordinates
    this.log('calculateDistance', `Input coordinates: User(${lat1}, ${lng1}) vs Spot(${lat2}, ${lng2})`);
    
    // Validate coordinates
    if (isNaN(lat1) || isNaN(lng1) || isNaN(lat2) || isNaN(lng2)) {
      this.log('calculateDistance', 'Invalid coordinates detected (NaN values)');
      return Infinity;
    }
    
    if (Math.abs(lat1) > 90 || Math.abs(lat2) > 90) {
      this.log('calculateDistance', 'Invalid latitude values (outside ±90 range)');
      return Infinity;
    }
    
    if (Math.abs(lng1) > 180 || Math.abs(lng2) > 180) {
      this.log('calculateDistance', 'Invalid longitude values (outside ±180 range)');
      return Infinity;
    }

    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lng2 - lng1) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; // Distance in meters
    
    this.log('calculateDistance', `Calculated distance: ${distance}m`);
    return distance;
  }

  // Tourist spots
  async getTouristSpots(): Promise<TouristSpot[]> {
    const { data, error } = await this.supabase
      .from('tourist_spots')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching tourist spots:', error);
      return [];
    }

    return data || [];
  }

  // Point transactions history
  async getPointTransactions(userId: string, limit = 50): Promise<PointTransaction[]> {
    try {
      const { data, error } = await this.supabase
        .from('point_transactions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching point transactions:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          userId,
          limit
        });
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Exception fetching point transactions:', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        limit
      });
      return [];
    }
  }

  // Leaderboard
  async getLeaderboard(limit = 10) {
    const { data, error } = await this.supabase
      .from('profiles')
      .select('id, full_name, points, level, avatar_url')
      .order('points', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching leaderboard:', error);
      return [];
    }

    return data?.map((profile, index) => ({
      ...profile,
      rank: index + 1,
    })) || [];
  }

  // Helper method for logging
  private log(method: string, message: string, data?: any): void {
    if (this.debugMode) {
      //console.log(`[GamificationService.${method}]`, message, data ? data : '');
    }
  }
}

// Export singleton instance
export const gamificationService = new GamificationService();