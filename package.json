{"name": "sx-locadora-mvp", "version": "1.0.0", "description": "Plataforma de mobilidade urbana com sistema de gamificação Ponto X", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-switch": "^1.2.5", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.47.7", "clsx": "^2.1.1", "lucide-react": "^0.460.0", "next": "^15.1.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-imask": "^7.6.1", "react-input-mask": "^2.0.4", "react-input-mask-next": "^3.0.0-alpha.12", "tailwind-merge": "^3.3.1", "zod": "^3.25.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.9", "@types/node": "^22.10.5", "@types/react": "^19.0.4", "@types/react-dom": "^19.0.3", "@types/react-input-mask": "^3.0.6", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-next": "^15.1.4", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["mobility", "rental", "gamification", "nextjs", "supabase", "pwa", "brazil"], "author": "<PERSON><PERSON>", "license": "MIT"}