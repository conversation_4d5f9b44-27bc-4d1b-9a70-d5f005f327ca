@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Global styles */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px; /* Account for fixed header */
}

/* Respect user motion preferences */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
  
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

body {
  font-family: var(--font-inter), 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background-color: #fafafa;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeSpeed;
}

/* Improved focus management */
*:focus-visible {
  outline: 2px solid #19964b;
  outline-offset: 2px;
}

/* Hide focus outline for mouse users */
*:focus:not(:focus-visible) {
  outline: none;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceLight {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-bounce-light {
  animation: bounceLight 0.6s ease-in-out;
}

/* Level colors */
.level-bronze {
  color: #cd7f32;
}

.level-silver {
  color: #c0c0c0;
}

.level-gold {
  color: #ffd700;
}

.level-platinum {
  color: #e5e4e2;
}

.level-diamond {
  color: #b9f2ff;
}

.bg-level-bronze {
  background-color: #cd7f32;
}

.bg-level-silver {
  background-color: #c0c0c0;
}

.bg-level-gold {
  background-color: #ffd700;
}

.bg-level-platinum {
  background-color: #e5e4e2;
}

.bg-level-diamond {
  background-color: #b9f2ff;
}

/* Progress bar animations */
.progress-bar {
  transition: width 0.8s ease-in-out;
}

/* Toast styles */
.toast-enter {
  transform: translateX(100%);
}

.toast-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-in-out;
}

.toast-exit {
  transform: translateX(0);
}

.toast-exit-active {
  transform: translateX(100%);
  transition: transform 300ms ease-in-out;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

/* Button hover effects */
.btn-hover {
  transition: all 0.2s ease-in-out;
}

.btn-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Card hover effects */
.card-hover {
  transition: all 0.2s ease-in-out;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Touch target optimization for mobile */
@media (max-width: 768px) {
  button, 
  a, 
  input, 
  select, 
  textarea,
  [role="button"],
  [tabindex="0"] {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Increase padding for better touch experience */
  .btn-touch {
    padding: 12px 16px;
  }
}

/* High contrast support */
@media (prefers-contrast: high) {
  :root {
    --primary-600: #000000;
    --primary-700: #000000;
  }
  
  .bg-primary-600 {
    background-color: #000000 !important;
  }
  
  .text-primary-600 {
    color: #000000 !important;
  }
}

/* Responsive utilities */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  /* Improve readability on small screens */
  h1 {
    font-size: 2rem !important;
    line-height: 1.2 !important;
  }
  
  h2 {
    font-size: 1.75rem !important;
    line-height: 1.3 !important;
  }
  
  /* Better spacing for mobile */
  .py-20 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --text-primary: #ffffff;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a, a:visited {
    text-decoration: underline;
  }
  
  h1, h2, h3 {
    page-break-after: avoid;
  }
}

/* Performance optimization for animations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Fix for iOS Safari viewport units */
.min-h-screen-ios {
  min-height: 100vh;
  min-height: -webkit-fill-available;
}

/* Loading state improvements */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}