'use client';

import { useMemo, useCallback } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Hook that provides a Supabase client with authentication validation
 * Ensures all operations are performed with valid authentication
 */
export function useAuthenticatedSupabase() {
  const { user, session, initialized } = useAuth();
  const supabase = useMemo(() => createClient(), []);

  const validateAuth = useCallback(() => {
    if (!initialized) {
      throw new Error('Autenticação ainda carregando. Tente novamente.');
    }
    
    if (!user || !session) {
      throw new Error('Usuário não autenticado. Faça login novamente.');
    }

    // Check if session is still valid (not expired)
    if (session.expires_at && new Date().getTime() > session.expires_at * 1000) {
      throw new Error('Sessão expirada. Faça login novamente.');
    }

    return true;
  }, [user, session, initialized]);

  const authenticatedSupabase = useMemo(() => {
    return {
      ...supabase,
      // Override methods that require authentication
      from: (table: string) => {
        // Only validate if initialized to prevent premature validation
        if (initialized) {
          validateAuth(); // Validate before any database operation
        }
        return supabase.from(table);
      }
    };
  }, [supabase, validateAuth, initialized]);

  return {
    supabase: authenticatedSupabase,
    user,
    session,
    initialized,
    isAuthenticated: !!(user && session && initialized),
    validateAuth,
  };
}