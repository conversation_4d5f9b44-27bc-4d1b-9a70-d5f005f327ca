'use client';

import { useState, useEffect } from 'react';
import { AppSetting } from '@/types';
import { createClient } from '@/utils/supabase/client';

interface AppSettingsData {
  whats_app_number?: string;
  points_per_rental?: number;
  points_per_checkin?: number;
  level_multiplier?: number;
  max_daily_checkins?: number;
  rental_cancellation_hours?: number;
  maintenance_mode?: boolean;
  registration_enabled?: boolean;
  notifications_enabled?: boolean;
  email_notifications?: boolean;
  sms_notifications?: boolean;
}

export function useAppSettings() {
  const [settings, setSettings] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('app_settings')
        .select('*');

      if (error) {
        console.error('Error loading settings:', error);
        return;
      }

      const settingsMap: Record<string, any> = {};
      data?.forEach((setting: AppSetting) => {
        try {
          // Parse JSON values
          settingsMap[setting.key] = typeof setting.value === 'string' 
            ? JSON.parse(setting.value as string)
            : setting.value;
        } catch {
          // If parsing fails, use the raw value
          settingsMap[setting.key] = setting.value;
        }
      });

      setSettings(settingsMap);
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSetting = (key: string, defaultValue?: any) => {
    return settings[key] !== undefined ? settings[key] : defaultValue;
  };

  const getWhatsAppNumber = (): string => {
    return getSetting('whats_app_number', process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '558781613421');
  };

  const getWelcomePoints = (): number => {
    return getSetting('welcome_points', 100);
  };

  const getAppName = (): string => {
    return getSetting('app_name', 'Trivvy');
  };

  const getAppDescription = (): string => {
    return getSetting('app_description', 'Plataforma de mobilidade urbana com gamificação');
  };

  const getSupportEmail = (): string => {
    return getSetting('support_email', '<EMAIL>');
  };

  const getTermsUrl = (): string => {
    return getSetting('terms_url', '/termos');
  };

  const getPrivacyUrl = (): string => {
    return getSetting('privacy_url', '/privacidade');
  };

  const getSocialInstagram = (): string => {
    return getSetting('social_instagram', '@sxlocadora');
  };

  const getSocialFacebook = (): string => {
    return getSetting('social_facebook', 'SXLocadora');
  };

  const getRentalBasePoints = (): number => {
    return getSetting('rental_base_points', 10);
  };

  const isAchievementCheckEnabled = (): boolean => {
    return getSetting('achievement_check_enabled', true);
  };

  const isLeaderboardEnabled = (): boolean => {
    return getSetting('leaderboard_enabled', true);
  };

  const getReferralPoints = (): number => {
    return getSetting('referral_points', 200);
  };

  const getMaxDailyPoints = (): number => {
    return getSetting('max_daily_points', 500);
  };

  const getDailyCheckInLimit = (): number => {
    return getSetting('daily_check_in_limit', 5);
  };

  const getMinCheckInInterval = (): number => {
    return getSetting('min_check_in_interval', 3600); // seconds
  };

  const updateSettings = async (updates: AppSettingsData) => {
    try {
      const upsertData = Object.entries(updates).map(([key, value]) => ({
        key,
        value: JSON.stringify(value),
        updated_at: new Date().toISOString()
      }));

      const { error } = await supabase
        .from('app_settings')
        .upsert(upsertData, { 
          onConflict: 'key',
          ignoreDuplicates: false 
        });

      if (error) {
        throw error;
      }

      await loadSettings();
    } catch (error) {
      console.error('Error updating settings:', error);
      throw error;
    }
  };

  return {
    settings: {
      whats_app_number: getSetting('whats_app_number', ''),
      points_per_rental: getSetting('points_per_rental', 100),
      points_per_checkin: getSetting('points_per_checkin', 50),
      level_multiplier: getSetting('level_multiplier', 500),
      max_daily_checkins: getSetting('max_daily_checkins', 5),
      rental_cancellation_hours: getSetting('rental_cancellation_hours', 24),
      maintenance_mode: getSetting('maintenance_mode', false),
      registration_enabled: getSetting('registration_enabled', true),
      notifications_enabled: getSetting('notifications_enabled', true),
      email_notifications: getSetting('email_notifications', true),
      sms_notifications: getSetting('sms_notifications', false),
    },
    loading,
    loadSettings,
    getSetting,
    updateSettings,
    
    // Convenience methods
    getWhatsAppNumber,
    getWelcomePoints,
    getAppName,
    getAppDescription,
    getSupportEmail,
    getTermsUrl,
    getPrivacyUrl,
    getSocialInstagram,
    getSocialFacebook,
    getRentalBasePoints,
    isAchievementCheckEnabled,
    isLeaderboardEnabled,
    getReferralPoints,
    getMaxDailyPoints,
    getDailyCheckInLimit,
    getMinCheckInInterval,
  };
}