'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';

interface Reward {
  id: string;
  name: string;
  description: string;
  cost_points: number;
  category?: string;
  type: 'discount' | 'free_rental' | 'merchandise' | 'experience' | 'partner_benefit';
  value: string;
  image_url?: string;
  is_active: boolean;
  stock?: number;
  valid_until?: string;
  created_at: string;
}

// Hook otimizado para dados públicos de recompensas (com cache)
export function usePublicRewards() {
  const [rewards, setRewards] = useState<Reward[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    loadRewards();
    
    // Recarregar dados a cada 5 minutos para manter sincronização
    const interval = setInterval(loadRewards, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const loadRewards = async () => {
    try {
      setLoading(true);
      setError(null);

      // Carregar apenas recompensas ativas para público
      const { data, error: fetchError } = await supabase
        .from('rewards')
        .select('*')
        .eq('is_active', true)
        .order('cost_points', { ascending: true });

      if (fetchError) {
        throw fetchError;
      }

      setRewards(data || []);
    } catch (err) {
      console.error('Error loading public rewards:', err);
      setError('Erro ao carregar recompensas');
    } finally {
      setLoading(false);
    }
  };

  return {
    rewards,
    loading,
    error,
    refetch: loadRewards,
  };
}
