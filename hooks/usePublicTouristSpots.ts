'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';

interface TouristSpot {
  id: string;
  name: string;
  description: string;
  address: string;
  latitude: number;
  longitude: number;
  points_reward: number;
  check_in_radius: number;
  image_url?: string;
  is_active: boolean;
  created_at: string;
  destaque: boolean;
}

// Hook otimizado para dados públicos de pontos turísticos (com cache)
export function usePublicTouristSpots() {
  const [touristSpots, setTouristSpots] = useState<TouristSpot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    loadTouristSpots();
    
    // Recarregar dados a cada 5 minutos para manter sincronização
    const interval = setInterval(loadTouristSpots, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const loadTouristSpots = async () => {
    try {
      setLoading(true);
      setError(null);

      // Carregar apenas pontos turísticos ativos para público
      const { data, error: fetchError } = await supabase
        .from('tourist_spots')
        .select('*')
        .eq('is_active', true)
        .order('points_reward', { ascending: false });

      if (fetchError) {
        throw fetchError;
      }

      setTouristSpots(data || []);
    } catch (err) {
      console.error('Error loading public tourist spots:', err);
      setError('Erro ao carregar pontos turísticos');
    } finally {
      setLoading(false);
    }
  };

  return {
    touristSpots,
    loading,
    error,
    refetch: loadTouristSpots,
  };
}