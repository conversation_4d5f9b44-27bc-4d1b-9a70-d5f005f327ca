'use client';

import { useState, useEffect } from 'react';
import { Vehicle } from '@/types';
import { createClient } from '@/utils/supabase/client';

// Hook otimizado para dados públicos de veículos (com cache)
export function usePublicVehicles() {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    loadVehicles();
    
    // Recarregar dados a cada 5 minutos para manter sincronização
    const interval = setInterval(loadVehicles, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const loadVehicles = async () => {
    try {
      setLoading(true);
      setError(null);

      // Carregar apenas veículos disponíveis para público
      const { data, error: fetchError } = await supabase
        .from('vehicles')
        .select('*')
        .eq('is_available', true)
        .order('created_at', { ascending: false });

      if (fetchError) {
        throw fetchError;
      }

      setVehicles(data || []);
    } catch (err) {
      console.error('Error loading public vehicles:', err);
      setError('Erro ao carregar veículos');
    } finally {
      setLoading(false);
    }
  };

  return {
    vehicles,
    loading,
    error,
    refetch: loadVehicles,
  };
}