'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';

interface Reward {
  id: string;
  name: string;
  description: string;
  cost_points: number;
  category?: string;
  type: 'discount' | 'free_rental' | 'merchandise' | 'experience' | 'partner_benefit';
  value: string;
  image_url?: string;
  is_active: boolean;
  stock?: number;
  valid_until?: string;
  created_at: string;
}

interface UserReward {
  id: string;
  user_id: string;
  reward_id: string;
  redeemed_at: string;
  points_spent: number;
  status: 'pending' | 'approved' | 'used' | 'expired';
  expires_at: string;
  used_at?: string;
}

export function useRewards() {
  const [rewards, setRewards] = useState<Reward[]>([]);
  const [userRewards, setUserRewards] = useState<UserReward[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await Promise.all([
        fetchRewards(),
        fetchUserRewards()
      ]);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchRewards = async () => {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('rewards')
      .select('*')
      .order('cost_points');

    if (error) throw error;
    setRewards(data || []);
  };

  const fetchUserRewards = async () => {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) return;

    const { data, error } = await supabase
      .from('reward_redemptions')
      .select('*')
      .eq('user_id', user.id)
      .order('redeemed_at', { ascending: false });

    if (error) throw error;
    setUserRewards(data || []);
  };

  const redeemReward = async (rewardId: string) => {
    try {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) throw new Error('Usuário não autenticado');

      const reward = rewards.find(r => r.id === rewardId);
      if (!reward) throw new Error('Recompensa não encontrada');

      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 30); // 30 dias por padrão

      const { data, error } = await supabase
        .from('reward_redemptions')
        .insert([{
          user_id: user.id,
          reward_id: rewardId,
          points_spent: reward.cost_points,
          status: 'pending',
          expires_at: expiresAt.toISOString(),
        }])
        .select()
        .single();

        //consulta pontos atuais do usuário, SOMENTE OS PONTOS E NADA MAIS
        const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('points')
        .eq('id', user.id)
        .single();

        if (!profileData) {
          throw new Error(`Perfil não encontrado para o usuário ${user.id}`);
        }

        const { error: pointsError } = await supabase
          .from("profiles")
          .update({
            points: profileData.points - reward.cost_points,
            updated_at: new Date().toISOString(),
          })
          .eq("id", user.id);

        if (pointsError) throw pointsError;

      if (error) throw error;
      
      setUserRewards([data, ...userRewards]);
      return data;
    } catch (err: any) {
      throw err;
    }
  };

  const createReward = async (reward: Partial<Reward>) => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('rewards')
        .insert([{
          ...reward,
          current_redemptions: 0,
        }])
        .select()
        .single();

      if (error) throw error;
      setRewards([...rewards, data]);
      return data;
    } catch (err: any) {
      throw err;
    }
  };

  const updateReward = async (id: string, updates: Partial<Reward>) => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('rewards')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      setRewards(rewards.map(r => r.id === id ? data : r));
      return data;
    } catch (err: any) {
      throw err;
    }
  };

  const deleteReward = async (id: string) => {
    try {
      const supabase = createClient();
      const { error } = await supabase
        .from('rewards')
        .delete()
        .eq('id', id);

      if (error) throw error;
      setRewards(rewards.filter(r => r.id !== id));
    } catch (err: any) {
      throw err;
    }
  };

  return {
    rewards,
    userRewards,
    loading,
    error,
    redeemReward,
    createReward,
    updateReward,
    deleteReward,
    refetch: loadData,
  };
}