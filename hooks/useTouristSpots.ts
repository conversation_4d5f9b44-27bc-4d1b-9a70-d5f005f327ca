'use client';

import { useState, useEffect } from 'react';
import { gamificationService } from '@/lib/gamification';
import { TouristSpotCategory } from '@/types';
import { useAuthenticatedSupabase } from '@/hooks/useAuthenticatedSupabase';

interface TouristSpot {
  id: string;
  name: string;
  description: string;
  address: string;
  latitude: number;
  longitude: number;
  points_reward: number;
  check_in_radius: number;
  image_url?: string;
  is_active: boolean;
  category: TouristSpotCategory;
  created_at: string;
}

interface CheckIn {
  id: string;
  user_id: string;
  tourist_spot_id: string;
  check_in_time: string;
  points_earned: number;
}

export function useTouristSpots() {
  const [touristSpots, setTouristSpots] = useState<TouristSpot[]>([]);
  const [checkIns, setCheckIns] = useState<CheckIn[]>([]);
  const [loading, setLoading] = useState(true);
  const [checkInLoading, setCheckInLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Use authenticated supabase client
  const { supabase, user, initialized, isAuthenticated } = useAuthenticatedSupabase();

  useEffect(() => {
    // Only load data when auth is initialized
    if (initialized) {
      loadData();
    }
  }, [initialized]);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await Promise.all([
        fetchTouristSpots(),
        fetchCheckIns()
      ]);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchTouristSpots = async () => {
    const { data, error } = await supabase
      .from('tourist_spots')
      .select('*')
      .order('name');

    if (error) throw error;
    setTouristSpots(data || []);
  };

  const fetchCheckIns = async () => {
    // Use user from auth context instead of making a new request
    if (!user) return;

    const { data, error } = await supabase
      .from('check_ins')
      .select('*')
      .eq('user_id', user.id)
      .order('check_in_time', { ascending: false });

    if (error) throw error;
    setCheckIns(data || []);
  };

  const performCheckIn = async (spotId: string) => {
    setCheckInLoading(true);
    
    try {
      // Authentication validation is handled by useAuthenticatedSupabase
      if (!user) {
        throw new Error('Usuário não autenticado. Faça login novamente.');
      }

      const spot = touristSpots.find(s => s.id === spotId);
      if (!spot) throw new Error('Ponto turístico não encontrado');

      // Obter localização do usuário
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        if (!navigator.geolocation) {
          reject(new Error('Geolocalização não é suportada pelo navegador'));
          return;
        }
        
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        });
      });

      const { latitude, longitude } = position.coords;

      // Usar o gamificationService para fazer check-in completo
      const result = await gamificationService.checkInToSpot(
        user.id,
        spotId,
        latitude,
        longitude
      );

      if (!result.success) {
        throw new Error(result.error || 'Erro ao fazer check-in');
      }

      // Recarregar dados para refletir o novo check-in
      await fetchCheckIns();
      
      return result;
    } catch (err: any) {
      if (err.code === 1) { // PERMISSION_DENIED
        throw new Error('Permissão de localização negada. Ative a localização para fazer check-in.');
      } else if (err.code === 2) { // POSITION_UNAVAILABLE
        throw new Error('Localização indisponível. Verifique se o GPS está ativado.');
      } else if (err.code === 3) { // TIMEOUT
        throw new Error('Tempo limite para obter localização excedido.');
      }
      throw err;
    } finally {
      setCheckInLoading(false);
    }
  };

  const createTouristSpot = async (spot: Partial<TouristSpot>) => {
    try {
      // Authentication validation is handled by useAuthenticatedSupabase
      const { data, error } = await supabase
        .from('tourist_spots')
        .insert([spot])
        .select()
        .single();

      if (error) throw error;
      setTouristSpots([...touristSpots, data]);
      return data;
    } catch (err: any) {
      throw err;
    }
  };

  const updateTouristSpot = async (id: string, updates: Partial<TouristSpot>) => {
    try {
      // Authentication validation is handled by useAuthenticatedSupabase
      const { data, error } = await supabase
        .from('tourist_spots')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      setTouristSpots(touristSpots.map(s => s.id === id ? data : s));
      return data;
    } catch (err: any) {
      throw err;
    }
  };

  const deleteTouristSpot = async (id: string) => {
    try {
      // Authentication validation is handled by useAuthenticatedSupabase
      const { error } = await supabase
        .from('tourist_spots')
        .delete()
        .eq('id', id);

      if (error) throw error;
      setTouristSpots(touristSpots.filter(s => s.id !== id));
    } catch (err: any) {
      throw err;
    }
  };

  return {
    touristSpots,
    checkIns,
    loading,
    checkInLoading,
    error,
    performCheckIn,
    createTouristSpot,
    updateTouristSpot,
    deleteTouristSpot,
    refetch: loadData,
  };
}