'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  Level, 
  Achievement, 
  UserAchievement, 
  TouristSpot, 
  PointTransaction,
  LevelProgress,
  Profile,
} from '@/types';
import { gamificationService } from '@/lib/gamification';
import { createClient } from '@/utils/supabase/client';
import { useAuth } from './useAuth';

export function useGamification() {
  const { profile, refreshProfile } = useAuth();
  const [levels, setLevels] = useState<Level[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [userAchievements, setUserAchievements] = useState<UserAchievement[]>([]);
  const [touristSpots, setTouristSpots] = useState<TouristSpot[]>([]);
  const [pointTransactions, setPointTransactions] = useState<PointTransaction[]>([]);
  const [levelProgress, setLevelProgress] = useState<LevelProgress | null>(null);
  const [leaderboard, setLeaderboard] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Force refresh with fresh profile fetch
  const forceRefreshUserData = useCallback(async () => {
    if (!profile) return;
    if (refreshing) {
      console.log('[useGamification.forceRefreshUserData] Already refreshing, skipping...');
      return;
    }

    setRefreshing(true);
    console.log('[useGamification.forceRefreshUserData] Force refreshing with fresh profile...');

    try {
      // Buscar profile atualizado diretamente do banco
      const supabase = createClient();
      const { data: freshProfile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', profile.id)
        .single();

      if (profileError) {
        console.error('[useGamification.forceRefreshUserData] Error fetching fresh profile:', profileError);
        return;
      }

      console.log('[useGamification.forceRefreshUserData] Fresh profile data:', {
        currentPoints: profile.points,
        freshPoints: freshProfile.points,
        currentTotal: profile.total_points_earned,
        freshTotal: freshProfile.total_points_earned
      });

      // Usar o profile atualizado para calcular progress
      const [userAchievementsData, transactionsData, progressData, leaderboardData] = await Promise.all([
        gamificationService.getUserAchievements(profile.id),
        gamificationService.getPointTransactions(profile.id),
        gamificationService.calculateLevelProgress(freshProfile),
        gamificationService.getLeaderboard(),
      ]);

      console.log('[useGamification.forceRefreshUserData] Fresh data loaded:', {
        achievements: userAchievementsData.length,
        transactions: transactionsData.length,
        level: progressData?.currentLevel?.name,
        progress: progressData?.progress
      });

      setUserAchievements(userAchievementsData);
      setPointTransactions(transactionsData);
      setLevelProgress(progressData);
      setLeaderboard(leaderboardData);

      // Forçar refresh do profile no contexto de auth se os pontos mudaram
      if (freshProfile.points !== profile.points || freshProfile.total_points_earned !== profile.total_points_earned) {
        console.log('[useGamification.forceRefreshUserData] Points changed, refreshing auth profile...');
        await refreshProfile();
      }
    } catch (error) {
      console.error('[useGamification.forceRefreshUserData] Error in force refresh:', error);
    } finally {
      setRefreshing(false);
    }
  }, [profile, refreshProfile, refreshing]);

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const [levelsData, achievementsData, spotsData] = await Promise.all([
          gamificationService.getLevels(),
          gamificationService.getAchievements(),
          gamificationService.getTouristSpots(),
        ]);

        setLevels(levelsData);
        setAchievements(achievementsData);
        setTouristSpots(spotsData);

        if (profile) {
          const [userAchievementsData, transactionsData, progressData, leaderboardData] = await Promise.all([
            gamificationService.getUserAchievements(profile.id),
            gamificationService.getPointTransactions(profile.id),
            gamificationService.calculateLevelProgress(profile),
            gamificationService.getLeaderboard(),
          ]);

          setUserAchievements(userAchievementsData);
          setPointTransactions(transactionsData);
          setLevelProgress(progressData);
          setLeaderboard(leaderboardData);
        }
      } catch (error) {
        console.error('Error loading gamification data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [profile]);

  const addPoints = useCallback(async (
    points: number,
    source: PointTransaction['source'],
    description: string,
    metadata?: any
  ) => {
    if (!profile) return { success: false, error: 'Usuário não autenticado' };

    console.log('[useGamification.addPoints] Adding points:', { points, source, description });

    const result = await gamificationService.addPoints(
      profile.id,
      points,
      source,
      description,
      metadata
    );

    if (result.success) {
      console.log('[useGamification.addPoints] Points added successfully, refreshing data...');
      
      // Aguardar um pouco para garantir que as transações foram processadas
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Refresh user data with fresh profile fetch
      await refreshProfile();
      
      // Force refresh all gamification data
      await forceRefreshUserData();
    } else {
      console.error('[useGamification.addPoints] Failed to add points:', result.error);
    }

    return result;
  }, [profile, refreshProfile, forceRefreshUserData]);

  const deductPoints = useCallback(async (
    points: number,
    source: PointTransaction['source'],
    description: string,
    metadata?: any
  ) => {
    if (!profile) return { success: false, error: 'Usuário não autenticado' };

    console.log('[useGamification.deductPoints] Deducting points:', { points, source, description });

    const result = await gamificationService.deductPoints(
      profile.id,
      points,
      source,
      description,
      metadata
    );

    if (result.success) {
      console.log('[useGamification.deductPoints] Points deducted successfully, refreshing data...');
      
      // Aguardar um pouco para garantir que as transações foram processadas
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Refresh user data
      await refreshProfile();
      
      // Force refresh all gamification data
      await forceRefreshUserData();
    } else {
      console.error('[useGamification.deductPoints] Failed to deduct points:', result.error);
    }

    return result;
  }, [profile, refreshProfile, forceRefreshUserData]);

  const checkInToSpot = useCallback(async (
    spotId: string,
    userLat: number,
    userLng: number
  ) => {
    if (!profile) return { success: false, error: 'Usuário não autenticado' };

    console.log('[useGamification.checkInToSpot] Checking in to spot:', spotId);

    const result = await gamificationService.checkInToSpot(
      profile.id,
      spotId,
      userLat,
      userLng
    );

    if (result.success) {
      console.log('[useGamification.checkInToSpot] Check-in successful, refreshing data...');
      
      // Aguardar um pouco para garantir que as transações foram processadas
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Refresh user data
      await refreshProfile();
      
      // Force refresh all gamification data
      await forceRefreshUserData();
    } else {
      console.error('[useGamification.checkInToSpot] Check-in failed:', result.error);
    }

    return result;
  }, [profile, refreshProfile, forceRefreshUserData]);

  const refreshUserData = useCallback(async () => {
    if (!profile) return;

    console.log('[useGamification.refreshUserData] Refreshing user data for:', profile.id);

    try {
      const [userAchievementsData, transactionsData, progressData, leaderboardData] = await Promise.all([
        gamificationService.getUserAchievements(profile.id),
        gamificationService.getPointTransactions(profile.id),
        gamificationService.calculateLevelProgress(profile),
        gamificationService.getLeaderboard(),
      ]);

      console.log('[useGamification.refreshUserData] Data refreshed:', {
        achievements: userAchievementsData.length,
        transactions: transactionsData.length,
        level: progressData?.currentLevel?.name,
        points: profile.points
      });

      setUserAchievements(userAchievementsData);
      setPointTransactions(transactionsData);
      setLevelProgress(progressData);
      setLeaderboard(leaderboardData);
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  }, [profile]);

  const checkAchievements = useCallback(async () => {
    if (!profile) return;

    console.log('[useGamification.checkAchievements] Checking achievements for user:', profile.id);
    
    await gamificationService.checkAchievements(profile.id);
    
    // Aguardar um pouco e fazer refresh completo
    await new Promise(resolve => setTimeout(resolve, 1000));
    await forceRefreshUserData();
  }, [profile, forceRefreshUserData]);

  // Helper functions
  const getLevelColor = useCallback((levelName: string) => {
    const levelColors: Record<string, string> = {
      'Bronze': '#cd7f32',
      'Silver': '#c0c0c0', 
      'Gold': '#ffd700',
      'Platinum': '#e5e4e2',
      'Diamond': '#b9f2ff',
    };
    return levelColors[levelName] || '#6b7280';
  }, []);

  const getAchievementIcon = useCallback((type: Achievement['type']) => {
    const iconMap: Record<Achievement['type'], string> = {
      'check_in': 'map-pin',
      'rental': 'bike',
      'points': 'coins',
      'social': 'users',
      'special': 'star',
      'streak': 'flame',
      'explorer': 'compass',
      'champion': 'trophy',
    };
    return iconMap[type] || 'award';
  }, []);

  const isAchievementEarned = useCallback((achievementId: string) => {
    return userAchievements.some(ua => ua.achievement_id === achievementId);
  }, [userAchievements]);

  const getUserRank = useCallback(() => {
    if (!profile) return null;
    return leaderboard.findIndex(user => user.id === profile.id) + 1 || null;
  }, [profile, leaderboard]);

  return {
    // Data
    levels,
    achievements,
    userAchievements,
    touristSpots,
    pointTransactions,
    levelProgress,
    leaderboard,
    loading,

    // Actions
    addPoints,
    deductPoints,
    checkInToSpot,
    checkAchievements,
    refreshUserData,
    forceRefreshUserData,

    // Helpers
    getLevelColor,
    getAchievementIcon,
    isAchievementEarned,
    getUserRank,
  };
}