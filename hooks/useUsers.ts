'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';

interface User {
  id: string;
  email: string;
  full_name: string | null;
  role: 'user' | 'admin';
  level: string;
  points: number;
  total_points_earned: number;
  phone: string | null;
  is_banned: boolean;
  created_at: string;
}

export function useUsers() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUsers(data || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const updateUserRole = async (userId: string, role: 'user' | 'admin') => {
    try {
      const supabase = createClient();
      const { error } = await supabase
        .from('profiles')
        .update({ role })
        .eq('id', userId);

      if (error) throw error;
      setUsers(users.map(u => u.id === userId ? { ...u, role } : u));
    } catch (err: any) {
      throw err;
    }
  };

  const banUser = async (userId: string) => {
    try {
      const supabase = createClient();
      const { error } = await supabase
        .from('profiles')
        .update({ is_banned: true })
        .eq('id', userId);

      if (error) throw error;
      setUsers(users.map(u => u.id === userId ? { ...u, is_banned: true } : u));
    } catch (err: any) {
      throw err;
    }
  };

  const unbanUser = async (userId: string) => {
    try {
      const supabase = createClient();
      const { error } = await supabase
        .from('profiles')
        .update({ is_banned: false })
        .eq('id', userId);

      if (error) throw error;
      setUsers(users.map(u => u.id === userId ? { ...u, is_banned: false } : u));
    } catch (err: any) {
      throw err;
    }
  };

  return {
    users,
    loading,
    error,
    updateUserRole,
    banUser,
    unbanUser,
    refetch: fetchUsers,
  };
}