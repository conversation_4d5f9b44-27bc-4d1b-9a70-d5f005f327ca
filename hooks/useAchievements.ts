'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';

interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: string;
  condition: any;
  reward_points: number;
  category?: string;
  is_active: boolean;
  created_at: string;
}

interface UserAchievement {
  id: string;
  user_id: string;
  achievement_id: string;
  earned_at: string;
  points_earned: number;
}

export function useAchievements() {
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [userAchievements, setUserAchievements] = useState<UserAchievement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await Promise.all([
        fetchAchievements(),
        fetchUserAchievements()
      ]);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchAchievements = async () => {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('achievements')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    setAchievements(data || []);
  };

  const fetchUserAchievements = async () => {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) return;

    const { data, error } = await supabase
      .from('user_achievements')
      .select('*')
      .eq('user_id', user.id);

    if (error) throw error;
    setUserAchievements(data || []);
  };

  const createAchievement = async (achievement: Partial<Achievement>) => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('achievements')
        .insert([achievement])
        .select()
        .single();

      if (error) throw error;
      setAchievements([data, ...achievements]);
      return data;
    } catch (err: any) {
      throw err;
    }
  };

  const updateAchievement = async (id: string, updates: Partial<Achievement>) => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('achievements')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      setAchievements(achievements.map(a => a.id === id ? data : a));
      return data;
    } catch (err: any) {
      throw err;
    }
  };

  const deleteAchievement = async (id: string) => {
    try {
      const supabase = createClient();
      const { error } = await supabase
        .from('achievements')
        .delete()
        .eq('id', id);

      if (error) throw error;
      setAchievements(achievements.filter(a => a.id !== id));
    } catch (err: any) {
      throw err;
    }
  };

  return {
    achievements,
    userAchievements,
    loading,
    error,
    createAchievement,
    updateAchievement,
    deleteAchievement,
    refetch: loadData,
  };
}