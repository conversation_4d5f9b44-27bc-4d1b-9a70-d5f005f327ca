'use client';

import { useState, useEffect } from 'react';
import { Vehicle, VehicleType } from '@/types';
import { createClient } from '@/utils/supabase/client';

export function useVehicles() {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    loadVehicles();
  }, []);

  const loadVehicles = async () => {
    try {
      setLoading(true);
      setError(null);

      // Para páginas admin, carregar todos os veículos (incluindo indisponíveis)
      const isAdmin = window.location.pathname.startsWith('/admin');
      
      let query = supabase
        .from('vehicles')
        .select('*');

      if (!isAdmin) {
        query = query.eq('is_available', true);
      }

      const { data, error: fetchError } = await query
        .order('created_at', { ascending: false });

      if (fetchError) {
        throw fetchError;
      }

      setVehicles(data || []);
    } catch (err) {
      console.error('Error loading vehicles:', err);
      setError('Erro ao carregar veículos');
    } finally {
      setLoading(false);
    }
  };

  const getVehiclesByType = (type: VehicleType): Vehicle[] => {
    return vehicles.filter(vehicle => vehicle.type === type);
  };

  const getVehicleTypes = (): { type: VehicleType; count: number; label: string }[] => {
    const typeLabels: Record<VehicleType, string> = {
      'scooter': 'Patinetes',
      'bike': 'Bicicletas', 
      'e-bike': 'E-bikes',
      'skateboard': 'Skates',
    };

    const typeCounts: Record<VehicleType, number> = {
      'scooter': 0,
      'bike': 0,
      'e-bike': 0,
      'skateboard': 0,
    };

    vehicles.forEach(vehicle => {
      typeCounts[vehicle.type]++;
    });

    return Object.entries(typeCounts)
      .filter(([, count]) => count > 0)
      .map(([type, count]) => ({
        type: type as VehicleType,
        count,
        label: typeLabels[type as VehicleType],
      }));
  };

  const searchVehicles = (query: string): Vehicle[] => {
    if (!query.trim()) return vehicles;

    const lowerQuery = query.toLowerCase();
    return vehicles.filter(vehicle => 
      vehicle.name.toLowerCase().includes(lowerQuery) ||
      vehicle.description?.toLowerCase().includes(lowerQuery) ||
      vehicle.location?.toLowerCase().includes(lowerQuery)
    );
  };

  const filterVehicles = (filters: {
    type?: VehicleType;
    maxPrice?: number;
    location?: string;
  }): Vehicle[] => {
    return vehicles.filter(vehicle => {
      if (filters.type && vehicle.type !== filters.type) return false;
      if (filters.maxPrice && vehicle.hourly_price > filters.maxPrice) return false;
      if (filters.location && vehicle.location?.toLowerCase() !== filters.location.toLowerCase()) return false;
      return true;
    });
  };

  const getAvailableLocations = (): string[] => {
    const locations = new Set<string>();
    vehicles.forEach(vehicle => {
      if (vehicle.location) {
        locations.add(vehicle.location);
      }
    });
    return Array.from(locations).sort();
  };

  const getPriceRange = (): { min: number; max: number } => {
    if (vehicles.length === 0) return { min: 0, max: 0 };
    
    const prices = vehicles.map(v => v.hourly_price);
    return {
      min: Math.min(...prices),
      max: Math.max(...prices),
    };
  };

  const createVehicle = async (vehicle: Partial<Vehicle>) => {
    try {
      const { data, error } = await supabase
        .from('vehicles')
        .insert([vehicle])
        .select()
        .single();

      if (error) throw error;
      
      // Atualizar a lista local imediatamente
      setVehicles(prevVehicles => [data, ...prevVehicles]);
      
      return data;
    } catch (err: any) {
      console.error('Error creating vehicle:', err);
      throw err;
    }
  };

  const updateVehicle = async (id: string, updates: Partial<Vehicle>) => {
    try {
      const { data, error } = await supabase
        .from('vehicles')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      
      // Atualizar a lista local imediatamente
      setVehicles(prevVehicles => prevVehicles.map(v => v.id === id ? data : v));
      
      return data;
    } catch (err: any) {
      console.error('Error updating vehicle:', err);
      throw err;
    }
  };

  const deleteVehicle = async (id: string) => {
    try {
      const { error } = await supabase
        .from('vehicles')
        .delete()
        .eq('id', id);

      if (error) throw error;
      
      // Atualizar a lista local imediatamente
      setVehicles(prevVehicles => prevVehicles.filter(v => v.id !== id));
    } catch (err: any) {
      console.error('Error deleting vehicle:', err);
      throw err;
    }
  };

  return {
    vehicles,
    loading,
    error,
    loadVehicles,
    getVehiclesByType,
    getVehicleTypes,
    searchVehicles,
    filterVehicles,
    getAvailableLocations,
    getPriceRange,
    createVehicle,
    updateVehicle,
    deleteVehicle,
  };
}