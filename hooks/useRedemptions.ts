'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';

export interface RedemptionWithDetails {
  id: string;
  user_id: string;
  reward_id: string;
  points_spent: number;
  status: 'pending' | 'approved' | 'used' | 'expired';
  created_at: string;
  updated_at: string;
  approved_at?: string;
  approved_by?: string;
  notes?: string;
  redeemed_at: string;

  
  // Dados do usuário
  user_name: string;
  user_email: string;
  
  // Dados da recompensa
  reward_name: string;
  reward_description: string;
  reward_type: string;
}

export function useRedemptions() {
  const [redemptions, setRedemptions] = useState<RedemptionWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    loadRedemptions();
  }, []);

  const loadRedemptions = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('reward_redemptions')
        .select(`
          *,
          profiles!reward_redemptions_user_id_fkey (
            full_name,
            email
          ),
          rewards!reward_redemptions_reward_id_fkey (
            name,
            description,
            type
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      const redemptionsWithDetails: RedemptionWithDetails[] = (data || []).map(item => ({
        id: item.id,
        user_id: item.user_id,
        reward_id: item.reward_id,
        points_spent: item.points_spent,
        status: item.status,
        created_at: item.created_at,
        updated_at: item.updated_at,
        approved_at: item.approved_at,
        approved_by: item.approved_by,
        notes: item.notes,
        
        user_name: item.profiles?.full_name || 'Usuário',
        user_email: item.profiles?.email || '',
        
        reward_name: item.rewards?.name || 'Recompensa',
        reward_description: item.rewards?.description || '',
        reward_type: item.rewards?.type || 'discount',
        redeemed_at: item.redeemed_at?? null,
      }));

      setRedemptions(redemptionsWithDetails);
    } catch (error) {
      console.error('Erro ao carregar resgates:', error);
      setError('Erro ao carregar resgates');
    } finally {
      setLoading(false);
    }
  };

  const approveRedemption = async (redemptionId: string, adminId: string, notes?: string) => {
    try {
      const { error } = await supabase
        .from('reward_redemptions')
        .update({
          status: 'approved',
          approved_at: new Date().toISOString(),
          approved_by: adminId,
          notes: notes || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', redemptionId);

      if (error) {
        throw error;
      }

      await loadRedemptions();
      return { success: true };
    } catch (error) {
      console.error('Erro ao aprovar resgate:', error);
      return { success: false, error: 'Erro ao aprovar resgate' };
    }
  };

  const rejectRedemption = async (redemptionId: string, adminId: string, notes?: string) => {
    try {
      // Para rejeição, vamos marcar como expired e devolver os pontos
      const redemption = redemptions.find(r => r.id === redemptionId);
      if (!redemption) {
        return { success: false, error: 'Resgate não encontrado' };
      }

      // Primeiro, atualizar o status do resgate
      const { error: updateError } = await supabase
        .from('reward_redemptions')
        .update({
          status: 'expired',
          approved_by: adminId,
          notes: notes || 'Resgate rejeitado pelo administrador',
          updated_at: new Date().toISOString()
        })
        .eq('id', redemptionId);

      if (updateError) {
        throw updateError;
      }

      //colsuta pontos do usuario
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('points')
        .eq('id', redemption.user_id)
        .single();

     if (!profileData) {
      throw new Error(`Perfil não encontrado para o usuário ${redemption.user_id}`);
    }

    const { error: pointsError } = await supabase
      .from("profiles")
      .update({
        points: profileData.points + redemption.points_spent,
        updated_at: new Date().toISOString(),
      })
      .eq("id", redemption.user_id);


      // const { error: pointsError } = await supabase.rpc('add_points_to_user', {
      //   user_id: redemption.user_id,
      //   points: redemption.points_spent,
      //   description: `Pontos devolvidos - Resgate rejeitado: ${redemption.reward_name}`
      // });

      if (pointsError) {
        console.error('Erro ao devolver pontos:', pointsError);
        // Não falha a operação, mas registra o erro
      }

      await loadRedemptions();
      return { success: true };
    } catch (error) {
      console.error('Erro ao rejeitar resgate:', error);
      return { success: false, error: 'Erro ao rejeitar resgate' };
    }
  };

  const markAsUsed = async (redemptionId: string, adminId: string, notes?: string) => {
    try {
      const { error } = await supabase
        .from('reward_redemptions')
        .update({
          status: 'used',
          approved_by: adminId,
          notes: notes || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', redemptionId);

      if (error) {
        throw error;
      }

      await loadRedemptions();
      return { success: true };
    } catch (error) {
      console.error('Erro ao marcar como usado:', error);
      return { success: false, error: 'Erro ao marcar como usado' };
    }
  };

  const getStats = () => {
    const pending = redemptions.filter(r => r.status === 'pending').length;
    const approved = redemptions.filter(r => r.status === 'approved').length;
    const used = redemptions.filter(r => r.status === 'used').length;
    const expired = redemptions.filter(r => r.status === 'expired').length;
    const total = redemptions.length;

    const totalPointsUsed = redemptions
      .filter(r => r.status !== 'expired')
      .reduce((sum, r) => sum + (r.points_spent || 0), 0);

    return {
      pending,
      approved,
      used,
      expired,
      total,
      totalPointsUsed
    };
  };

  return {
    redemptions,
    loading,
    error,
    loadRedemptions,
    approveRedemption,
    rejectRedemption,
    markAsUsed,
    getStats,
  };
}